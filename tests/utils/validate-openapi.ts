#!/usr/bin/env tsx

/**
 * OpenAPI Schema Validation
 * Validates the OpenAPI specification file for correctness
 */

import { readFileSync } from 'node:fs';
import { join } from 'node:path';

type ValidationResult = {
  valid: boolean;
  errors: string[];
  warnings: string[];
};

/**
 * Interface for OpenAPI 3.x specification
 */
type OpenAPISpec = {
  openapi: string;
  info?: {
    title?: string;
    version?: string;
    description?: string;
  };
  paths?: Record<string, unknown>;
  components?: {
    schemas?: Record<string, unknown>;
    [key: string]: unknown;
  };
  servers?: Array<{
    url: string;
    description?: string;
  }>;
  jsonSchemaDialect?: string;
  [key: string]: unknown;
};

/**
 * Load and parse the OpenAPI specification file
 */
function loadOpenAPISpec(): OpenAPISpec {
  const specPath = join(process.cwd(), 'public/api/openapi.json');
  const content = readFileSync(specPath, 'utf-8');
  return JSON.parse(content) as OpenAPISpec;
}

/**
 * Validate OpenAPI version field
 */
function validateOpenAPIVersion(spec: OpenAPISpec, errors: string[]): void {
  if (!spec.openapi) {
    errors.push('Missing openapi version field');
  } else if (!spec.openapi.startsWith('3.')) {
    errors.push(`Unsupported OpenAPI version: ${spec.openapi}`);
  }
}

/**
 * Validate info object
 */
function validateInfoObject(
  spec: OpenAPISpec,
  errors: string[],
  warnings: string[]
): void {
  if (spec.info) {
    if (!spec.info.title) {
      errors.push('Missing info.title');
    }
    if (!spec.info.version) {
      errors.push('Missing info.version');
    }
    if (!spec.info.description) {
      warnings.push('Missing info.description');
    }
  } else {
    errors.push('Missing info object');
  }
}

/**
 * Get HTTP methods from a path object
 */
function getHttpMethods(pathObj: unknown): string[] {
  if (typeof pathObj !== 'object' || pathObj === null) {
    return [];
  }

  return Object.keys(pathObj).filter((key) =>
    ['get', 'post', 'put', 'patch', 'delete', 'options', 'head'].includes(
      key.toLowerCase()
    )
  );
}

/**
 * Validate endpoint responses
 */
function validateEndpointResponses(
  path: string,
  pathObj: Record<string, unknown>,
  methods: string[],
  errors: string[]
): void {
  for (const method of methods) {
    const endpoint = pathObj[method] as Record<string, unknown>;
    if (!endpoint.responses) {
      errors.push(`Missing responses for ${method.toUpperCase()} ${path}`);
    }
  }
}

/**
 * Validate paths and endpoints
 */
function validatePaths(
  spec: OpenAPISpec,
  errors: string[],
  warnings: string[]
): void {
  if (spec.paths) {
    const pathCount = Object.keys(spec.paths).length;
    if (pathCount === 0) {
      warnings.push('No API paths defined');
    }

    // Check for common HTTP methods
    let _totalEndpoints = 0;
    for (const [path, pathObj] of Object.entries(spec.paths)) {
      const methods = getHttpMethods(pathObj);
      _totalEndpoints += methods.length;

      if (methods.length > 0) {
        validateEndpointResponses(
          path,
          pathObj as Record<string, unknown>,
          methods,
          errors
        );
      }
    }
  } else {
    errors.push('Missing paths object');
  }
}

/**
 * Validate components object
 */
function validateComponents(spec: OpenAPISpec, warnings: string[]): void {
  if (spec.components) {
    if (spec.components.schemas) {
      const _schemaCount = Object.keys(spec.components.schemas).length;
    }

    if (spec.components.securitySchemes) {
      const _securityCount = Object.keys(
        spec.components.securitySchemes
      ).length;
    }
  } else {
    warnings.push('Missing components object (schemas, etc.)');
  }
}

/**
 * Validate servers configuration
 */
function validateServers(spec: OpenAPISpec, warnings: string[]): void {
  if (!spec.servers || spec.servers.length === 0) {
    warnings.push('No servers defined');
  }
}

/**
 * Validate OpenAPI 3.1 specific features
 */
function validateOpenAPI31Features(
  spec: OpenAPISpec,
  warnings: string[]
): void {
  if (spec.openapi.startsWith('3.1') && !spec.jsonSchemaDialect) {
    warnings.push('Missing jsonSchemaDialect for OpenAPI 3.1');
  }
}

function validateOpenAPISpec(): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  try {
    const spec = loadOpenAPISpec();

    // Run all validation checks
    validateOpenAPIVersion(spec, errors);
    validateInfoObject(spec, errors, warnings);
    validatePaths(spec, errors, warnings);
    validateComponents(spec, warnings);
    validateServers(spec, warnings);
    validateOpenAPI31Features(spec, warnings);

    return {
      valid: errors.length === 0,
      errors,
      warnings,
    };
  } catch (error) {
    return {
      valid: false,
      errors: [`Failed to parse OpenAPI spec: ${error}`],
      warnings: [],
    };
  }
}

function main() {
  const result = validateOpenAPISpec();

  if (!result.valid) {
    // Errors would be logged here in a real implementation
  }

  if (result.warnings.length > 0) {
    // Warnings would be logged here in a real implementation
  }

  // Exit with error code if validation failed
  process.exit(result.valid ? 0 : 1);
}

try {
  main();
} catch (_error) {
  process.exit(1);
}
