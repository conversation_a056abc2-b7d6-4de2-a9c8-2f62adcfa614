/**
 * Apify Service - Centralized Apify API integration
 *
 * Consolidates all Apify API interactions that were previously duplicated
 * across multiple webhook handlers and source endpoints.
 */

export type ApifyRunResponse = {
  id: string;
  actId: string;
  status: string;
  startedAt: string;
  defaultDatasetId?: string;
};

export type ScheduleAction = {
  actorId: string;
  input?: Record<string, unknown>;
};

export type ApifySchedule = {
  id: string;
  name: string;
  actions: ScheduleAction[];
};

export type ApifyRunOptions = {
  input?: Record<string, unknown>;
  timeout?: number;
  memory?: number;
};

export class ApifyService {
  private readonly token: string;
  private readonly baseUrl = 'https://api.apify.com/v2';

  constructor(token?: string) {
    this.token = token || process.env.APIFY_TOKEN || '';
    if (!this.token) {
      throw new Error('Apify token is required');
    }
  }

  /**
   * Fetches all schedules and finds input configuration for a specific actor
   */
  async fetchScheduleInput(actorId: string): Promise<Record<string, unknown>> {
    try {
      const response = await fetch(`${this.baseUrl}/schedules`, {
        headers: this.getHeaders(),
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch schedules: ${response.status}`);
      }

      const schedulesData = await response.json();
      const actorSchedule = schedulesData.data?.items?.find(
        (schedule: ApifySchedule) =>
          schedule.actions?.some(
            (actionItem: ScheduleAction) => actionItem.actorId === actorId
          )
      );

      if (actorSchedule) {
        const action = actorSchedule.actions.find(
          (actionItem: ScheduleAction) => actionItem.actorId === actorId
        );
        return action?.input || {};
      }
    } catch (_error) {}
    return {};
  }

  /**
   * Triggers an actor run with optional input override
   */
  async triggerRun(
    actorId: string,
    options: ApifyRunOptions = {}
  ): Promise<ApifyRunResponse> {
    const { input, timeout, memory } = options;

    // Get schedule input as base, then merge with provided input
    const scheduleInput = await this.fetchScheduleInput(actorId);
    const finalInput = { ...scheduleInput, ...input };

    const body: Record<string, unknown> = finalInput;
    if (timeout) {
      body.timeout = timeout;
    }
    if (memory) {
      body.memory = memory;
    }

    const response = await fetch(`${this.baseUrl}/acts/${actorId}/runs`, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to trigger run: ${response.status} ${errorText}`);
    }

    const runData = await response.json();
    return runData.data;
  }

  /**
   * Gets the status of a specific run
   */
  async getRunStatus(runId: string): Promise<ApifyRunResponse> {
    const response = await fetch(`${this.baseUrl}/actor-runs/${runId}`, {
      headers: this.getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Failed to get run status: ${response.status}`);
    }

    const runData = await response.json();
    return runData.data;
  }

  /**
   * Fetches data from a dataset
   */
  async fetchDataset<T = unknown>(
    datasetId: string,
    options: {
      format?: 'json' | 'csv' | 'xlsx';
      clean?: boolean;
      offset?: number;
      limit?: number;
    } = {}
  ): Promise<T[]> {
    const { format = 'json', clean = true, offset, limit } = options;

    const params = new URLSearchParams({
      format,
      clean: clean.toString(),
    });

    if (offset !== undefined) {
      params.set('offset', offset.toString());
    }
    if (limit !== undefined) {
      params.set('limit', limit.toString());
    }

    const response = await fetch(
      `${this.baseUrl}/datasets/${datasetId}/items?${params}`,
      {
        headers: this.getHeaders(),
      }
    );

    if (!response.ok) {
      throw new Error(`Failed to fetch dataset: ${response.status}`);
    }

    const data = await response.json();

    if (!Array.isArray(data)) {
      throw new Error('Invalid dataset response format');
    }

    return data as T[];
  }

  /**
   * Waits for a run to complete and returns the final dataset
   */
  async waitForRunCompletion<T = unknown>(
    runId: string,
    options: {
      maxWaitTime?: number; // milliseconds
      pollInterval?: number; // milliseconds
    } = {}
  ): Promise<T[]> {
    const { maxWaitTime = 300_000, pollInterval = 5000 } = options; // 5 min max, poll every 5s
    const startTime = Date.now();

    while (Date.now() - startTime < maxWaitTime) {
      const runStatus = await this.getRunStatus(runId);

      if (runStatus.status === 'SUCCEEDED' && runStatus.defaultDatasetId) {
        return this.fetchDataset<T>(runStatus.defaultDatasetId);
      }

      if (runStatus.status === 'FAILED' || runStatus.status === 'ABORTED') {
        throw new Error(`Run ${runId} ${runStatus.status.toLowerCase()}`);
      }

      // Still running, wait and try again
      await new Promise((resolve) => setTimeout(resolve, pollInterval));
    }

    throw new Error(`Run ${runId} did not complete within ${maxWaitTime}ms`);
  }

  /**
   * Triggers a run and waits for completion in one call
   */
  async triggerAndWaitForRun<T = unknown>(
    actorId: string,
    options: ApifyRunOptions & {
      maxWaitTime?: number;
      pollInterval?: number;
    } = {}
  ): Promise<T[]> {
    const { maxWaitTime, pollInterval, ...runOptions } = options;

    const run = await this.triggerRun(actorId, runOptions);
    return this.waitForRunCompletion<T>(run.id, { maxWaitTime, pollInterval });
  }

  /**
   * Lists all actors (for health checks and debugging)
   */
  async listActors(): Promise<any[]> {
    const response = await fetch(`${this.baseUrl}/acts`, {
      headers: this.getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Failed to list actors: ${response.status}`);
    }

    const data = await response.json();
    return data.data?.items || [];
  }

  /**
   * Gets details of a specific actor
   */
  async getActor(actorId: string): Promise<any> {
    const response = await fetch(`${this.baseUrl}/acts/${actorId}`, {
      headers: this.getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Failed to get actor: ${response.status}`);
    }

    const data = await response.json();
    return data.data;
  }

  /**
   * Validates environment and actor configuration
   */
  async validateConfig(actorId: string): Promise<{
    isValid: boolean;
    errors: string[];
    actor?: any;
  }> {
    const errors: string[] = [];

    if (!this.token) {
      errors.push('APIFY_TOKEN not configured');
    }

    if (!actorId) {
      errors.push('Actor ID not provided');
    }

    let actor;
    if (actorId && this.token) {
      try {
        actor = await this.getActor(actorId);
      } catch (error) {
        errors.push(
          `Failed to fetch actor: ${error instanceof Error ? error.message : 'Unknown error'}`
        );
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      actor,
    };
  }

  /**
   * Enhanced schedule input fetch with API key injection for JobData API
   */
  async fetchScheduleInputWithApiKey(
    actorId: string,
    apiKeyEnvVar?: string
  ): Promise<Record<string, unknown>> {
    const scheduleInput = await this.fetchScheduleInput(actorId);

    // Inject API key if not present and environment variable is available
    if (!scheduleInput.api_key && apiKeyEnvVar) {
      const apiKey = process.env[apiKeyEnvVar];
      if (apiKey && apiKey !== 'YOUR_API_KEY') {
        scheduleInput.api_key = apiKey;
      } else if (!apiKey) {
        throw new Error(`${apiKeyEnvVar} not configured`);
      }
    }

    return scheduleInput;
  }

  private getHeaders(): HeadersInit {
    return {
      Authorization: `Bearer ${this.token}`,
      'Content-Type': 'application/json',
    };
  }
}

// Singleton instance for common usage
let apifyServiceInstance: ApifyService | null = null;

export function getApifyService(token?: string): ApifyService {
  if (!apifyServiceInstance || token) {
    apifyServiceInstance = new ApifyService(token);
  }
  return apifyServiceInstance;
}

// Helper functions for backward compatibility
export async function triggerApifyRun(
  actorId: string,
  input?: Record<string, unknown>
): Promise<ApifyRunResponse> {
  return getApifyService().triggerRun(actorId, { input });
}

export async function fetchApifyDataset<T = unknown>(
  datasetId: string
): Promise<T[]> {
  return getApifyService().fetchDataset<T>(datasetId);
}

export async function validateApifyConfig(actorId: string) {
  return getApifyService().validateConfig(actorId);
}
