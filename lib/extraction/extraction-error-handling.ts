import { logger } from '../logger';

/**
 * Extraction error handling utilities
 *
 * Specialized error handling for AI extraction operations,
 * including schema validation errors, debugging information
 * extraction, and comprehensive error logging.
 */

/**
 * Handle and process schema mismatch errors with detailed debugging
 */
// biome-ignore lint/complexity/noExcessiveCognitiveComplexity: Complex error handling with multiple conditional checks is necessary for debugging
export function handleSchemaError(error: Error): void {
  if (!error.message.includes('response did not match schema')) {
    return;
  }

  logger.warn('Schema validation error detected', {
    message: error.message,
    hasErrorCause: 'cause' in error,
  });

  // Try to extract debugging info from error cause
  if ('cause' in error && error.cause && typeof error.cause === 'object') {
    const cause = error.cause as Record<string, unknown>;

    // Log the parsed value if available
    if ('value' in cause || 'parsedValue' in cause) {
      const parsedValue = cause.value || cause.parsedValue;
      logger.debug('Schema error - parsed value available', {
        hasValue: !!parsedValue,
        valueType: typeof parsedValue,
      });
    }

    // Try to parse error response for debugging
    if ('text' in cause && typeof cause.text === 'string') {
      try {
        const parsedResponse = JSON.parse(cause.text);
        logger.debug('Schema error - parsed AI response', {
          responseKeys: Object.keys(parsedResponse),
          responseLength: cause.text.length,
        });
      } catch (_parseError) {
        logger.debug('Schema error - could not parse AI response as JSON', {
          textLength: cause.text.length,
          textPreview: cause.text.substring(0, 100),
        });
      }
    }

    // Log specific field validation errors
    if ('issues' in cause && Array.isArray(cause.issues)) {
      const issues = cause.issues as Array<{
        path?: (string | number)[];
        message?: string;
        code?: string;
        expected?: string;
        received?: string;
      }>;

      logger.warn('Schema validation issues found', {
        issueCount: issues.length,
        issues: issues.map((issue, index) => ({
          index,
          path: issue.path?.join('.') || 'unknown',
          message: issue.message || 'unknown',
          code: issue.code || 'unknown',
          expected: issue.expected || 'unknown',
          received: issue.received || 'unknown',
        })),
      });

      // Log field-specific issues for easier debugging
      for (const [index, issue] of issues.entries()) {
        const fieldPath = issue.path?.join('.') || 'unknown_field';
        logger.debug(`Schema issue ${index + 1} - ${fieldPath}`, {
          message: issue.message,
          expected: issue.expected,
          received: issue.received,
          code: issue.code,
        });
      }
    }
  } else {
    logger.warn('Schema error without detailed cause information', {
      errorName: error.name,
      errorMessage: error.message,
    });
  }
}

/**
 * Enhanced error logging for extraction failures
 */
export function logExtractionError(
  error: unknown,
  context: {
    sourceUrl: string;
    contentLength: number;
    duration: number;
    promptLength: number;
    memoryInfo?: Record<string, string>;
  }
): void {
  const baseErrorInfo = {
    error: error instanceof Error ? error.message : String(error),
    cause: error instanceof Error ? error.cause : undefined,
    stack:
      error instanceof Error
        ? error.stack?.split('\n').slice(0, 10)
        : undefined,
    duration: `${context.duration}ms`,
    inputSize: context.contentLength,
    promptLength: context.promptLength,
    sourceUrl: context.sourceUrl,
    environment: process.env.NODE_ENV,
    nodeVersion: process.version,
    openaiKeyExists: !!process.env.OPENAI_API_KEY,
  };

  // Add memory info if provided
  if (context.memoryInfo) {
    Object.assign(baseErrorInfo, context.memoryInfo);
  }

  logger.critical('AI extraction failed', baseErrorInfo);

  // Handle schema-specific errors
  if (error instanceof Error) {
    handleSchemaError(error);
  }
}

/**
 * Check if an error is retryable based on its characteristics
 */
export function isRetryableError(error: Error): boolean {
  const message = error.message.toLowerCase();

  // Network/timeout errors
  if (
    message.includes('timeout') ||
    message.includes('network') ||
    message.includes('econnreset') ||
    message.includes('etimedout') ||
    message.includes('socket hang up')
  ) {
    return true;
  }

  // Rate limiting
  if (message.includes('rate limit') || message.includes('429')) {
    return true;
  }

  // Temporary service issues
  if (
    message.includes('service unavailable') ||
    message.includes('502') ||
    message.includes('503') ||
    message.includes('504')
  ) {
    return true;
  }

  return false;
}

/**
 * Get a user-friendly error message for different error types
 */
export function getErrorMessage(error: Error): string {
  if (error.message.includes('response did not match schema')) {
    return 'AI response format validation failed - this may be a temporary issue';
  }

  if (error.message.includes('rate limit')) {
    return 'AI service rate limit reached - please try again in a moment';
  }

  if (error.message.includes('timeout')) {
    return 'AI service timeout - please try again';
  }

  if (isRetryableError(error)) {
    return 'Temporary AI service issue - please try again';
  }

  return 'AI extraction failed - please contact support if this persists';
}

/**
 * Extract debug information from error for troubleshooting
 */
export function extractErrorDebugInfo(error: Error): Record<string, unknown> {
  const debugInfo: Record<string, unknown> = {
    name: error.name,
    message: error.message,
    stack: error.stack?.split('\n').slice(0, 5),
    isRetryable: isRetryableError(error),
    userMessage: getErrorMessage(error),
  };

  if ('cause' in error && error.cause) {
    debugInfo.cause = error.cause;
  }

  return debugInfo;
}
