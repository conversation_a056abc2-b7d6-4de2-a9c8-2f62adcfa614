import { NextResponse } from 'next/server';
import { logger } from '@/lib/logger';
import { createClient } from '@/lib/supabase';

type JobForDedup = {
  title: string;
  company: string;
  created_at: string;
};

type DuplicateAnalysis = {
  totalDuplicates: number;
  duplicatesToday: number;
  duplicateRate: number;
  uniquePairsWithDuplicates: number;
  mostCommonDuplicates: Array<{ title: string; count: number }>;
};

function analyzeDuplicates(
  allJobs: JobForDedup[],
  totalJobs: number
): DuplicateAnalysis {
  const titleCompanyMap = new Map<string, number>();
  const titleMap = new Map<string, number>();
  let duplicatesToday = 0;
  const today = new Date().toISOString().split('T')[0];

  // Process all jobs to find duplicates
  for (const job of allJobs) {
    // Track title+company duplicates (more accurate)
    const titleCompanyKey = `${job.title}|||${job.company}`;
    const titleCompanyCount = titleCompanyMap.get(titleCompanyKey) || 0;
    titleCompanyMap.set(titleCompanyKey, titleCompanyCount + 1);

    // Track title-only duplicates for "most common" list
    const titleCount = titleMap.get(job.title) || 0;
    titleMap.set(job.title, titleCount + 1);

    // Count today's duplicates
    if (job.created_at?.startsWith(today) && titleCompanyCount > 0) {
      duplicatesToday++;
    }
  }

  // Calculate total duplicates
  const totalDuplicates = Array.from(titleCompanyMap.values()).reduce(
    (sum, count) => sum + Math.max(0, count - 1),
    0
  );

  // Calculate duplicate rate
  const duplicateRate = totalJobs > 0 ? (totalDuplicates / totalJobs) * 100 : 0;

  // Get most common duplicate titles
  const mostCommonDuplicates = Array.from(titleMap.entries())
    .filter(([_, count]) => count > 1)
    .map(([title, count]) => ({ title, count: count - 1 }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 5);

  const uniquePairsWithDuplicates = Array.from(titleCompanyMap.values()).filter(
    (count) => count > 1
  ).length;

  return {
    totalDuplicates,
    duplicatesToday,
    duplicateRate: Math.round(duplicateRate * 100) / 100,
    uniquePairsWithDuplicates,
    mostCommonDuplicates,
  };
}

export async function GET() {
  const supabase = createClient();

  try {
    // Get all jobs with title and company for duplicate analysis
    const { data: allJobs, error: jobsError } = await supabase
      .from('jobs')
      .select('title, company, created_at')
      .not('title', 'is', null)
      .not('company', 'is', null);

    if (jobsError) {
      throw new Error(`Database query failed: ${jobsError.message}`);
    }

    // Get total job count
    const { count: totalJobs, error: countError } = await supabase
      .from('jobs')
      .select('*', { count: 'exact', head: true });

    if (countError) {
      throw new Error(`Count query failed: ${countError.message}`);
    }

    // Analyze duplicates
    const analysis = analyzeDuplicates(allJobs || [], totalJobs || 0);

    const data = {
      total_duplicates: analysis.totalDuplicates,
      duplicates_today: analysis.duplicatesToday,
      duplicate_rate: analysis.duplicateRate,
      total_jobs: totalJobs || 0,
      unique_pairs_with_duplicates: analysis.uniquePairsWithDuplicates,
      most_common_duplicates: analysis.mostCommonDuplicates,
    };

    return NextResponse.json({
      success: true,
      data,
    });
  } catch (error) {
    logger.error('Failed to fetch deduplication statistics:', error);

    const errorMessage = error instanceof Error ? error.message : String(error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch deduplication statistics',
        details: errorMessage,
      },
      { status: 500 }
    );
  }
}
