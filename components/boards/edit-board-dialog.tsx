'use client';

import { type FormEvent, useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { CAREER_LEVELS } from '@/lib/career-levels';
import { countries } from '@/lib/data/countries';
import { CURRENCY_CODES } from '@/lib/data/currencies';
import { LANGUAGE_CODES } from '@/lib/data/languages';
import { JOB_TYPES } from '@/lib/job-types';
import { REMOTE_REGIONS, WORKPLACE_TYPES } from '@/lib/workplace';

import type { JobBoard } from './columns';

type EditBoardDialogProps = {
  open: boolean;
  onClose: () => void;
  onSubmit: (config: JobBoard) => Promise<void>;
  board: JobBoard;
};

// Filter section component moved outside to avoid nested component definition
const FilterSection = ({
  title,
  items,
  selectedItems,
  field,
  columns = 3,
  helpText,
  setFormData,
}: {
  title: string;
  items: readonly string[] | string[];
  selectedItems: string[];
  field: string;
  columns?: number;
  helpText?: string;
  // biome-ignore lint/suspicious/noExplicitAny: Complex form data type with many optional properties
  setFormData: (updater: (prev: any) => any) => void;
}) => {
  const handleCheckboxChange = (
    fieldName: string,
    item: string,
    checked: boolean
  ) => {
    setFormData((prev) => {
      const currentArray = (prev[fieldName] as string[]) || [];
      return {
        ...prev,
        [fieldName]: checked
          ? [...currentArray, item]
          : currentArray.filter((v) => v !== item),
      };
    });
  };

  return (
    <div className="space-y-2">
      <Label className="font-medium text-sm">{title}</Label>
      <div
        className={`grid grid-cols-${columns} max-h-40 gap-2 overflow-y-auto rounded-md border bg-gray-50 p-3`}
      >
        {items.map((item) => (
          <div className="flex items-center space-x-2" key={item}>
            <Checkbox
              checked={selectedItems.includes(item)}
              id={`${field}-${item}`}
              onCheckedChange={(checked) =>
                handleCheckboxChange(field, item, !!checked)
              }
            />
            <Label
              className="cursor-pointer text-xs"
              htmlFor={`${field}-${item}`}
            >
              {item}
            </Label>
          </div>
        ))}
      </div>
      {helpText && <p className="text-muted-foreground text-xs">{helpText}</p>}
    </div>
  );
};

// Helper function to initialize basic form fields
const initializeBasicFields = (board: JobBoard) => ({
  id: board.id || '',
  name: board.name || '',
  description: board.description || '',
  enabled: board.enabled ?? true,
});

// Helper function to initialize Airtable fields
const initializeAirtableFields = (board: JobBoard) => ({
  airtableBaseId: board.airtable?.baseId || '',
  airtableTableName: board.airtable?.tableName || 'Jobs',
});

// Helper function to initialize posting fields
const initializePostingFields = (board: JobBoard) => ({
  dailyLimit: board.posting?.dailyLimit || 10,
  strategy: (board.posting?.strategy || 'newest_first') as
    | 'newest_first'
    | 'best_match'
    | 'random',
  avoidRepostingDays: board.posting?.avoidRepostingDays || 30,
});

// Helper function to initialize filter fields
const initializeFilterFields = (board: JobBoard) => ({
  types: board.filters?.types || [],
  workplaceTypes: board.filters?.workplaceTypes || [],
  careerLevels: board.filters?.careerLevels || [],
  salaryMin: board.filters?.salaryMin?.toString() || '',
  salaryMax: board.filters?.salaryMax?.toString() || '',
  salaryCurrencies: board.filters?.salaryCurrencies || [],
  includeKeywords: board.filters?.includeKeywords?.join(', ') || '',
  excludeKeywords: board.filters?.excludeKeywords?.join(', ') || '',
  countries: board.filters?.countries || [],
  languages: board.filters?.languages || [],
  remoteRegions: board.filters?.remoteRegions || [],
});

// Helper function to initialize form data from board (now simplified)
const initializeFormData = (board: JobBoard) => {
  return {
    ...initializeBasicFields(board),
    ...initializeAirtableFields(board),
    ...initializePostingFields(board),
    ...initializeFilterFields(board),
  };
};

export function EditBoardDialog({
  open,
  onClose,
  onSubmit,
  board,
}: EditBoardDialogProps) {
  const [formData, setFormData] = useState(() => initializeFormData(board));
  const [loading, setLoading] = useState(false);

  // Helper function to build basic filters
  const buildBasicFilters = () => {
    // biome-ignore lint/suspicious/noExplicitAny: Dynamic filter object with various optional properties
    const filters: any = {};

    if (formData.types.length > 0) {
      filters.types = formData.types;
    }
    if (formData.workplaceTypes.length > 0) {
      filters.workplaceTypes = formData.workplaceTypes;
    }
    if (formData.careerLevels.length > 0) {
      filters.careerLevels = formData.careerLevels;
    }

    return filters;
  };

  // Helper function to build salary filters
  // biome-ignore lint/suspicious/noExplicitAny: Filter object passed from buildBasicFilters
  const buildSalaryFilters = (filters: any) => {
    if (formData.salaryMin) {
      filters.salaryMin = Number(formData.salaryMin);
    }
    if (formData.salaryMax) {
      filters.salaryMax = Number(formData.salaryMax);
    }
    if (formData.salaryCurrencies.length > 0) {
      filters.salaryCurrencies = formData.salaryCurrencies;
    }
  };

  // Helper function to build keyword filters
  // biome-ignore lint/suspicious/noExplicitAny: Filter object passed from buildBasicFilters
  const buildKeywordFilters = (filters: any) => {
    if (formData.includeKeywords.trim()) {
      filters.includeKeywords = formData.includeKeywords
        .split(',')
        .map((k) => k.trim())
        .filter((k) => k);
    }
    if (formData.excludeKeywords.trim()) {
      filters.excludeKeywords = formData.excludeKeywords
        .split(',')
        .map((k) => k.trim())
        .filter((k) => k);
    }
  };

  // Helper function to build location filters
  // biome-ignore lint/suspicious/noExplicitAny: Filter object passed from buildBasicFilters
  const buildLocationFilters = (filters: any) => {
    if (formData.countries.length > 0) {
      filters.countries = formData.countries;
    }
    if (formData.languages.length > 0) {
      filters.languages = formData.languages;
    }
    if (formData.remoteRegions.length > 0) {
      filters.remoteRegions = formData.remoteRegions;
    }
  };

  // Helper function to build filters object (now simplified)
  const buildFilters = () => {
    const filters = buildBasicFilters();
    buildSalaryFilters(filters);
    buildKeywordFilters(filters);
    buildLocationFilters(filters);
    return filters;
  };

  // Helper function to build config object
  const buildEditConfig = (): JobBoard => {
    const filters = buildFilters();

    return {
      id: formData.id,
      name: formData.name,
      description: formData.description,
      enabled: formData.enabled,
      airtable: {
        baseId: formData.airtableBaseId,
        tableName: formData.airtableTableName,
      },
      posting: {
        dailyLimit: formData.dailyLimit,
        strategy: formData.strategy,
        avoidRepostingDays: formData.avoidRepostingDays,
      },
      filters,
      patStatus: board.patStatus, // Preserve the original PAT status
      lastPostedAt: board.lastPostedAt,
      totalPosted: board.totalPosted,
    };
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const config = buildEditConfig();
      await onSubmit(config);
      onClose();
    } catch (_error) {
      // Silent error handling - errors are handled by parent component
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog onOpenChange={onClose} open={open}>
      <DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Job Board</DialogTitle>
          <DialogDescription>
            Update the configuration for your automated job posting board.
          </DialogDescription>
        </DialogHeader>

        <form className="space-y-6" onSubmit={handleSubmit}>
          <Tabs className="w-full" defaultValue="basic">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="basic">Basic Info</TabsTrigger>
              <TabsTrigger value="posting">Posting Settings</TabsTrigger>
              <TabsTrigger value="filters">Job Filters</TabsTrigger>
            </TabsList>

            <TabsContent className="space-y-4" value="basic">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="id">Job Board ID</Label>
                  <Input
                    className="bg-muted"
                    disabled
                    id="id"
                    value={formData.id}
                  />
                  <p className="text-muted-foreground text-xs">
                    ID cannot be changed after creation
                  </p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="name">Name *</Label>
                  <Input
                    id="name"
                    onChange={(e) =>
                      setFormData((prev) => ({ ...prev, name: e.target.value }))
                    }
                    placeholder="e.g., Remote React Jobs"
                    required
                    value={formData.name}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      description: e.target.value,
                    }))
                  }
                  placeholder="Brief description of this job board"
                  rows={2}
                  value={formData.description}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="airtableBaseId">Airtable Base ID *</Label>
                  <Input
                    id="airtableBaseId"
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        airtableBaseId: e.target.value,
                      }))
                    }
                    placeholder="app..."
                    required
                    value={formData.airtableBaseId}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="airtableTableName">Table Name</Label>
                  <Input
                    id="airtableTableName"
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        airtableTableName: e.target.value,
                      }))
                    }
                    placeholder="Jobs"
                    value={formData.airtableTableName}
                  />
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  checked={formData.enabled}
                  id="enabled"
                  onCheckedChange={(checked) =>
                    setFormData((prev) => ({ ...prev, enabled: !!checked }))
                  }
                />
                <Label htmlFor="enabled">Enable this job board</Label>
              </div>
            </TabsContent>

            <TabsContent className="space-y-4" value="posting">
              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="dailyLimit">Daily Limit</Label>
                  <Input
                    id="dailyLimit"
                    max="50"
                    min="1"
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        dailyLimit: Number(e.target.value),
                      }))
                    }
                    type="number"
                    value={formData.dailyLimit}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="strategy">Strategy</Label>
                  <Select
                    // biome-ignore lint/suspicious/noExplicitAny: Select component value type
                    onValueChange={(value: any) =>
                      setFormData((prev) => ({ ...prev, strategy: value }))
                    }
                    value={formData.strategy}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="newest_first">Newest First</SelectItem>
                      <SelectItem value="best_match">Best Match</SelectItem>
                      <SelectItem value="random">Random</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="avoidRepostingDays">
                    Avoid Reposting (days)
                  </Label>
                  <Input
                    id="avoidRepostingDays"
                    max="365"
                    min="1"
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        avoidRepostingDays: Number(e.target.value),
                      }))
                    }
                    type="number"
                    value={formData.avoidRepostingDays}
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent className="space-y-6" value="filters">
              <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
                <div className="space-y-4">
                  <FilterSection
                    field="types"
                    helpText="Employment types (Full-time, Part-time, Contract, etc.)"
                    items={JOB_TYPES}
                    selectedItems={formData.types}
                    setFormData={setFormData}
                    title="Job Types"
                  />

                  <FilterSection
                    field="workplaceTypes"
                    helpText="Work arrangements (On-site, Remote, Hybrid)"
                    items={WORKPLACE_TYPES.filter(
                      (type) => type !== 'Not specified'
                    )}
                    selectedItems={formData.workplaceTypes}
                    setFormData={setFormData}
                    title="Workplace Types"
                  />

                  <FilterSection
                    columns={2}
                    field="remoteRegions"
                    helpText="Geographic restrictions for remote work"
                    items={REMOTE_REGIONS}
                    selectedItems={formData.remoteRegions}
                    setFormData={setFormData}
                    title="Remote Regions"
                  />

                  <FilterSection
                    field="careerLevels"
                    helpText="Experience levels (Internship to C-Level)"
                    items={CAREER_LEVELS.filter(
                      (level) => level !== 'Not Specified'
                    )}
                    selectedItems={formData.careerLevels}
                    setFormData={setFormData}
                    title="Career Levels"
                  />
                </div>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>Salary Range</Label>
                    <div className="grid grid-cols-2 gap-2">
                      <Input
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            salaryMin: e.target.value,
                          }))
                        }
                        placeholder="Min salary"
                        type="number"
                        value={formData.salaryMin}
                      />
                      <Input
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            salaryMax: e.target.value,
                          }))
                        }
                        placeholder="Max salary"
                        type="number"
                        value={formData.salaryMax}
                      />
                    </div>
                  </div>

                  <FilterSection
                    columns={4}
                    field="salaryCurrencies"
                    helpText="Including fiat and crypto (USD, EUR, BTC, ETH, etc.)"
                    items={CURRENCY_CODES}
                    selectedItems={formData.salaryCurrencies}
                    setFormData={setFormData}
                    title="Accepted Currencies"
                  />

                  <div className="space-y-2">
                    <Label htmlFor="includeKeywords">Include Keywords</Label>
                    <Input
                      id="includeKeywords"
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          includeKeywords: e.target.value,
                        }))
                      }
                      placeholder="React, TypeScript, Node.js (comma-separated)"
                      value={formData.includeKeywords}
                    />
                    <p className="text-muted-foreground text-xs">
                      Jobs must contain at least one of these keywords
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="excludeKeywords">Exclude Keywords</Label>
                    <Input
                      id="excludeKeywords"
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          excludeKeywords: e.target.value,
                        }))
                      }
                      placeholder="PHP, WordPress (comma-separated)"
                      value={formData.excludeKeywords}
                    />
                    <p className="text-muted-foreground text-xs">
                      Jobs with these keywords will be filtered out
                    </p>
                  </div>

                  <FilterSection
                    columns={2}
                    field="countries"
                    helpText="Geographic targeting"
                    items={countries}
                    selectedItems={formData.countries}
                    setFormData={setFormData}
                    title="Target Countries"
                  />

                  <FilterSection
                    columns={6}
                    field="languages"
                    helpText="ISO 639-1 language codes (en, es, de, etc.)"
                    items={LANGUAGE_CODES}
                    selectedItems={formData.languages}
                    setFormData={setFormData}
                    title="Required Languages"
                  />
                </div>
              </div>
            </TabsContent>
          </Tabs>

          <div className="flex justify-end gap-3">
            <Button onClick={onClose} type="button" variant="outline">
              Cancel
            </Button>
            <Button disabled={loading} type="submit">
              {loading ? 'Updating...' : 'Update Job Board'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
