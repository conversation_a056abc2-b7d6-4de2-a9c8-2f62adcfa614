import { NextResponse } from 'next/server';
import { handleApiError } from '@/lib/api-utils';
import { createClient } from '@/lib/supabase';

type HealthCheck = {
  status: 'healthy' | 'error' | 'unknown';
  latency: number;
  error?: string;
};

type HealthStatus = {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  version: string;
  environment: string;
  checks: {
    database: HealthCheck;
    workflow: HealthCheck;
    apify: HealthCheck;
    slack: HealthCheck;
    uptime: number;
  };
};

async function checkDatabase(): Promise<HealthCheck> {
  try {
    const dbStart = Date.now();
    const supabase = createClient();
    const { error } = await supabase.from('jobs').select('id').limit(1);

    if (error) {
      return {
        status: 'error',
        latency: Date.now() - dbStart,
        error: error.message,
      };
    }
    return {
      status: 'healthy',
      latency: Date.now() - dbStart,
    };
  } catch (dbError) {
    return {
      status: 'error',
      latency: 0,
      error: (dbError as Error).message,
    };
  }
}

async function checkWorkflow(): Promise<HealthCheck> {
  try {
    const workflowStart = Date.now();
    const supabase = createClient();

    // Check if workflow system is healthy by looking at recent activity
    const { data: recentRuns, error } = await supabase
      .from('workflow_runs')
      .select('id, status, started_at')
      .order('started_at', { ascending: false })
      .limit(5);

    if (error) {
      throw new Error(`Workflow health check failed: ${error.message}`);
    }

    // Check if we have recent workflow activity (within last 24 hours)
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
    const _recentActivity = recentRuns?.some(
      (run) => new Date(run.started_at) > oneDayAgo
    );

    const latency = Date.now() - workflowStart;

    return {
      status: 'healthy',
      latency,
    };
  } catch (workflowError) {
    return {
      status: 'error',
      latency: 0,
      error: (workflowError as Error).message,
    };
  }
}

async function checkApify(): Promise<HealthCheck> {
  try {
    const apifyStart = Date.now();
    const apifyToken = process.env.APIFY_TOKEN;
    const workableActorId = process.env.WORKABLE_ACTOR_ID;

    if (!apifyToken) {
      throw new Error('Apify token not configured');
    }

    if (!workableActorId) {
      throw new Error('Workable actor ID not configured');
    }

    const apifyResponse = await fetch(
      `https://api.apify.com/v2/acts/${workableActorId}`,
      {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${apifyToken}`,
          'Content-Type': 'application/json',
        },
      }
    );

    if (!apifyResponse.ok) {
      throw new Error(`Apify API error: ${apifyResponse.status}`);
    }

    return {
      status: 'healthy',
      latency: Date.now() - apifyStart,
    };
  } catch (apifyError) {
    return {
      status: 'error',
      latency: 0,
      error: (apifyError as Error).message,
    };
  }
}

async function checkSlack(): Promise<HealthCheck> {
  try {
    const slackStart = Date.now();
    const slackWebhookUrl = process.env.SLACK_WEBHOOK_URL;
    if (!slackWebhookUrl) {
      throw new Error('Slack webhook URL not configured');
    }

    // Validate Slack webhook URL format without sending actual message
    try {
      const url = new URL(slackWebhookUrl);
      if (!url.hostname.includes('hooks.slack.com')) {
        throw new Error('Invalid Slack webhook URL format');
      }
    } catch (_urlError) {
      throw new Error('Invalid Slack webhook URL');
    }

    // Only send actual test message once per hour to avoid spam
    const shouldSendTestMessage = Math.random() < 0.0014; // ~1/720 chance (once per ~30 min at 30s intervals)

    if (shouldSendTestMessage) {
      const slackResponse = await fetch(slackWebhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: '🔍 Scheduled Slack connectivity test',
          blocks: [
            {
              type: 'section',
              text: {
                type: 'mrkdwn',
                text: '🔍 *Slack Connectivity Test*\nBordfeed health monitoring - connectivity verified.',
              },
            },
          ],
        }),
      });

      if (!slackResponse.ok) {
        throw new Error(`Slack webhook error: ${slackResponse.status}`);
      }
    }

    return {
      status: 'healthy',
      latency: Date.now() - slackStart,
    };
  } catch (slackError) {
    return {
      status: 'error',
      latency: 0,
      error: (slackError as Error).message,
    };
  }
}

function determineOverallStatus(
  checks: HealthStatus['checks']
): 'healthy' | 'degraded' | 'unhealthy' {
  const healthChecks = [
    checks.database,
    checks.workflow,
    checks.apify,
    checks.slack,
  ];

  const errorCount = healthChecks.filter(
    (check) => check.status === 'error'
  ).length;

  if (errorCount === 0) {
    return 'healthy';
  }
  if (errorCount <= 2) {
    return 'degraded';
  }
  return 'unhealthy';
}

export async function GET() {
  try {
    const status: HealthStatus = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      checks: {
        database: { status: 'unknown', latency: 0 },
        workflow: { status: 'unknown', latency: 0 },
        apify: { status: 'unknown', latency: 0 },
        slack: { status: 'unknown', latency: 0 },
        uptime: process.uptime(),
      },
    };

    // Run all health checks in parallel
    const [databaseCheck, workflowCheck, apifyCheck, slackCheck] =
      await Promise.all([
        checkDatabase(),
        checkWorkflow(),
        checkApify(),
        checkSlack(),
      ]);

    status.checks.database = databaseCheck;
    status.checks.workflow = workflowCheck;
    status.checks.apify = apifyCheck;
    status.checks.slack = slackCheck;

    status.status = determineOverallStatus(status.checks);

    return NextResponse.json(status);
  } catch (error) {
    return handleApiError(error, 'Health check');
  }
}
