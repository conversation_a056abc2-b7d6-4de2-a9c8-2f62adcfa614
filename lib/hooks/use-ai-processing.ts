import { useCallback, useRef, useState } from 'react';
import { logger } from '../utils';

type ProcessingJob = {
  id: string;
  title?: string;
  company?: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  progress: number;
  error?: string;
  result?: Record<string, unknown>;
  startTime?: number;
  endTime?: number;
};

type ProcessingBatch = {
  id: string;
  totalJobs: number;
  completedJobs: number;
  failedJobs: number;
  status: 'idle' | 'running' | 'completed' | 'error';
  estimatedCost: number;
  actualCost: number;
  startTime?: number;
  endTime?: number;
  jobs: ProcessingJob[];
  estimatedTimeRemaining?: string;
};

type AIProcessingState = {
  currentBatch: ProcessingBatch | null;
  isProcessing: boolean;
  globalProgress: number;
  estimatedTimeRemaining: string | null;
};

type AIProcessingActions = {
  startProcessing: (
    jobIds: string[],
    jobData?: Array<{ id: string; title?: string; company?: string }>
  ) => Promise<void>;
  cancelProcessing: () => void;
  retryFailedJobs: () => Promise<void>;
  estimateCost: (jobCount: number) => number;
  clearBatch: () => void;
};

const AI_PROCESSING_COST_PER_JOB = 0.001; // Estimated $0.001 per job
const AVERAGE_PROCESSING_TIME_MS = 10_000; // 10 seconds per job average

export const useAIProcessing = (): [AIProcessingState, AIProcessingActions] => {
  const [state, setState] = useState<AIProcessingState>({
    currentBatch: null,
    isProcessing: false,
    globalProgress: 0,
    estimatedTimeRemaining: null,
  });

  const abortControllerRef = useRef<AbortController | null>(null);
  const processingIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Estimate processing cost
  const estimateCost = useCallback((jobCount: number): number => {
    // Static cost estimate for UI demo purposes
    // Production systems would call a pricing API for real-time costs
    return jobCount * AI_PROCESSING_COST_PER_JOB;
  }, []);

  // Calculate estimated time remaining
  const calculateETA = useCallback(
    (
      completedJobs: number,
      totalJobs: number,
      startTime: number
    ): string | null => {
      if (completedJobs === 0 || totalJobs === 0) {
        return null;
      }

      const elapsedTime = Date.now() - startTime;
      const avgTimePerJob = elapsedTime / completedJobs;
      const remainingJobs = totalJobs - completedJobs;
      const estimatedRemainingMs = remainingJobs * avgTimePerJob;

      const seconds = Math.ceil(estimatedRemainingMs / 1000);
      if (seconds < 60) {
        return `${seconds}s`;
      }

      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      if (minutes < 60) {
        return remainingSeconds > 0
          ? `${minutes}m ${remainingSeconds}s`
          : `${minutes}m`;
      }

      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;
      return remainingMinutes > 0
        ? `${hours}h ${remainingMinutes}m`
        : `${hours}h`;
    },
    []
  );

  // Process a single job
  const processJob = useCallback(
    async (job: ProcessingJob, signal: AbortSignal): Promise<ProcessingJob> => {
      try {
        // Update job status to processing
        setState((prev) => ({
          ...prev,
          currentBatch: prev.currentBatch
            ? {
                ...prev.currentBatch,
                jobs: prev.currentBatch.jobs.map((j) =>
                  j.id === job.id
                    ? {
                        ...j,
                        status: 'processing',
                        progress: 0,
                        startTime: Date.now(),
                      }
                    : j
                ),
              }
            : null,
        }));

        // Simulate processing progress (in real implementation, this would come from the API)
        const progressInterval = setInterval(() => {
          setState((prev) => ({
            ...prev,
            currentBatch: prev.currentBatch
              ? {
                  ...prev.currentBatch,
                  jobs: prev.currentBatch.jobs.map((j) => {
                    if (j.id === job.id && j.status === 'processing') {
                      return { ...j, progress: Math.min(90, j.progress + 10) };
                    }
                    return j;
                  }),
                }
              : null,
          }));
        }, 1000);

        // Simulate API call for demo purposes
        // Production code would call: POST /api/extract with job data
        await new Promise((resolve, reject) => {
          const timeout = setTimeout(resolve, AVERAGE_PROCESSING_TIME_MS);
          signal.addEventListener('abort', () => {
            clearTimeout(timeout);
            reject(new Error('Processing cancelled'));
          });
        });

        clearInterval(progressInterval);

        if (signal.aborted) {
          throw new Error('Processing cancelled');
        }

        // Simulate successful result
        const result = {
          title: job.title || `Processed Job ${job.id}`,
          company: job.company || 'Unknown Company',
          extractedData: {},
          metadata: {
            cost: AI_PROCESSING_COST_PER_JOB,
            duration: AVERAGE_PROCESSING_TIME_MS,
            model: 'gpt-4o-mini',
          },
        };

        return {
          ...job,
          status: 'completed',
          progress: 100,
          result,
          endTime: Date.now(),
        };
      } catch (error) {
        return {
          ...job,
          status: 'error',
          progress: 100,
          error: error instanceof Error ? error.message : 'Processing failed',
          endTime: Date.now(),
        };
      }
    },
    []
  );

  // Start batch processing
  const startProcessing = useCallback(
    async (
      jobIds: string[],
      jobData?: Array<{ id: string; title?: string; company?: string }>
    ) => {
      if (state.isProcessing) {
        logger.info('Processing already in progress');
        return;
      }

      const estimatedCost = estimateCost(jobIds.length);

      // Create new batch
      const batchId = `batch-${Date.now()}`;
      const jobs: ProcessingJob[] = jobIds.map((id) => {
        const data = jobData?.find((j) => j.id === id);
        return {
          id,
          title: data?.title,
          company: data?.company,
          status: 'pending',
          progress: 0,
        };
      });

      const batch: ProcessingBatch = {
        id: batchId,
        jobs,
        totalJobs: jobs.length,
        completedJobs: 0,
        failedJobs: 0,
        status: 'running',
        estimatedCost,
        actualCost: 0,
        startTime: Date.now(),
      };

      setState((prev) => ({
        ...prev,
        currentBatch: batch,
        isProcessing: true,
        globalProgress: 0,
      }));

      // Create abort controller for cancellation
      const abortController = new AbortController();
      abortControllerRef.current = abortController;

      try {
        // Process jobs sequentially to avoid overwhelming the API
        const _index = 0;
        for (const job of jobs) {
          if (abortController.signal.aborted) {
            break;
          }

          // biome-ignore lint/nursery/noAwaitInLoop: Sequential processing is intentional to respect rate limits
          const processedJob = await processJob(job, abortController.signal);

          setState((prev) => {
            if (!prev.currentBatch) {
              return prev;
            }

            const updatedJobs = prev.currentBatch.jobs.map((j) =>
              j.id === processedJob.id ? processedJob : j
            );

            const completedJobs = updatedJobs.filter(
              (j) => j.status === 'completed'
            ).length;
            const failedJobs = updatedJobs.filter(
              (j) => j.status === 'error'
            ).length;
            const globalProgress =
              ((completedJobs + failedJobs) / prev.currentBatch.totalJobs) *
              100;

            const estimatedTimeRemaining = prev.currentBatch.startTime
              ? calculateETA(
                  completedJobs + failedJobs,
                  prev.currentBatch.totalJobs,
                  prev.currentBatch.startTime
                )
              : undefined;

            return {
              ...prev,
              currentBatch: {
                ...prev.currentBatch,
                jobs: updatedJobs,
                completedJobs,
                failedJobs,
                actualCost:
                  prev.currentBatch.actualCost +
                  ((processedJob.result?.metadata as { cost?: number })?.cost ??
                    0),
              },
              globalProgress,
              estimatedTimeRemaining: estimatedTimeRemaining || null,
            };
          });
        }

        // Mark batch as completed
        setState((prev) => ({
          ...prev,
          currentBatch: prev.currentBatch
            ? {
                ...prev.currentBatch,
                status:
                  prev.currentBatch.failedJobs > 0 ? 'error' : 'completed',
                endTime: Date.now(),
              }
            : null,
          isProcessing: false,
          estimatedTimeRemaining: null,
        }));
      } catch (error) {
        logger.error('Batch processing failed:', error);
        setState((prev) => ({
          ...prev,
          currentBatch: prev.currentBatch
            ? {
                ...prev.currentBatch,
                status: 'error',
                endTime: Date.now(),
              }
            : null,
          isProcessing: false,
          estimatedTimeRemaining: null,
        }));
      }
    },
    [state.isProcessing, estimateCost, processJob, calculateETA]
  );

  // Cancel processing
  const cancelProcessing = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }

    if (processingIntervalRef.current) {
      clearInterval(processingIntervalRef.current);
      processingIntervalRef.current = null;
    }

    setState((prev) => ({
      ...prev,
      currentBatch: prev.currentBatch
        ? {
            ...prev.currentBatch,
            status: 'error',
            endTime: Date.now(),
          }
        : null,
      isProcessing: false,
      estimatedTimeRemaining: null,
    }));
  }, []);

  // Retry failed jobs
  const retryFailedJobs = useCallback(async () => {
    if (!state.currentBatch || state.isProcessing) {
      return;
    }

    const failedJobIds = state.currentBatch.jobs
      .filter((job) => job.status === 'error')
      .map((job) => job.id);

    if (failedJobIds.length === 0) {
      return;
    }

    const failedJobData = state.currentBatch.jobs
      .filter((job) => job.status === 'error')
      .map((job) => ({
        id: job.id,
        title: job.title,
        company: job.company,
      }));

    await startProcessing(failedJobIds, failedJobData);
  }, [state.currentBatch, state.isProcessing, startProcessing]);

  // Clear batch
  const clearBatch = useCallback(() => {
    setState((prev) => ({
      ...prev,
      currentBatch: null,
      globalProgress: 0,
      estimatedTimeRemaining: null,
    }));
  }, []);

  return [
    state,
    {
      startProcessing,
      cancelProcessing,
      retryFailedJobs,
      estimateCost,
      clearBatch,
    },
  ];
};
