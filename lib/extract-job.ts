import { logger } from './logger';
import type { ExtractJobInput } from './extraction/ai-extraction';
import { performAIExtraction, getAIConfig } from './extraction/ai-extraction';
import { 
  createExtractionMetadata, 
  createFailureMetadata,
  createMemoryDebugInfo 
} from './extraction/extraction-metadata';
import { 
  logExtractionError, 
  handleSchemaError 
} from './extraction/extraction-error-handling';
import { 
  applyJobPostProcessing,
  validateProcessedJob,
  type ExtractedJobData 
} from './extraction/job-post-processing';

/**
 * Main job extraction orchestrator
 * 
 * Coordinates the entire extraction process by delegating to
 * specialized modules for each aspect of the operation.
 */
export async function extractJob(post: ExtractJobInput) {
  const startTime = Date.now();
  const startMemory = process.memoryUsage();

  // Log initial state for debugging
  const aiConfig = getAIConfig();
  logger.debug('Starting AI extraction', {
    contentLength: post.content.length,
    sourceUrl: post.sourceUrl,
    ...aiConfig,
    memoryStart: createMemoryDebugInfo(startMemory),
  });

  try {
    // Perform the core AI extraction
    const result = await performAIExtraction(post);
    
    const endMemory = process.memoryUsage();
    const duration = Date.now() - startTime;

    logger.debug('AI extraction successful', {
      duration: `${duration}ms`,
      inputTokens: result.usage?.inputTokens || 0,
      outputTokens: result.usage?.outputTokens || 0,
      totalTokens: result.usage?.totalTokens || 0,
      finishReason: result.finishReason,
      memoryUsed: `${
        (endMemory.heapUsed - startMemory.heapUsed) / 1024 / 1024
      }MB`,
      memoryEnd: createMemoryDebugInfo(endMemory),
    });

    // Apply post-processing to the extracted job data
    const extractedJob = result.object as ExtractedJobData;
    applyJobPostProcessing(extractedJob, post.sourceUrl);

    // Validate the processed job data
    const validation = validateProcessedJob(extractedJob);
    if (!validation.isValid) {
      logger.warn('Job validation failed after post-processing', {
        missingFields: validation.missingFields,
        warnings: validation.warnings,
      });
    } else if (validation.warnings.length > 0) {
      logger.info('Job validation completed with warnings', {
        warnings: validation.warnings,
      });
    }

    // Create comprehensive metadata
    const metadata = createExtractionMetadata(
      startTime,
      result.usage,
      result,
      post,
      result.promptUsed,
      {
        startMemory,
        endMemory,
      }
    );

    return {
      job: extractedJob,
      metadata,
      validation,
      aiSdkResult: result, // Include the full AI SDK result for future extensibility
    };
  } catch (error) {
    const endMemory = process.memoryUsage();
    const duration = Date.now() - startTime;

    // Use specialized error logging
    logExtractionError(error, {
      sourceUrl: post.sourceUrl,
      contentLength: post.content.length,
      duration,
      promptLength: 0, // Don't have access to result.promptLength in error case
      memoryInfo: {
        memoryUsed: `${
          (endMemory.heapUsed - startMemory.heapUsed) / 1024 / 1024
        }MB`,
        memoryEnd: JSON.stringify(createMemoryDebugInfo(endMemory)),
      },
    });

    // Handle schema-specific errors
    if (error instanceof Error) {
      handleSchemaError(error);
    }
    
    throw error;
  }
}
