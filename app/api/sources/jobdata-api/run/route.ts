import { type NextRequest, NextResponse } from 'next/server';
import { handleApiError } from '@/lib/api-utils';
import { getApifyService } from '@/lib/services/ApifyService';

export async function POST(_request: NextRequest): Promise<NextResponse> {
  try {
    const actorId = process.env.JOBDATAAPI_ACTOR_ID;

    if (!actorId) {
      return NextResponse.json(
        { error: 'JobDataAPI actor ID not configured' },
        { status: 500 }
      );
    }

    const apifyService = getApifyService();
    
    // Get the schedule input and inject JobData API key if needed
    const scheduleInput = await apifyService.fetchScheduleInputWithApiKey(
      actorId, 
      'JOBDATA_API_KEY'
    );

    // Trigger the run with the schedule input
    const runData = await apifyService.triggerRun(actorId, {
      input: scheduleInput
    });

    return NextResponse.json({
      success: true,
      message: 'JobDataAPI run triggered successfully',
      data: {
        runId: runData.id,
        status: runData.status,
        apifyUrl: `https://console.apify.com/actors/${actorId}/runs/${runData.id}`,
      },
    });
  } catch (error) {
    return handleApiError(error, 'JobDataAPI run trigger');
  }
}
