/**
 * API Request Validation Schemas
 * 
 * Consolidated Zod schemas for API request validation
 * moved from lib/validation.ts for better organization.
 */

import { z } from 'zod';
import { INPUT_LIMITS } from '@/lib/config';

// =============================================================================
// COMMON SCHEMAS
// =============================================================================

export const TimestampSchema = z.string().datetime();
export const UUIDSchema = z.string().uuid();
export const URLSchema = z.string().url();
export const EmailSchema = z.string().email();

// =============================================================================
// EXTRACT API SCHEMAS
// =============================================================================

/**
 * Schema for basic extract API request
 */
export const ExtractRequestSchema = z.object({
  content: z
    .string()
    .min(1, 'Content is required')
    .max(
      INPUT_LIMITS.CONTENT_MAX_CHARS,
      `Content must be less than ${INPUT_LIMITS.CONTENT_MAX_CHARS} characters`
    ),
  sourceUrl: z.string().url().optional(),
});

/**
 * Schema for extract job request - supports updating existing jobs
 */
export const ExtractJobRequestSchema = z.object({
  jobId: z.string().uuid().optional(), // If provided, update existing job
  sourcedAt: z.string().datetime().optional(),
  sourceUrl: z.string().url().optional(),
  content: z
    .string()
    .min(1, 'Content is required')
    .max(
      INPUT_LIMITS.CONTENT_MAX_CHARS,
      `Content must be less than ${INPUT_LIMITS.CONTENT_MAX_CHARS} characters`
    ),
});

export type ExtractJobRequest = z.infer<typeof ExtractJobRequestSchema>;

// =============================================================================
// HEALTH CHECK SCHEMAS
// =============================================================================

export const HealthCheckSchema = z.object({
  service: z.string(),
  status: z.enum(['healthy', 'degraded', 'unhealthy']),
  version: z.string().optional(),
  features: z.array(z.string()),
  environment: z.record(z.string(), z.boolean()).optional(),
  timestamp: TimestampSchema.optional(),
});

export const ServiceHealthResponseSchema = z.object({
  service: z.string(),
  status: z.string(),
  features: z.array(z.string()),
  environment: z.record(z.string(), z.boolean()).optional(),
});

// =============================================================================
// WEBHOOK SCHEMAS
// =============================================================================

export const WebhookBaseSchema = z.object({
  eventType: z.string(),
  createdAt: TimestampSchema,
  timestamp: TimestampSchema,
});

export const ApifyWebhookSchema = WebhookBaseSchema.extend({
  eventData: z.object({
    actorId: z.string(),
    actorRunId: z.string(),
    actorTaskId: z.string().optional(),
    userId: z.string(),
  }),
});

// =============================================================================
// PAGINATION SCHEMAS
// =============================================================================

export const PaginationQuerySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(20),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

export type PaginationQuery = z.infer<typeof PaginationQuerySchema>;

// =============================================================================
// FILTER SCHEMAS
// =============================================================================

export const DateRangeSchema = z.object({
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
});

export const SearchQuerySchema = z.object({
  q: z.string().optional(),
  filters: z.record(z.string(), z.union([z.string(), z.array(z.string())])).optional(),
});

export type SearchQuery = z.infer<typeof SearchQuerySchema>;

// =============================================================================
// AIRTABLE SCHEMAS
// =============================================================================

export const AirtableConfigSchema = z.object({
  baseId: z.string().regex(/^app[A-Za-z0-9]{14}$/, 'Invalid Airtable base ID format'),
  tableName: z.string().min(1).max(100),
  pat: z.string().regex(/^pat[A-Za-z0-9.]{40,}$/, 'Invalid Airtable PAT format'),
});

export type AirtableConfig = z.infer<typeof AirtableConfigSchema>;

// =============================================================================
// JOB SCHEMAS
// =============================================================================

export const JobCreateSchema = z.object({
  title: z.string().min(1),
  description: z.string().optional(),
  company: z.string().optional(),
  location: z.string().optional(),
  remote: z.boolean().optional(),
  salary: z.object({
    min: z.number().optional(),
    max: z.number().optional(),
    currency: z.string().optional(),
  }).optional(),
  tags: z.array(z.string()).optional(),
  sourceUrl: URLSchema.optional(),
});

export type JobCreate = z.infer<typeof JobCreateSchema>;

export const JobUpdateSchema = JobCreateSchema.partial().extend({
  id: UUIDSchema,
});

export type JobUpdate = z.infer<typeof JobUpdateSchema>;