import type { SupabaseClient } from '@supabase/supabase-js';
import { type NextRequest, NextResponse } from 'next/server';
import {
  classifyStatus,
  downloadSnippet,
  fetchHead,
  getMonitoringUrl,
  matchClosedPhrase,
} from '@/lib/monitor';
import type { PipelineResult } from '@/lib/monitor-worker';
import { createServerClient } from '@/lib/supabase';
import { generateMetadata } from '@/lib/utils';

export const dynamic = 'force-dynamic';

type MonitorRequest = {
  type: 'head' | 'full' | 'ai';
};

type MonitoringJob = {
  id: string;
  source_url: string | null;
  apply_url: string | null;
  apply_email: string | null;
  description: string | null;
  status: string;
};

// Helper function to validate request parameters
function validateRequest(jobId: string, type: string) {
  if (!jobId) {
    return NextResponse.json({ error: 'Job ID is required' }, { status: 400 });
  }

  if (!(type && ['head', 'full', 'ai'].includes(type))) {
    return NextResponse.json(
      { error: 'Valid monitoring type is required (head, full, ai)' },
      { status: 400 }
    );
  }

  return null;
}

// Helper function to get job from database
async function getJobForMonitoring(supabase: SupabaseClient, jobId: string) {
  const { data: job, error: jobError } = await supabase
    .from('jobs')
    .select('id, source_url, apply_url, apply_email, description, status')
    .eq('id', jobId)
    .single();

  if (jobError || !job) {
    return {
      error: NextResponse.json({ error: 'Job not found' }, { status: 404 }),
    };
  }

  const urlToMonitor = getMonitoringUrl(job);
  if (!urlToMonitor) {
    return {
      error: NextResponse.json(
        {
          error: 'Job has no valid URL to monitor (email-only or invalid URLs)',
        },
        { status: 400 }
      ),
    };
  }

  return { job, urlToMonitor };
}

// Helper function for HEAD monitoring
async function performHeadMonitoring(
  job: MonitoringJob,
  urlToMonitor: string,
  startTime: number
): Promise<PipelineResult> {
  const head = await fetchHead(urlToMonitor);
  return {
    jobId: job.id,
    headStatus: head.status,
    headOk: head.ok,
    status: head.ok ? 'active' : 'closed',
    confidence: head.ok ? 0.8 : 0.95,
    metadata: generateMetadata(startTime, undefined, {
      model: 'heuristic',
    }),
    decisionLayer: 'head',
  };
}

// Helper function for FULL monitoring (HEAD + phrase matching)
async function performFullMonitoring(
  job: MonitoringJob,
  urlToMonitor: string,
  startTime: number
): Promise<PipelineResult> {
  const head = await fetchHead(urlToMonitor);
  if (head.ok) {
    const snippetRes = await downloadSnippet(urlToMonitor);
    const phrasesModule = await import('@/lib/data/closed-phrases.json', {
      with: { type: 'json' },
    });
    const phrases = phrasesModule.default as readonly string[];
    const pm = matchClosedPhrase(snippetRes.content, phrases);

    return {
      jobId: job.id,
      headStatus: head.status,
      headOk: true,
      status: pm.matched ? 'closed' : 'active',
      confidence: pm.matched ? 0.9 : 0.7,
      metadata: generateMetadata(startTime, undefined, {
        model: 'heuristic',
        matchedPhrase: pm.phrase,
      }),
      decisionLayer: pm.matched ? 'phrase' : 'head',
    };
  }
  return {
    jobId: job.id,
    headStatus: head.status,
    headOk: false,
    status: 'closed',
    confidence: 0.95,
    metadata: generateMetadata(startTime, undefined, {
      model: 'heuristic',
    }),
    decisionLayer: 'head',
  };
}

// Helper function for AI monitoring
async function performAiMonitoring(
  job: MonitoringJob,
  urlToMonitor: string,
  startTime: number
): Promise<PipelineResult> {
  const head = await fetchHead(urlToMonitor);
  if (head.ok) {
    const snippetRes = await downloadSnippet(urlToMonitor);
    const classification = await classifyStatus(snippetRes.content);
    return {
      jobId: job.id,
      headStatus: head.status,
      headOk: true,
      decisionLayer: 'ai',
      ...classification,
    };
  }
  return {
    jobId: job.id,
    headStatus: head.status,
    headOk: false,
    status: 'closed',
    confidence: 0.95,
    metadata: generateMetadata(startTime, undefined, {
      model: 'heuristic',
    }),
    decisionLayer: 'head',
  };
}

// Helper function to store monitoring result in database
async function storeMonitoringResult(
  supabase: SupabaseClient,
  result: PipelineResult,
  job: MonitoringJob
) {
  const logPayload = {
    job_id: result.jobId,
    previous_status: job.status,
    new_status: result.status,
    checked_at: result.metadata.timestamp,
    duration_millis: result.metadata.duration,
    cost_input_tokens: result.metadata.usage.inputTokens,
    cost_output_tokens: result.metadata.usage.outputTokens,
    total_tokens: result.metadata.usage.totalTokens,
    model: (result.metadata as { model?: string }).model ?? null,
    head_status: result.headStatus,
    head_ok: result.headOk,
    decision_layer: result.decisionLayer,
  };

  const { error: logError } = await supabase
    .from('job_monitor_logs')
    .insert([logPayload]);

  if (logError) {
    // Log error silently - monitor log insertion failure shouldn't break the main flow
  }
}

// Helper function to update job status
async function updateJobStatus(
  supabase: SupabaseClient,
  result: PipelineResult,
  job: MonitoringJob
) {
  if (result.status !== job.status) {
    const { error: updateError } = await supabase
      .from('jobs')
      .update({
        status: result.status,
        last_checked_at: result.metadata.timestamp,
        updated_at: new Date().toISOString(),
      })
      .eq('id', job.id);

    if (updateError) {
      // Status update error - handled silently
    }
  } else {
    // Just update the last_checked_at timestamp
    const { error: updateError } = await supabase
      .from('jobs')
      .update({
        last_checked_at: result.metadata.timestamp,
      })
      .eq('id', job.id);

    if (updateError) {
      // Timestamp update error - handled silently
    }
  }
}

// Helper function to handle monitoring errors
async function handleMonitoringError(
  supabase: SupabaseClient,
  job: MonitoringJob,
  startTime: number,
  monitorError: unknown,
  type: string
) {
  const errorResult: PipelineResult = {
    jobId: job.id,
    headStatus: 0,
    headOk: false,
    decisionLayer: 'head',
    status: 'unknown',
    confidence: 0,
    metadata: generateMetadata(startTime, undefined, { model: 'error' }),
    error:
      monitorError instanceof Error
        ? monitorError.message
        : String(monitorError),
  };

  const logPayload = {
    job_id: errorResult.jobId,
    previous_status: job.status,
    new_status: errorResult.status,
    checked_at: errorResult.metadata.timestamp,
    duration_millis: errorResult.metadata.duration,
    cost_input_tokens: 0,
    cost_output_tokens: 0,
    total_tokens: 0,
    model: 'error',
    head_status: 0,
    head_ok: false,
    decision_layer: 'head',
  };

  await supabase.from('job_monitor_logs').insert([logPayload]);

  return NextResponse.json(
    {
      error: 'Monitoring failed',
      details: errorResult.error,
      type,
    },
    { status: 500 }
  );
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: jobId } = await params;
    const { type }: MonitorRequest = await request.json();

    // Validate request parameters
    const validationError = validateRequest(jobId, type);
    if (validationError) {
      return validationError;
    }

    const supabase = await createServerClient();

    // Get job and monitoring URL
    const jobResult = await getJobForMonitoring(supabase, jobId);
    if (jobResult.error) {
      return jobResult.error;
    }

    const { job, urlToMonitor } = jobResult;

    const startTime = Date.now();
    let result: PipelineResult;

    try {
      // Perform monitoring based on type
      switch (type) {
        case 'head':
          result = await performHeadMonitoring(job, urlToMonitor, startTime);
          break;
        case 'full':
          result = await performFullMonitoring(job, urlToMonitor, startTime);
          break;
        case 'ai':
          result = await performAiMonitoring(job, urlToMonitor, startTime);
          break;
        default:
          throw new Error(`Invalid monitoring type: ${type}`);
      }

      // Store monitoring result and update job status
      await storeMonitoringResult(supabase, result, job);
      await updateJobStatus(supabase, result, job);

      return NextResponse.json({
        success: true,
        type,
        status: result.status,
        confidence: result.confidence,
        decisionLayer: result.decisionLayer,
        duration: result.metadata.duration,
        headStatus: result.headStatus,
        headOk: result.headOk,
        statusChanged: result.status !== job.status,
      });
    } catch (monitorError) {
      return await handleMonitoringError(
        supabase,
        job,
        startTime,
        monitorError,
        type
      );
    }
  } catch (_error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
