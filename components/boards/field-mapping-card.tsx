'use client';

import { Database } from 'lucide-react';
import { AIRTABLE_FIELD_LIST } from '../../lib/airtable-mappings';
import { cn } from '../../lib/utils';
import { Alert, AlertDescription } from '../ui/alert';
import { Badge } from '../ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../ui/card';
import type { BoardSchemaData } from './schema-card';

type FieldMappingCardProps = {
  schemaData: BoardSchemaData | null;
  className?: string;
  boardName?: string;
};

export function FieldMappingCard({
  schemaData,
  className,
  boardName,
}: FieldMappingCardProps) {
  if (!schemaData?.success) {
    return null;
  }

  // Use centralized field list from airtable-mappings
  const airtableExpectedFields = [...AIRTABLE_FIELD_LIST]; // Convert to mutable array

  // Get actual Airtable field names from schema
  const airtableFieldNames = schemaData?.fields?.map((f) => f.name) || [];

  const matchedFields = airtableFieldNames.filter((f) =>
    (airtableExpectedFields as readonly string[]).includes(f)
  ).length;

  const extraFields = airtableFieldNames.filter(
    (f) => !(airtableExpectedFields as readonly string[]).includes(f)
  );

  return (
    <Card className={cn(className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 font-medium text-sm">
          <Database className="h-5 w-5" />
          Field Mapping Validation
          {boardName && (
            <Badge className="ml-auto" variant="outline">
              {boardName}
            </Badge>
          )}
        </CardTitle>
        <CardDescription className="text-xs">
          Validation of expected Bordfeed fields against your Airtable table
          schema
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="overflow-x-auto">
          <table className="w-full text-xs">
            <thead>
              <tr className="border-b">
                <th className="py-2 text-left font-medium">Bordfeed Field</th>
                <th className="py-2 text-left font-medium">Airtable Status</th>
                <th className="py-2 text-left font-medium">Field Type</th>
                <th className="py-2 text-left font-medium">
                  Available Options
                </th>
              </tr>
            </thead>
            <tbody>
              {airtableExpectedFields.map((field) => {
                const exists = airtableFieldNames.includes(field);
                const airtableField = schemaData?.fields?.find(
                  (f) => f.name === field
                );

                // Get available options for select fields
                const getFieldOptions = (
                  airtableFieldData:
                    | {
                        options?: { choices?: Array<{ name: string }> };
                      }
                    | undefined
                ) => {
                  if (!airtableFieldData?.options?.choices) {
                    return null;
                  }

                  const choices = airtableFieldData.options.choices;
                  if (choices.length === 0) {
                    return null;
                  }

                  // Show first few options with count if there are many
                  const displayChoices = choices
                    .slice(0, 4)
                    .map((choice) => `"${choice.name}"`);
                  const remainingCount = choices.length - 4;

                  if (remainingCount > 0) {
                    displayChoices.push(`+${remainingCount} more`);
                  }

                  return displayChoices.join(', ');
                };

                const fieldOptions = getFieldOptions(airtableField);

                return (
                  <tr className="border-b" key={field}>
                    <td className="py-2">
                      <code className="text-xs">{field}</code>
                    </td>
                    <td className="py-2">
                      <Badge variant={exists ? 'default' : 'destructive'}>
                        {exists ? '✅ Available' : '❌ Missing'}
                      </Badge>
                    </td>
                    <td className="py-2 text-muted-foreground">
                      {airtableField ? airtableField.type : '-'}
                    </td>
                    <td className="max-w-xs py-2">
                      {fieldOptions ? (
                        <code className="break-words text-muted-foreground text-xs">
                          {fieldOptions}
                        </code>
                      ) : (
                        <span className="text-muted-foreground">-</span>
                      )}
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>

        <div className="flex items-center gap-4 text-xs">
          <div className="flex items-center gap-2">
            <div className="h-4 w-4 rounded border bg-primary" />
            <span className="text-muted-foreground">
              Field exists in Airtable (will be synced)
            </span>
          </div>
          <div className="flex items-center gap-2">
            <div className="h-4 w-4 rounded border bg-destructive" />
            <span className="text-muted-foreground">
              Field missing in Airtable (will be skipped)
            </span>
          </div>
        </div>

        <Alert>
          <AlertDescription className="text-xs">
            <strong>Summary:</strong> {matchedFields} out of{' '}
            {airtableExpectedFields.length} expected fields are available in
            your Airtable table. Missing fields will be ignored during job
            posting.
          </AlertDescription>
        </Alert>

        {extraFields.length > 0 && (
          <Alert>
            <AlertDescription className="text-xs">
              <div className="space-y-2">
                <div className="font-medium">
                  Extra Airtable Fields (not used by Bordfeed):
                </div>
                <div className="flex flex-wrap gap-2">
                  {extraFields.map((field) => (
                    <Badge key={field} variant="outline">
                      {field}
                    </Badge>
                  ))}
                </div>
              </div>
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
}
