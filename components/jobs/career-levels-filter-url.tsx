'use client';

import { MultiSelectUrlFilter } from '@/components/common/MultiSelectUrlFilter';
import { CAREER_LEVELS } from '@/lib/career-levels';

type CareerLevelsFilterUrlProps = {
  disabled?: boolean;
};

/**
 * Career Levels filter component with URL state
 * Now uses the generic MultiSelectUrlFilter component
 */
export function CareerLevelsFilterUrl({
  disabled = false,
}: CareerLevelsFilterUrlProps) {
  return (
    <MultiSelectUrlFilter
      clearable={false}
      disabled={disabled}
      label="Career Levels"
      maxHeight="max-h-48"
      options={CAREER_LEVELS}
      paramName="levels"
      searchable={false}
      showCount={true}
    />
  );
}
