/**
 * Schema Validation Utilities
 *
 * Advanced schema validation patterns and error handling utilities
 * for complex validation scenarios and custom validation logic.
 */

import { type ZodError, type ZodSchema, z } from 'zod';
import {
  createValidationError,
  type ValidationErrorResult,
} from './validation-utils';

// =============================================================================
// CUSTOM VALIDATION HELPERS
// =============================================================================

/**
 * Custom Zod refinement for URL validation with specific requirements
 */
export const customUrlValidation = (
  options: {
    allowLocalhost?: boolean;
    requireHttps?: boolean;
    allowedDomains?: string[];
  } = {}
) => {
  return z.string().refine(
    (url) => {
      try {
        const parsed = new URL(url);

        // Check HTTPS requirement
        if (options.requireHttps && parsed.protocol !== 'https:') {
          return false;
        }

        // Check localhost allowance
        if (
          !options.allowLocalhost &&
          (parsed.hostname === 'localhost' || parsed.hostname === '127.0.0.1')
        ) {
          return false;
        }

        // Check allowed domains
        if (
          options.allowedDomains &&
          !options.allowedDomains.includes(parsed.hostname)
        ) {
          return false;
        }

        return true;
      } catch {
        return false;
      }
    },
    {
      message: 'Invalid URL format or does not meet requirements',
    }
  );
};

/**
 * Validate content length with multiple constraints
 */
export const contentLengthValidation = (options: {
  minWords?: number;
  maxWords?: number;
  minChars?: number;
  maxChars?: number;
  forbiddenWords?: string[];
}) => {
  return z.string().refine(
    (content) => {
      const wordCount = content.trim().split(/\s+/).length;
      const charCount = content.length;

      // Check word count
      if (options.minWords && wordCount < options.minWords) {
        return false;
      }
      if (options.maxWords && wordCount > options.maxWords) {
        return false;
      }

      // Check character count
      if (options.minChars && charCount < options.minChars) {
        return false;
      }
      if (options.maxChars && charCount > options.maxChars) {
        return false;
      }

      // Check forbidden words
      if (options.forbiddenWords) {
        const lowercaseContent = content.toLowerCase();
        for (const word of options.forbiddenWords) {
          if (lowercaseContent.includes(word.toLowerCase())) {
            return false;
          }
        }
      }

      return true;
    },
    {
      message: 'Content does not meet validation requirements',
    }
  );
};

// =============================================================================
// SCHEMA COMPOSITION UTILITIES
// =============================================================================

/**
 * Create a schema with pagination support
 */
export function withPagination<T extends ZodSchema>(baseSchema: T) {
  return z.object({
    data: z.array(baseSchema),
    pagination: z.object({
      page: z.number().min(1),
      limit: z.number().min(1).max(100),
      total: z.number().min(0),
      totalPages: z.number().min(0),
      hasNext: z.boolean(),
      hasPrevious: z.boolean(),
    }),
  });
}

/**
 * Create a schema with standard API response wrapper
 */
export function withApiResponse<T extends ZodSchema>(dataSchema: T) {
  return z.object({
    success: z.boolean(),
    data: dataSchema.optional(),
    error: z.string().optional(),
    timestamp: z.string().datetime(),
    requestId: z.string().uuid().optional(),
  });
}

/**
 * Create a schema for soft-deletable entities
 */
export function withSoftDelete<T extends z.ZodObject<any>>(baseSchema: T) {
  return baseSchema.extend({
    deletedAt: z.string().datetime().nullable().optional(),
    isDeleted: z.boolean().default(false),
  });
}

/**
 * Create a schema with audit fields
 */
export function withAuditFields<T extends z.ZodObject<any>>(baseSchema: T) {
  return baseSchema.extend({
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    createdBy: z.string().uuid().optional(),
    updatedBy: z.string().uuid().optional(),
  });
}

// =============================================================================
// VALIDATION ERROR PROCESSING
// =============================================================================

/**
 * Process Zod validation errors into user-friendly messages
 */
export function processZodError(error: ZodError): ValidationErrorResult {
  const errors: string[] = [];

  for (const issue of error.issues) {
    const path = issue.path.length > 0 ? issue.path.join('.') : 'root';
    const message = `${path}: ${issue.message}`;
    errors.push(message);
  }

  return createValidationError(errors, {
    zodError: error,
    issueCount: error.issues.length,
  });
}

/**
 * Validate schema and return formatted error result
 */
export function validateSchemaWithErrors<T>(
  schema: ZodSchema<T>,
  data: unknown
): { data?: T; errors?: ValidationErrorResult } {
  const result = schema.safeParse(data);

  if (result.success) {
    return { data: result.data };
  }

  return { errors: processZodError(result.error) };
}

// =============================================================================
// CONDITIONAL VALIDATION
// =============================================================================

/**
 * Create a schema that validates differently based on a condition
 */
export function conditionalSchema<T, U>(
  condition: (data: unknown) => boolean,
  trueSchema: ZodSchema<T>,
  falseSchema: ZodSchema<U>
) {
  return z.union([trueSchema, falseSchema]).refine((data) => {
    if (condition(data)) {
      return trueSchema.safeParse(data).success;
    }
    return falseSchema.safeParse(data).success;
  });
}

/**
 * Create a schema that requires certain fields based on another field's value
 */
export function dependentRequired<T extends Record<string, unknown>>(
  triggerField: keyof T,
  triggerValue: unknown,
  requiredFields: (keyof T)[]
) {
  return z.any().refine(
    (data: T) => {
      if (data[triggerField] === triggerValue) {
        for (const field of requiredFields) {
          if (!data[field]) {
            return false;
          }
        }
      }
      return true;
    },
    {
      message: `Fields ${requiredFields.join(', ')} are required when ${String(triggerField)} is ${triggerValue}`,
    }
  );
}

// =============================================================================
// ASYNC VALIDATION
// =============================================================================

/**
 * Interface for async validation functions
 */
export type AsyncValidator<T = unknown> = {
  validate: (data: T) => Promise<ValidationErrorResult>;
  name: string;
};

/**
 * Run multiple async validators and combine results
 */
export async function runAsyncValidators<T>(
  data: T,
  validators: AsyncValidator<T>[]
): Promise<ValidationErrorResult> {
  const results = await Promise.all(
    validators.map((validator) => validator.validate(data))
  );

  const allErrors = results.flatMap((result) => result.errors);

  return {
    valid: allErrors.length === 0,
    errors: allErrors,
    details: {
      validatorResults: results.map((result, index) => ({
        name: validators[index].name,
        valid: result.valid,
        errors: result.errors,
      })),
    },
  };
}
