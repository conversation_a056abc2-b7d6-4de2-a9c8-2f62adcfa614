import type { NextResponse } from 'next/server';
import type { ZodError, ZodSchema } from 'zod';
import { validationErrorResponse } from '@/lib/api-utils';

/**
 * Core Validation Utilities
 *
 * Provides standardized validation patterns and error handling
 * for consistent validation across the application.
 */

export type ValidationResult<T = unknown> = {
  success: boolean;
  data?: T;
  error?: ZodError;
};

export type ValidationErrorResult = {
  valid: boolean;
  errors: string[];
  details?: Record<string, unknown>;
};

/**
 * Validate data with a Zod schema and return standardized result
 */
export function validateWithSchema<T>(
  schema: ZodSchema<T>,
  data: unknown
): ValidationResult<T> {
  const result = schema.safeParse(data);

  if (result.success) {
    return {
      success: true,
      data: result.data,
    };
  }

  return {
    success: false,
    error: result.error,
  };
}

/**
 * Validate API request body and return NextResponse on error
 */
export function validateApiRequest<T>(
  schema: ZodSchema<T>,
  data: unknown
): { data?: T; errorResponse?: NextResponse } {
  const validation = validateWithSchema(schema, data);

  if (!validation.success && validation.error) {
    return {
      errorResponse: validationErrorResponse(validation.error),
    };
  }

  return {
    data: validation.data,
  };
}

/**
 * Common URL validation patterns
 */
export const URL_PATTERNS = {
  // Basic URL regex
  URL: /^https?:\/\/[^\s/$.?#].[^\s]*$/i,

  // Specific service patterns
  AIRTABLE_BASE_ID: /^app[A-Za-z0-9]{14}$/,
  AIRTABLE_PAT: /^pat[A-Za-z0-9.]{40,}$/,

  // UUID patterns
  UUID_V4:
    /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,

  // Email pattern (basic)
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
} as const;

/**
 * Validate URL format
 */
export function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

/**
 * Validate email format
 */
export function isValidEmail(email: string): boolean {
  return URL_PATTERNS.EMAIL.test(email);
}

/**
 * Validate UUID format
 */
export function isValidUUID(uuid: string): boolean {
  return URL_PATTERNS.UUID_V4.test(uuid);
}

/**
 * Validate string length with custom error messages
 */
export function validateStringLength(
  value: string,
  options: {
    min?: number;
    max?: number;
    fieldName?: string;
  }
): ValidationErrorResult {
  const {
    min = 0,
    max = Number.POSITIVE_INFINITY,
    fieldName = 'Value',
  } = options;
  const errors: string[] = [];

  if (value.length < min) {
    errors.push(`${fieldName} must be at least ${min} characters`);
  }

  if (value.length > max) {
    errors.push(`${fieldName} must be no more than ${max} characters`);
  }

  return {
    valid: errors.length === 0,
    errors,
  };
}

/**
 * Validate required fields in an object
 */
export function validateRequiredFields<T extends Record<string, unknown>>(
  data: T,
  requiredFields: (keyof T)[]
): ValidationErrorResult {
  const errors: string[] = [];

  for (const field of requiredFields) {
    const value = data[field];
    if (value === undefined || value === null || value === '') {
      errors.push(`${String(field)} is required`);
    }
  }

  return {
    valid: errors.length === 0,
    errors,
  };
}

/**
 * Combine multiple validation results
 */
export function combineValidationResults(
  ...results: ValidationErrorResult[]
): ValidationErrorResult {
  const allErrors = results.flatMap((result) => result.errors);

  return {
    valid: allErrors.length === 0,
    errors: allErrors,
  };
}

/**
 * Create a validation error result
 */
export function createValidationError(
  errors: string | string[],
  details?: Record<string, unknown>
): ValidationErrorResult {
  return {
    valid: false,
    errors: Array.isArray(errors) ? errors : [errors],
    details,
  };
}

/**
 * Create a validation success result
 */
export function createValidationSuccess(): ValidationErrorResult {
  return {
    valid: true,
    errors: [],
  };
}
