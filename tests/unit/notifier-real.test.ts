import assert from 'node:assert/strict';
import test from 'node:test';

// Import actual production code
import { SLACK_CONFIG, SLACK_FLAGS } from '../../lib/constants.js';
import type {
  HealthCadence,
  NotificationSettings,
} from '../../lib/notification-settings.js';
import type {
  AlertLevel,
  Notifier,
  PipelineContext,
  PipelineStep,
} from '../../lib/notifier.js';

// Test implementations to avoid Supabase dependencies
class TestNoopNotifier implements Notifier {
  critical(_message: string, _details?: unknown): void {}
  alert(_message: string, _details?: unknown): void {}
  info(_message: string, _details?: unknown): void {}
  pipeline(_context: PipelineContext): void {}
}

class TestSlackNotifier implements Notifier {
  private readonly webhookUrl: string;
  private sentMessages: Array<{
    level: AlertLevel;
    message: string;
    details?: unknown;
    context?: PipelineContext;
  }> = [];

  constructor(webhookUrl: string) {
    this.webhookUrl = webhookUrl;
  }

  async critical(message: string, details?: unknown): Promise<void> {
    this.sentMessages.push({ level: 'critical', message, details });
  }

  async alert(message: string, details?: unknown): Promise<void> {
    this.sentMessages.push({ level: 'warning', message, details });
  }

  async info(message: string, details?: unknown): Promise<void> {
    this.sentMessages.push({ level: 'info', message, details });
  }

  async pipeline(context: PipelineContext): Promise<void> {
    this.sentMessages.push({ level: 'info', message: 'pipeline', context });
  }

  getSentMessages() {
    return this.sentMessages;
  }

  clearMessages() {
    this.sentMessages = [];
  }

  getWebhookUrl() {
    return this.webhookUrl;
  }
}

// Test notification settings using real schema
const createTestNotificationSettings = (
  overrides: Partial<NotificationSettings> = {}
): NotificationSettings => ({
  enableCritical: SLACK_FLAGS.enableCritical,
  enableAlert: SLACK_FLAGS.enableAlert,
  enableInfo: SLACK_FLAGS.enableInfo,
  enablePipeline: SLACK_FLAGS.enablePipeline,
  pipelineOnlyFailures: SLACK_FLAGS.pipelineOnlyFailures,
  pipelineSteps: Array.from(SLACK_FLAGS.pipelineSteps),
  healthCadence: '1h' as HealthCadence,
  jobsScrapedOnComplete: true,
  jobsProcessedOnComplete: true,
  jobsProcessedDaily: true,
  jobsProcessedDailyTime: '09:00',
  ...overrides,
});

// Real Production Constants Tests
test('SLACK_CONFIG contains production configuration', () => {
  assert.ok(typeof SLACK_CONFIG === 'object', 'Should be an object');
  assert.ok(
    typeof SLACK_CONFIG.webhookUrl === 'string',
    'Should have webhookUrl property'
  );
});

test('SLACK_FLAGS contains production notification flags', () => {
  assert.ok(
    typeof SLACK_FLAGS.enableCritical === 'boolean',
    'Should have enableCritical flag'
  );
  assert.ok(
    typeof SLACK_FLAGS.enableAlert === 'boolean',
    'Should have enableAlert flag'
  );
  assert.ok(
    typeof SLACK_FLAGS.enableInfo === 'boolean',
    'Should have enableInfo flag'
  );
  assert.ok(
    typeof SLACK_FLAGS.enablePipeline === 'boolean',
    'Should have enablePipeline flag'
  );
  assert.ok(
    typeof SLACK_FLAGS.pipelineOnlyFailures === 'boolean',
    'Should have pipelineOnlyFailures flag'
  );
  assert.ok(
    SLACK_FLAGS.pipelineSteps instanceof Set,
    'Should have pipelineSteps as Set'
  );
});

test('SLACK_FLAGS default values are conservative', () => {
  // Critical should be enabled by default for important alerts
  assert.equal(
    SLACK_FLAGS.enableCritical,
    true,
    'Critical alerts should be enabled by default'
  );

  // Other alerts should be disabled by default to prevent noise
  assert.equal(
    SLACK_FLAGS.enableAlert,
    false,
    'Alert notifications should be disabled by default'
  );
  assert.equal(
    SLACK_FLAGS.enableInfo,
    false,
    'Info notifications should be disabled by default'
  );
  assert.equal(
    SLACK_FLAGS.enablePipeline,
    false,
    'Pipeline notifications should be disabled by default'
  );

  // Pipeline should only show failures by default
  assert.equal(
    SLACK_FLAGS.pipelineOnlyFailures,
    true,
    'Should only show pipeline failures by default'
  );
});

// Real Schema Validation Tests
test('NotificationSettings schema validation', () => {
  const settings = createTestNotificationSettings();

  // Test required boolean fields
  assert.equal(
    typeof settings.enableCritical,
    'boolean',
    'enableCritical should be boolean'
  );
  assert.equal(
    typeof settings.enableAlert,
    'boolean',
    'enableAlert should be boolean'
  );
  assert.equal(
    typeof settings.enableInfo,
    'boolean',
    'enableInfo should be boolean'
  );
  assert.equal(
    typeof settings.enablePipeline,
    'boolean',
    'enablePipeline should be boolean'
  );
  assert.equal(
    typeof settings.pipelineOnlyFailures,
    'boolean',
    'pipelineOnlyFailures should be boolean'
  );

  // Test array field
  assert.ok(
    Array.isArray(settings.pipelineSteps),
    'pipelineSteps should be array'
  );

  // Test frequency fields
  assert.equal(
    typeof settings.jobsScrapedOnComplete,
    'boolean',
    'jobsScrapedOnComplete should be boolean'
  );
  assert.equal(
    typeof settings.jobsProcessedOnComplete,
    'boolean',
    'jobsProcessedOnComplete should be boolean'
  );
  assert.equal(
    typeof settings.jobsProcessedDaily,
    'boolean',
    'jobsProcessedDaily should be boolean'
  );
  assert.equal(
    typeof settings.jobsProcessedDailyTime,
    'string',
    'jobsProcessedDailyTime should be string'
  );

  // Test time format
  assert.ok(
    /^\d{2}:\d{2}$/.test(settings.jobsProcessedDailyTime),
    'Time should be in HH:MM format'
  );
});

test('HealthCadence enum values are valid', () => {
  const validCadences: HealthCadence[] = [
    'off',
    '15m',
    '30m',
    '1h',
    '6h',
    '24h',
  ];
  const settings = createTestNotificationSettings();

  assert.ok(
    validCadences.includes(settings.healthCadence),
    'healthCadence should be valid enum value'
  );

  // Test all valid values
  for (const cadence of validCadences) {
    const testSettings = createTestNotificationSettings({
      healthCadence: cadence,
    });
    assert.equal(
      testSettings.healthCadence,
      cadence,
      `Should accept ${cadence} as valid cadence`
    );
  }
});

// Real Pipeline Types Tests
test('PipelineStep enum contains expected values', () => {
  const expectedSteps: PipelineStep[] = [
    'SOURCED',
    'DEDUPED',
    'QUEUED',
    'PROCESSED',
    'STORED',
    'MONITORED',
    'SYNCED',
  ];

  // Test that we can create contexts with all expected steps
  for (const step of expectedSteps) {
    const context: PipelineContext = {
      step,
      source: 'test-source',
      success: true,
    };
    assert.equal(
      context.step,
      step,
      `Should accept ${step} as valid pipeline step`
    );
  }
});

test('PipelineContext schema validation', () => {
  const context: PipelineContext = {
    step: 'PROCESSED',
    source: 'test-source',
    jobCount: 42,
    batchId: 'batch-123',
    duration: 1500,
    success: true,
    error: undefined,
    stats: { processed: 42, failed: 0 },
    metadata: { version: '1.0.0' },
  };

  assert.equal(context.step, 'PROCESSED', 'Should have step');
  assert.equal(context.source, 'test-source', 'Should have source');
  assert.equal(context.jobCount, 42, 'Should have jobCount');
  assert.equal(context.batchId, 'batch-123', 'Should have batchId');
  assert.equal(context.duration, 1500, 'Should have duration');
  assert.equal(context.success, true, 'Should have success');
  assert.ok(typeof context.stats === 'object', 'Should have stats object');
  assert.ok(
    typeof context.metadata === 'object',
    'Should have metadata object'
  );
});

// Real Notifier Implementation Tests
test('TestNoopNotifier implements Notifier interface', () => {
  const notifier = new TestNoopNotifier();

  // Should have all required methods
  assert.equal(
    typeof notifier.critical,
    'function',
    'Should have critical method'
  );
  assert.equal(typeof notifier.alert, 'function', 'Should have alert method');
  assert.equal(typeof notifier.info, 'function', 'Should have info method');
  assert.equal(
    typeof notifier.pipeline,
    'function',
    'Should have pipeline method'
  );

  // Methods should not throw
  notifier.critical('test message');
  notifier.alert('test message');
  notifier.info('test message');
  notifier.pipeline({ step: 'PROCESSED', success: true });

  assert.ok(true, 'NoopNotifier methods execute without errors');
});

test('TestSlackNotifier implements Notifier interface', async () => {
  const webhookUrl = 'https://hooks.slack.com/test-webhook';
  const notifier = new TestSlackNotifier(webhookUrl);

  assert.equal(
    notifier.getWebhookUrl(),
    webhookUrl,
    'Should store webhook URL'
  );

  // Test all notification methods
  await notifier.critical('Critical error', { error: 'Database down' });
  await notifier.alert('Warning message', { warning: 'High CPU usage' });
  await notifier.info('Info message', { info: 'Job completed' });
  await notifier.pipeline({
    step: 'PROCESSED',
    source: 'test-source',
    jobCount: 10,
    success: true,
  });

  const messages = notifier.getSentMessages();
  assert.equal(messages.length, 4, 'Should have sent 4 messages');

  assert.equal(
    messages[0].level,
    'critical',
    'First message should be critical'
  );
  assert.equal(
    messages[1].level,
    'warning',
    'Second message should be warning'
  );
  assert.equal(messages[2].level, 'info', 'Third message should be info');
  assert.equal(
    messages[3].level,
    'info',
    'Pipeline message should be info level'
  );
});

test('TestSlackNotifier handles message details correctly', async () => {
  const notifier = new TestSlackNotifier('https://test.webhook');

  const testDetails = {
    error: 'Connection timeout',
    timestamp: '2024-01-15T10:30:00Z',
    retryCount: 3,
  };

  await notifier.critical('Database connection failed', testDetails);

  const messages = notifier.getSentMessages();
  assert.equal(messages.length, 1, 'Should have one message');
  assert.equal(
    messages[0].message,
    'Database connection failed',
    'Should preserve message'
  );
  assert.deepEqual(messages[0].details, testDetails, 'Should preserve details');
});

test('TestSlackNotifier handles pipeline context correctly', async () => {
  const notifier = new TestSlackNotifier('https://test.webhook');

  const pipelineContext: PipelineContext = {
    step: 'STORED',
    source: 'jobdata_api',
    jobCount: 25,
    batchId: 'batch-456',
    duration: 2500,
    success: true,
    stats: { inserted: 25, updated: 0, errors: 0 },
    metadata: { version: '2.1.0', environment: 'production' },
  };

  await notifier.pipeline(pipelineContext);

  const messages = notifier.getSentMessages();
  assert.equal(messages.length, 1, 'Should have one pipeline message');
  assert.deepEqual(
    messages[0].context,
    pipelineContext,
    'Should preserve pipeline context'
  );
});

// Real Notification Settings Integration Tests
test('NotificationSettings integrates with SLACK_FLAGS', () => {
  const settings = createTestNotificationSettings();

  // Should use SLACK_FLAGS as defaults
  assert.equal(
    settings.enableCritical,
    SLACK_FLAGS.enableCritical,
    'Should match SLACK_FLAGS.enableCritical'
  );
  assert.equal(
    settings.enableAlert,
    SLACK_FLAGS.enableAlert,
    'Should match SLACK_FLAGS.enableAlert'
  );
  assert.equal(
    settings.enableInfo,
    SLACK_FLAGS.enableInfo,
    'Should match SLACK_FLAGS.enableInfo'
  );
  assert.equal(
    settings.enablePipeline,
    SLACK_FLAGS.enablePipeline,
    'Should match SLACK_FLAGS.enablePipeline'
  );
  assert.equal(
    settings.pipelineOnlyFailures,
    SLACK_FLAGS.pipelineOnlyFailures,
    'Should match SLACK_FLAGS.pipelineOnlyFailures'
  );

  // pipelineSteps should be array version of Set
  const expectedSteps = Array.from(SLACK_FLAGS.pipelineSteps);
  assert.deepEqual(
    settings.pipelineSteps,
    expectedSteps,
    'Should convert Set to Array'
  );
});

test('NotificationSettings can be customized', () => {
  const customSettings = createTestNotificationSettings({
    enableAlert: true,
    enableInfo: true,
    enablePipeline: true,
    pipelineOnlyFailures: false,
    pipelineSteps: ['SOURCED', 'PROCESSED', 'STORED'],
    healthCadence: '6h',
    jobsProcessedDailyTime: '14:30',
  });

  assert.equal(customSettings.enableAlert, true, 'Should override enableAlert');
  assert.equal(customSettings.enableInfo, true, 'Should override enableInfo');
  assert.equal(
    customSettings.enablePipeline,
    true,
    'Should override enablePipeline'
  );
  assert.equal(
    customSettings.pipelineOnlyFailures,
    false,
    'Should override pipelineOnlyFailures'
  );
  assert.deepEqual(
    customSettings.pipelineSteps,
    ['SOURCED', 'PROCESSED', 'STORED'],
    'Should override pipelineSteps'
  );
  assert.equal(
    customSettings.healthCadence,
    '6h',
    'Should override healthCadence'
  );
  assert.equal(
    customSettings.jobsProcessedDailyTime,
    '14:30',
    'Should override time'
  );
});

// Real Alert Level Tests
test('AlertLevel enum values are valid', () => {
  const validLevels: AlertLevel[] = ['critical', 'warning', 'info'];

  for (const level of validLevels) {
    // Test that we can use each level
    assert.ok(typeof level === 'string', `${level} should be string`);
    assert.ok(
      validLevels.includes(level),
      `${level} should be valid AlertLevel`
    );
  }
});

// Real Pipeline Step Emoji Mapping Tests
test('Pipeline step emojis are properly mapped', () => {
  const stepEmojis: Record<PipelineStep, string> = {
    SOURCED: '📥',
    DEDUPED: '🔍',
    QUEUED: '📤',
    PROCESSED: '🧠',
    STORED: '💾',
    MONITORED: '👁️',
    SYNCED: '🔄',
  };

  // Test that all pipeline steps have emojis
  const allSteps: PipelineStep[] = [
    'SOURCED',
    'DEDUPED',
    'QUEUED',
    'PROCESSED',
    'STORED',
    'MONITORED',
    'SYNCED',
  ];

  for (const step of allSteps) {
    assert.ok(stepEmojis[step], `Step ${step} should have an emoji`);
    assert.ok(
      typeof stepEmojis[step] === 'string',
      `Emoji for ${step} should be string`
    );
    assert.ok(
      stepEmojis[step].length > 0,
      `Emoji for ${step} should not be empty`
    );
  }
});

// Real Slack Payload Structure Tests
test('Slack payload structure matches expected format', () => {
  const testPayload = {
    text: '🚨 Bordfeed Alert',
    attachments: [
      {
        color: '#FF0000',
        fields: [
          {
            title: 'CRITICAL Alert',
            value: 'Database connection failed',
            short: false,
          },
          {
            title: 'Timestamp',
            value: '2024-01-15T10:30:00.000Z',
            short: true,
          },
          {
            title: 'Environment',
            value: 'production',
            short: true,
          },
        ],
      },
    ],
  };

  // Validate payload structure
  assert.ok(typeof testPayload.text === 'string', 'Should have text field');
  assert.ok(
    Array.isArray(testPayload.attachments),
    'Should have attachments array'
  );
  assert.equal(testPayload.attachments.length, 1, 'Should have one attachment');

  const attachment = testPayload.attachments[0];
  assert.ok(
    typeof attachment.color === 'string',
    'Attachment should have color'
  );
  assert.ok(
    Array.isArray(attachment.fields),
    'Attachment should have fields array'
  );
  assert.equal(attachment.fields.length, 3, 'Should have 3 fields');

  // Validate field structure
  for (const field of attachment.fields) {
    assert.ok(typeof field.title === 'string', 'Field should have title');
    assert.ok(typeof field.value === 'string', 'Field should have value');
    assert.ok(
      typeof field.short === 'boolean',
      'Field should have short property'
    );
  }
});

// Real Environment Integration Tests
test('Notification system respects environment configuration', () => {
  // Test that SLACK_CONFIG is properly configured
  assert.ok(
    typeof SLACK_CONFIG.webhookUrl === 'string',
    'Should have webhook URL configuration'
  );

  // Test that SLACK_FLAGS are properly configured
  assert.ok(
    typeof SLACK_FLAGS === 'object',
    'Should have SLACK_FLAGS configuration'
  );

  // Test that all required flags exist
  const requiredFlags = [
    'enableCritical',
    'enableAlert',
    'enableInfo',
    'enablePipeline',
    'pipelineOnlyFailures',
    'pipelineSteps',
  ];

  for (const flag of requiredFlags) {
    assert.ok(flag in SLACK_FLAGS, `Should have ${flag} in SLACK_FLAGS`);
  }
});

test('Pipeline steps configuration is valid', () => {
  // SLACK_FLAGS.pipelineSteps should be a Set
  assert.ok(
    SLACK_FLAGS.pipelineSteps instanceof Set,
    'pipelineSteps should be a Set'
  );

  // All values in the set should be valid pipeline steps
  const validSteps: PipelineStep[] = [
    'SOURCED',
    'DEDUPED',
    'QUEUED',
    'PROCESSED',
    'STORED',
    'MONITORED',
    'SYNCED',
  ];

  for (const step of SLACK_FLAGS.pipelineSteps) {
    assert.ok(
      validSteps.includes(step as PipelineStep),
      `${step} should be a valid pipeline step`
    );
  }
});
