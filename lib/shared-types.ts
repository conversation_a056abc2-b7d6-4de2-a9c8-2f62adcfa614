/**
 * Shared types and interfaces used across multiple modules
 */

/**
 * Raw job data structure used by multiple fetch endpoints
 */
export type RawJobData = {
  id: string;
  title: string;
  content: string;
  source_type: string;
  source_name: string;
  raw_data: Record<string, unknown>;
};

/**
 * Airtable field definition
 */
export type AirtableField = {
  id: string;
  name: string;
  type: string;
  options?: {
    choices?: Array<{
      id: string;
      name: string;
      color?: string;
    }>;
    [key: string]: unknown;
  };
};

/**
 * Airtable table definition
 */
export type AirtableTable = {
  id: string;
  name: string;
  primaryFieldId?: string;
  fields: AirtableField[];
};

/**
 * Database operation result
 */
export type DatabaseOperationResult = {
  success: boolean;
  error?: string;
  data?: unknown;
  id?: string;
};

/**
 * Job processing result
 */
export type JobProcessingResult = {
  success: boolean;
  job_id: string;
  database_id?: string;
  error?: string;
  duplicated?: boolean;
  stored_at?: string;
};
