'use client';

import { parseAsString, useQueryState } from 'nuqs';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface MultiSelectUrlFilterProps {
  /** Display label for the filter */
  label: string;
  /** URL query parameter name */
  paramName: string;
  /** Array of options to display - can be strings or objects with code/name */
  options: readonly string[] | readonly { code: string; name: string }[];
  /** Whether the filter is disabled */
  disabled?: boolean;
  /** Whether to show search input for filtering options */
  searchable?: boolean;
  /** Maximum height for the options container */
  maxHeight?: string;
  /** Whether to show clear all button */
  clearable?: boolean;
  /** Whether to show selected count */
  showCount?: boolean;
}

/**
 * Generic multi-select filter component with URL state management
 * 
 * Consolidates the common pattern used across filter components:
 * - URL state stores comma-separated string
 * - Converts between array and string automatically
 * - Optional search functionality
 * - Consistent UI patterns
 */
export function MultiSelectUrlFilter({
  label,
  paramName,
  options,
  disabled = false,
  searchable = false,
  maxHeight = 'max-h-48',
  clearable = true,
  showCount = true,
}: MultiSelectUrlFilterProps) {
  // URL state for selected items (comma-separated string)
  const [selectedString, setSelectedString] = useQueryState(
    paramName,
    parseAsString.withDefault('')
  );

  // Local search state for filtering options list
  const [searchTerm, setSearchTerm] = useState('');

  // Convert string to array for UI
  const selectedItems = selectedString
    ? selectedString
        .split(',')
        .map((s) => s.trim())
        .filter(Boolean)
    : [];

  // Helper to get value and display text from option
  const getOptionValue = (option: string | { code: string; name: string }) =>
    typeof option === 'string' ? option : option.code;
  
  const getOptionDisplay = (option: string | { code: string; name: string }) =>
    typeof option === 'string' ? option : `${option.name} (${option.code})`;

  // Filter options based on search term
  const filteredOptions = searchable
    ? options.filter((option) => {
        const display = getOptionDisplay(option);
        const value = getOptionValue(option);
        return display.toLowerCase().includes(searchTerm.toLowerCase()) ||
               value.toLowerCase().includes(searchTerm.toLowerCase());
      })
    : options;

  // Handle checkbox changes
  const handleToggle = (value: string, checked: boolean) => {
    let newItems: string[];

    if (checked) {
      // Add item if not already selected
      newItems = selectedItems.includes(value)
        ? selectedItems
        : [...selectedItems, value];
    } else {
      // Remove item
      newItems = selectedItems.filter((selected) => selected !== value);
    }

    // Convert back to comma-separated string for URL
    const newSelectedString =
      newItems.length > 0 ? newItems.join(',') : null; // null removes the parameter from URL

    setSelectedString(newSelectedString);
  };

  // Clear all selected items
  const clearAll = () => {
    setSelectedString(null);
  };

  return (
    <div>
      {/* Header with label and optional clear button */}
      <div className="flex items-center justify-between">
        <Label className="font-medium text-sm">{label}</Label>
        {clearable && selectedItems.length > 0 && (
          <Button
            className="h-auto p-1 text-xs"
            disabled={disabled}
            onClick={clearAll}
            size="sm"
            variant="ghost"
          >
            Clear All
          </Button>
        )}
      </div>

      {/* Optional search input */}
      {searchable && (
        <div className="mt-2">
          <Input
            className="text-sm"
            disabled={disabled}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder={`Search ${label.toLowerCase()}...`}
            value={searchTerm}
          />
        </div>
      )}

      {/* Selected items summary */}
      {showCount && selectedItems.length > 0 && (
        <div className="mt-2 text-muted-foreground text-xs">
          {searchable ? (
            <>
              Selected ({selectedItems.length}):{' '}
              {selectedItems.slice(0, 3).map(value => {
                // Find the option to get display name
                const option = options.find(opt => getOptionValue(opt) === value);
                return option ? (typeof option === 'string' ? option : option.name) : value;
              }).join(', ')}
              {selectedItems.length > 3 &&
                ` and ${selectedItems.length - 3} more`}
            </>
          ) : (
            `Selected: ${selectedItems.map(value => {
              // Find the option to get display name
              const option = options.find(opt => getOptionValue(opt) === value);
              return option ? (typeof option === 'string' ? option : option.name) : value;
            }).join(', ')}`
          )}
        </div>
      )}

      {/* Options list */}
      <div className={`mt-2 space-y-2 overflow-y-auto ${maxHeight} ${
        searchable ? 'rounded border p-2' : ''
      }`}>
        {filteredOptions.length === 0 ? (
          searchable && (
            <div className="py-2 text-center text-muted-foreground text-sm">
              No {label.toLowerCase()} found
            </div>
          )
        ) : (
          filteredOptions.map((option) => {
            const value = getOptionValue(option);
            const display = getOptionDisplay(option);
            return (
              <div className="flex items-center space-x-2" key={value}>
                <Checkbox
                  checked={selectedItems.includes(value)}
                  disabled={disabled}
                  id={`${paramName}-${value}`}
                  onCheckedChange={(checked) =>
                    handleToggle(value, checked as boolean)
                  }
                />
                <Label
                  className="cursor-pointer font-normal text-sm"
                  htmlFor={`${paramName}-${value}`}
                >
                  {display}
                </Label>
              </div>
            );
          })
        )}
      </div>
    </div>
  );
}