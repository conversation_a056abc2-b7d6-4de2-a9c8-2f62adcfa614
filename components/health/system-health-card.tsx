'use client';

import { Bot, Database, MessageSquare, Server, Zap } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  type HealthStatus,
  type HealthCheck,
  HealthStatusBadge,
  HealthCheckRow,
  HealthCardLoading,
  formatUptime,
  getOverallHealthStatus,
} from '@/lib/utils/health-status';

interface SystemHealthData {
  status: HealthStatus;
  timestamp: string;
  version: string;
  environment: string;
  checks: {
    database: {
      status: string;
      latency: number;
      error?: string;
    };
    workflow: {
      status: string;
      latency: number;
      error?: string;
    };
    apify: {
      status: string;
      latency: number;
      error?: string;
    };
    slack: {
      status: string;
      latency: number;
      error?: string;
    };
    uptime: number;
    responseTime?: number;
  };
}

interface SystemHealthCardProps {
  data?: SystemHealthData;
}

export function SystemHealthCard({ data }: SystemHealthCardProps) {
  if (!data?.checks) {
    return (
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="font-medium text-sm">System Health</CardTitle>
        </CardHeader>
        <CardContent>
          <HealthCardLoading title="System Health" />
        </CardContent>
      </Card>
    );
  }

  // Convert check data to HealthCheck format
  const healthChecks: HealthCheck[] = [
    {
      name: 'Database',
      status: data.checks.database.status as HealthStatus,
      latency: data.checks.database.latency,
      error: data.checks.database.error,
    },
    {
      name: 'Workflow', 
      status: data.checks.workflow.status as HealthStatus,
      latency: data.checks.workflow.latency,
      error: data.checks.workflow.error,
    },
    {
      name: 'Apify',
      status: data.checks.apify.status as HealthStatus, 
      latency: data.checks.apify.latency,
      error: data.checks.apify.error,
    },
    {
      name: 'Slack',
      status: data.checks.slack.status as HealthStatus,
      latency: data.checks.slack.latency, 
      error: data.checks.slack.error,
    },
  ];

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center gap-2 font-medium text-sm">
          <Server className="h-4 w-4" />
          System Health
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center gap-2">
          <HealthStatusBadge status={data.status} />
        </div>

        <div className="space-y-3">
          <HealthCheckRow 
            check={healthChecks[0]} 
            icon={<Database className="h-4 w-4 text-blue-500" />}
          />
          <HealthCheckRow 
            check={healthChecks[1]} 
            icon={<Zap className="h-4 w-4 text-purple-500" />}
          />
          <HealthCheckRow 
            check={healthChecks[2]} 
            icon={<Bot className="h-4 w-4 text-green-500" />}
          />
          <HealthCheckRow 
            check={healthChecks[3]} 
            icon={<MessageSquare className="h-4 w-4 text-indigo-500" />}
          />

          <div className="border-t pt-2">
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Uptime</span>
              <span>{formatUptime(data.checks.uptime)}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Version</span>
              <span>{data.version}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Environment</span>
              <span className="capitalize">{data.environment}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
