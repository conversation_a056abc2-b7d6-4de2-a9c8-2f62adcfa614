'use client';

import type { ColumnDef } from '@tanstack/react-table';
import {
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { Brain, Eye, Globe, RefreshCw, Search } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { toast } from 'sonner';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { DataTableBase } from '@/components/ui/data-table-base';

type MonitorLog = {
  id: string;
  job_id: string | null;
  previous_status: string | null;
  new_status: string | null;
  checked_at: string | null;
  duration_millis: number | null;
  cost_input_tokens: number | null;
  cost_output_tokens: number | null;
  total_tokens: number | null;
  model: string | null;
  head_status: number | null;
  head_ok: boolean | null;
  decision_layer: string | null;
};

type JobMonitoringLogProps = {
  jobId: string;
  job?: {
    last_checked_at?: string;
    monitor_attempts?: number;
    status?: string;
  };
};

export function JobMonitoringLog({ jobId, job }: JobMonitoringLogProps) {
  const [logs, setLogs] = useState<MonitorLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const [monitoring, setMonitoring] = useState<{
    head: boolean;
    full: boolean;
    ai: boolean;
  }>({
    head: false,
    full: false,
    ai: false,
  });

  const fetchLogs = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/jobs/${jobId}/monitor-logs`);

      if (!response.ok) {
        throw new Error(`Failed to fetch logs: ${response.status}`);
      }

      const data = await response.json();
      setLogs(data.logs || []);
      setError('');
    } catch (err) {
      setError(
        err instanceof Error ? err.message : 'Failed to load monitoring logs'
      );
    } finally {
      setLoading(false);
    }
  }, [jobId]);

  useEffect(() => {
    fetchLogs();
  }, [fetchLogs]);

  const runMonitoring = async (type: 'head' | 'full' | 'ai') => {
    try {
      setMonitoring((prev) => ({ ...prev, [type]: true }));

      const response = await fetch(`/api/jobs/${jobId}/monitor`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ type }),
      });

      if (!response.ok) {
        throw new Error(`Failed to run ${type} monitoring: ${response.status}`);
      }

      const result = await response.json();

      toast.success(`${type.toUpperCase()} monitoring completed`, {
        description: `Status: ${result.status || 'unknown'} (${
          result.confidence ? Math.round(result.confidence * 100) : 0
        }% confidence)`,
      });

      // Refresh logs after monitoring
      await fetchLogs();
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : `Failed to run ${type} monitoring`;
      toast.error('Monitoring failed', {
        description: errorMessage,
      });
    } finally {
      setMonitoring((prev) => ({ ...prev, [type]: false }));
    }
  };

  const getDecisionLayerIcon = (layer: string | null) => {
    switch (layer) {
      case 'head':
        return <Globe className="h-4 w-4 text-blue-500" />;
      case 'phrase':
        return <Eye className="h-4 w-4 text-orange-500" />;
      case 'ai':
        return <Brain className="h-4 w-4 text-purple-500" />;
      default:
        return <div className="h-4 w-4" />;
    }
  };

  const getStatusBadgeColor = (status: string | null) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'closed':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'filled':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'unknown':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatTimestamp = (timestamp: string | null) => {
    if (!timestamp) {
      return '-';
    }
    return new Date(timestamp).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false,
    });
  };

  const formatDuration = (ms: number | null) => {
    if (!ms) {
      return '-';
    }
    if (ms < 1000) {
      return `${ms}ms`;
    }
    return `${(ms / 1000).toFixed(2)}s`;
  };

  // Reuse shared DataTable components like Jobs table
  const columns: ColumnDef<MonitorLog>[] = useMemo(
    () => [
      {
        accessorKey: 'checked_at',
        header: 'Checked At',
        cell: ({ row }) => (
          <div className="text-foreground text-sm">
            {formatTimestamp(row.original.checked_at)}
          </div>
        ),
        meta: { className: 'min-w-[180px]' },
      },
      {
        id: 'status_change',
        header: 'Status Change',
        cell: ({ row }) => (
          <div className="flex items-center space-x-2">
            {row.original.previous_status && (
              <>
                <Badge
                  className={getStatusBadgeColor(row.original.previous_status)}
                >
                  {row.original.previous_status}
                </Badge>
                <span className="text-muted-foreground">→</span>
              </>
            )}
            <Badge className={getStatusBadgeColor(row.original.new_status)}>
              {row.original.new_status}
            </Badge>
          </div>
        ),
        meta: { className: 'min-w-[220px]' },
      },
      {
        accessorKey: 'decision_layer',
        header: 'Decision Layer',
        cell: ({ row }) => (
          <div className="flex items-center space-x-2">
            {getDecisionLayerIcon(row.original.decision_layer)}
            <span className="text-foreground text-sm capitalize">
              {row.original.decision_layer}
            </span>
          </div>
        ),
        meta: { className: 'min-w-[160px]' },
      },
      {
        accessorKey: 'duration_millis',
        header: 'Duration',
        cell: ({ row }) => (
          <div className="text-foreground text-sm">
            {formatDuration(row.original.duration_millis)}
          </div>
        ),
        meta: { className: 'min-w-[120px]' },
      },
      {
        id: 'http_status',
        header: 'HTTP Status',
        cell: ({ row }) => (
          <div className="flex items-center space-x-1 text-sm">
            <span>{row.original.head_ok ? '✅' : '❌'}</span>
            <span className="text-foreground">
              {row.original.head_status || '-'}
            </span>
          </div>
        ),
        meta: { className: 'min-w-[120px]' },
      },
      {
        accessorKey: 'total_tokens',
        header: 'AI Tokens',
        cell: ({ row }) => (
          <div className="text-foreground text-sm">
            {row.original.total_tokens
              ? row.original.total_tokens.toLocaleString()
              : '-'}
          </div>
        ),
        meta: { className: 'min-w-[120px] text-right' },
      },
      {
        accessorKey: 'model',
        header: 'Model',
        cell: ({ row }) => (
          <div className="flex items-center space-x-1">
            {row.original.model &&
              row.original.model !== 'heuristic' &&
              row.original.model !== 'error' && (
                <Brain className="h-3 w-3 text-purple-500" />
              )}
            <span className="text-foreground text-sm">
              {row.original.model || '-'}
            </span>
          </div>
        ),
        meta: { className: 'min-w-[140px]' },
      },
    ],
    [formatDuration, formatTimestamp, getDecisionLayerIcon, getStatusBadgeColor]
  );

  const table = useReactTable({
    data: logs,
    columns,
    state: {},
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  if (loading) {
    return (
      <div className="rounded-lg border bg-card text-card-foreground shadow">
        <div className="border-border border-b px-6 py-4">
          <h2 className="font-semibold text-foreground text-lg">
            Job Monitoring Log
          </h2>
        </div>
        <div className="p-6">
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin text-muted-foreground" />
            <span className="ml-2 text-muted-foreground">
              Loading monitoring logs...
            </span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-lg border bg-card text-card-foreground shadow">
        <div className="border-border border-b px-6 py-4">
          <h2 className="font-semibold text-foreground text-lg">
            Job Monitoring Log
          </h2>
        </div>
        <div className="p-6">
          <div className="py-8 text-center">
            <p className="text-destructive">{error}</p>
            <Button
              className="mt-4"
              onClick={fetchLogs}
              size="sm"
              variant="outline"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Retry
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const noDataMessage = job?.last_checked_at
    ? 'No detailed monitoring logs available.'
    : 'No monitoring logs found for this job.';

  return (
    <div className="rounded-lg border bg-card text-card-foreground shadow">
      <div className="border-border border-b px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="font-semibold text-foreground text-lg">
              Job Monitoring Log
            </h2>
            <p className="mt-1 text-muted-foreground text-sm">
              Detailed monitoring history showing status checks and decision
              layers.
            </p>
          </div>
        </div>
      </div>
      <div className="p-6">
        <DataTableBase
          customActions={
            <div className="flex items-center gap-2">
              <Button
                disabled={monitoring.head}
                onClick={() => runMonitoring('head')}
                size="sm"
                variant="outline"
              >
                {monitoring.head ? (
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Globe className="mr-2 h-4 w-4" />
                )}
                HEAD Check
              </Button>
              <Button
                disabled={monitoring.full}
                onClick={() => runMonitoring('full')}
                size="sm"
                variant="outline"
              >
                {monitoring.full ? (
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Search className="mr-2 h-4 w-4" />
                )}
                Full Check
              </Button>
              <Button
                disabled={monitoring.ai}
                onClick={() => runMonitoring('ai')}
                size="sm"
                variant="outline"
              >
                {monitoring.ai ? (
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Brain className="mr-2 h-4 w-4" />
                )}
                AI Check
              </Button>
            </div>
          }
          filterColumn="model"
          filterPlaceholder="Filter logs..."
          loading={loading}
          noDataIcon={<Eye />}
          noDataMessage={noDataMessage}
          onRefresh={fetchLogs}
          refreshLabel="Refresh logs"
          showPagination
          showSelection={false}
          table={table}
        />
      </div>
    </div>
  );
}
