import type { Country } from './data/countries';

// Core workplace types
export const WORKPLACE_TYPES = [
  'On-site',
  'Hybrid',
  'Remote',
  'Not specified',
] as const;

export type WorkplaceType = (typeof WORKPLACE_TYPES)[number];

// Remote region restrictions - comprehensive global coverage
export const REMOTE_REGIONS = [
  'Worldwide',
  'Americas Only',
  'Europe Only',
  'Asia-Pacific Only',
  'US Only',
  'EU Only',
  'UK/EU Only',
  'US/Canada Only',
  'LATAM Only',
  'EMEA Only',
  'APAC Only',
  'English Speaking Only',
  'Same Timezone Only',
] as const;

export type RemoteRegion = (typeof REMOTE_REGIONS)[number];

// Timezone requirements (common patterns)
export const TIMEZONE_REQUIREMENTS = [
  'UTC',
  'EST/EDT',
  'PST/PDT',
  'CET/CEST',
  'GMT/BST',
  'JST',
  'AEST/AEDT',
  'IST',
  'CST',
  'MST/MDT',
  'Flexible',
  'Core hours overlap required',
  'Business hours availability',
  'Not specified',
] as const;

export type TimezoneRequirement = (typeof TIMEZONE_REQUIREMENTS)[number];

// Workplace flexibility levels
export const FLEXIBILITY_LEVELS = [
  'Fully Remote',
  'Remote First',
  'Hybrid Flexible',
  'Hybrid Required',
  'Office First',
  'Office Required',
  'Location Dependent',
  'Not specified',
] as const;

export type FlexibilityLevel = (typeof FLEXIBILITY_LEVELS)[number];

// Work arrangement details
export type WorkplaceSettings = {
  workplace_type: WorkplaceType;
  remote_region: RemoteRegion | null;
  timezone_requirements: string | null;
  workplace_city: string | null;
  workplace_country: Country | null;
  flexibility_level?: FlexibilityLevel | null;
  office_days_per_week?: number | null; // For hybrid roles
  travel_required?: boolean | null;
  travel_percentage?: number | null; // 0-100%
};

// Simplified status for filtering/display
export type RemoteFriendly = 'Yes' | 'No' | 'Hybrid' | 'Not specified';

export function getRemoteFriendlyStatus(
  settings: Pick<WorkplaceSettings, 'workplace_type'>
): RemoteFriendly {
  switch (settings.workplace_type) {
    case 'Remote':
      return 'Yes';
    case 'Hybrid':
      return 'Hybrid';
    case 'On-site':
      return 'No';
    default:
      return 'Not specified';
  }
}

// Enhanced workplace analysis
export function getWorkplaceFlexibility(settings: WorkplaceSettings): {
  isRemoteFriendly: boolean;
  hasLocationRestrictions: boolean;
  hasTimezoneRestrictions: boolean;
  travelRequired: boolean;
  flexibilityScore: number; // 0-100
} {
  const isRemoteFriendly =
    settings.workplace_type === 'Remote' ||
    settings.workplace_type === 'Hybrid';
  const hasLocationRestrictions =
    settings.remote_region !== null && settings.remote_region !== 'Worldwide';
  const hasTimezoneRestrictions = settings.timezone_requirements !== null;
  const travelRequired = settings.travel_required === true;

  // Calculate flexibility score
  let flexibilityScore = 0;

  if (settings.workplace_type === 'Remote') {
    flexibilityScore += 40;
  } else if (settings.workplace_type === 'Hybrid') {
    flexibilityScore += 25;
  } else if (settings.workplace_type === 'On-site') {
    flexibilityScore += 5;
  }

  if (settings.remote_region === 'Worldwide') {
    flexibilityScore += 30;
  } else if (settings.remote_region === null) {
    flexibilityScore += 20;
  } else {
    flexibilityScore += 10;
  }

  if (hasTimezoneRestrictions) {
    flexibilityScore += 5;
  } else {
    flexibilityScore += 15;
  }

  if (travelRequired) {
    flexibilityScore += 2;
  } else {
    flexibilityScore += 10;
  }

  if (
    settings.office_days_per_week !== null &&
    settings.office_days_per_week !== undefined &&
    settings.office_days_per_week <= 2
  ) {
    flexibilityScore += 5;
  }

  return {
    isRemoteFriendly,
    hasLocationRestrictions,
    hasTimezoneRestrictions,
    travelRequired,
    flexibilityScore: Math.min(100, flexibilityScore),
  };
}

// Regional mapping helpers
export function getRegionalRestrictions(remoteRegion: RemoteRegion | null): {
  allowedContinents: string[];
  allowedCountries: string[];
  restrictions: string[];
} {
  if (!remoteRegion || remoteRegion === 'Worldwide') {
    return {
      allowedContinents: ['All'],
      allowedCountries: ['All'],
      restrictions: [],
    };
  }

  const mapping: Record<
    RemoteRegion,
    { continents: string[]; countries: string[]; restrictions: string[] }
  > = {
    Worldwide: { continents: ['All'], countries: ['All'], restrictions: [] },
    'Americas Only': {
      continents: ['North America', 'South America'],
      countries: [],
      restrictions: ['Visa/legal restrictions may apply'],
    },
    'Europe Only': {
      continents: ['Europe'],
      countries: [],
      restrictions: ['EU work authorization may be required'],
    },
    'Asia-Pacific Only': {
      continents: ['Asia', 'Oceania'],
      countries: [],
      restrictions: ['Regional work authorization required'],
    },
    'US Only': {
      continents: [],
      countries: ['United States'],
      restrictions: ['US work authorization required'],
    },
    'EU Only': {
      continents: [],
      countries: ['EU Member States'],
      restrictions: ['EU citizenship or work permit required'],
    },
    'UK/EU Only': {
      continents: [],
      countries: ['United Kingdom', 'EU Member States'],
      restrictions: ['UK/EU work authorization required'],
    },
    'US/Canada Only': {
      continents: [],
      countries: ['United States', 'Canada'],
      restrictions: ['North American work authorization required'],
    },
    'LATAM Only': {
      continents: [],
      countries: ['Latin America'],
      restrictions: ['Regional presence preferred'],
    },
    'EMEA Only': {
      continents: ['Europe', 'Middle East', 'Africa'],
      countries: [],
      restrictions: ['EMEA timezone overlap required'],
    },
    'APAC Only': {
      continents: ['Asia', 'Oceania'],
      countries: [],
      restrictions: ['APAC timezone overlap required'],
    },
    'English Speaking Only': {
      continents: [],
      countries: ['English-speaking countries'],
      restrictions: ['Native/fluent English required'],
    },
    'Same Timezone Only': {
      continents: [],
      countries: [],
      restrictions: ['Must be in company timezone'],
    },
  };

  const config = mapping[remoteRegion];
  return {
    allowedContinents: config.continents,
    allowedCountries: config.countries,
    restrictions: config.restrictions,
  };
}

// UI helpers
export function getWorkplaceTypeDisplayName(type: WorkplaceType): string {
  const displayNames: Record<WorkplaceType, string> = {
    'On-site': 'On-site',
    Hybrid: 'Hybrid',
    Remote: 'Remote',
    'Not specified': 'Not specified',
  };
  return displayNames[type];
}

export function getRemoteRegionDisplayName(
  region: RemoteRegion | null
): string {
  if (!region) {
    return 'No restrictions';
  }

  const displayNames: Record<RemoteRegion, string> = {
    Worldwide: 'Worldwide',
    'Americas Only': 'Americas Only',
    'Europe Only': 'Europe Only',
    'Asia-Pacific Only': 'Asia-Pacific Only',
    'US Only': 'US Only',
    'EU Only': 'EU Only',
    'UK/EU Only': 'UK/EU Only',
    'US/Canada Only': 'US/Canada Only',
    'LATAM Only': 'Latin America Only',
    'EMEA Only': 'EMEA Only',
    'APAC Only': 'APAC Only',
    'English Speaking Only': 'English Speaking Countries Only',
    'Same Timezone Only': 'Same Timezone Only',
  };
  return displayNames[region];
}

/**
 * Get the display value for a workplace type
 */
export function getWorkplaceTypeDisplay(type: WorkplaceType): string {
  return type.charAt(0).toUpperCase() + type.slice(1).replace('_', '-');
}
