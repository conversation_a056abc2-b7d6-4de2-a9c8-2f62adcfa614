import { type NextRequest, NextResponse } from 'next/server';
import { handleApiError } from '@/lib/api-utils';
import {
  getScheduleStatus,
  toggleAutomaticSchedule,
} from '@/lib/qstash-scheduler';
import { logger } from '@/lib/utils';

/**
 * GET /api/workflows/schedule
 * Get current automatic schedule status
 */
export async function GET() {
  try {
    logger.info('Getting automatic schedule status');

    const status = await getScheduleStatus();

    return NextResponse.json({
      success: true,
      enabled: status.exists,
      schedule: status.schedule,
      error: status.error,
    });
  } catch (error) {
    logger.error('Failed to get schedule status', { error });
    return handleApiError(error);
  }
}

/**
 * POST /api/workflows/schedule
 * Toggle automatic schedule (enable/disable)
 */
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { action } = body;

    logger.info('Toggling automatic schedule', { action });

    // If specific action provided, validate it
    if (action && !['enable', 'disable', 'toggle'].includes(action)) {
      return NextResponse.json(
        { error: 'Invalid action. Use "enable", "disable", or "toggle"' },
        { status: 400 }
      );
    }

    // For now, we'll always toggle. In the future, we can handle specific actions
    const result = await toggleAutomaticSchedule();

    if (!result.success) {
      return NextResponse.json(
        {
          success: false,
          error: result.error || 'Failed to toggle schedule',
        },
        { status: 500 }
      );
    }

    const message =
      result.action === 'created'
        ? 'Automatic job processing enabled'
        : 'Automatic job processing disabled';

    logger.info('Schedule toggled successfully', {
      action: result.action,
      message,
    });

    return NextResponse.json({
      success: true,
      action: result.action,
      enabled: result.action === 'created',
      message,
    });
  } catch (error) {
    logger.error('Failed to toggle schedule', { error });
    return handleApiError(error);
  }
}
