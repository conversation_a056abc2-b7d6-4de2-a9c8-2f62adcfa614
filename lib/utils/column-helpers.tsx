import type { ColumnDef } from '@tanstack/react-table';
import { ArrowUpDown } from 'lucide-react';
import type { ReactElement } from 'react';
import { Button } from '@/components/ui/button';

export type SortableColumnOptions = {
  icon?: ReactElement;
  className?: string;
};

export function createSortableColumn<TData>(
  accessorKey: string,
  title: string,
  options: SortableColumnOptions = {}
): Partial<ColumnDef<TData>> {
  const { icon, className } = options;

  return {
    accessorKey,
    header: ({ column }) => (
      <Button
        className="h-8 px-2"
        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        variant="ghost"
      >
        {icon && <span className="mr-2">{icon}</span>}
        {title}
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    ...(className && {
      meta: {
        className,
      },
    }),
  };
}

export function createTextColumn<TData>(
  accessorKey: string,
  title: string,
  cellFormatter?: (value: unknown) => React.ReactNode
): Partial<ColumnDef<TData>> {
  return {
    accessorKey,
    header: title,
    cell: ({ row }) => {
      const value = row.getValue(accessorKey);
      return cellFormatter ? cellFormatter(value) : String(value || '');
    },
  };
}

export type NumberColumnOptions = {
  formatter?: (value: number) => string;
  className?: string;
  fallback?: string;
};

export function createNumberColumn<TData>(
  accessorKey: string,
  title: string,
  options: NumberColumnOptions = {}
): Partial<ColumnDef<TData>> {
  const { formatter, className = 'text-right', fallback = '-' } = options;

  return {
    accessorKey,
    header: title,
    cell: ({ row }) => {
      const value = row.getValue(accessorKey) as number | null | undefined;
      if (value === null || value === undefined) {
        return <span className="text-muted-foreground">{fallback}</span>;
      }
      const formattedValue = formatter ? formatter(value) : String(value);
      return <div className={className}>{formattedValue}</div>;
    },
    ...(className && {
      meta: {
        className,
      },
    }),
  };
}
