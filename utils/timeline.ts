import { getSourceDisplayName } from '@/lib/job-board-constants';
import type { DatabaseJob } from '@/lib/storage';
import type { TimelineActivity } from '@/types/timeline';

// Helper function to format time units
const formatTimeUnit = (value: number, unit: string): string => {
  return `${value} ${unit}${value === 1 ? '' : 's'} ago`;
};

// Time intervals in seconds
const TIME_INTERVALS = [
  { threshold: 60, divisor: 1, unit: 'second', label: 'just now' },
  { threshold: 3600, divisor: 60, unit: 'minute', label: undefined },
  { threshold: 86_400, divisor: 3600, unit: 'hour', label: undefined },
  {
    threshold: 2_592_000,
    divisor: 86_400,
    unit: 'day',
    label: undefined,
  },
  {
    threshold: 31_536_000,
    divisor: 2_592_000,
    unit: 'month',
    label: undefined,
  },
  {
    threshold: Number.POSITIVE_INFINITY,
    divisor: 31_536_000,
    unit: 'year',
    label: undefined,
  },
] as const;

// Format relative time (e.g., "2 hours ago", "3 days ago")
export const formatRelativeTime = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  for (const interval of TIME_INTERVALS) {
    if (diffInSeconds < interval.threshold) {
      if (interval.label) {
        return interval.label;
      }
      const value = Math.floor(diffInSeconds / interval.divisor);
      return formatTimeUnit(value, interval.unit);
    }
  }

  // Fallback (should never reach here due to Infinity threshold)
  return 'unknown time ago';
};

// Helper function to create timeline activity
const createActivity = (
  id: number,
  type: TimelineActivity['type'],
  personName: string,
  dateTime: string,
  comment?: string
): TimelineActivity => ({
  id,
  type,
  person: { name: personName },
  date: formatRelativeTime(dateTime),
  dateTime,
  comment,
});

// Timeline event configuration
type TimelineEventConfig = {
  type: TimelineActivity['type'];
  personName: string;
  condition: (job: DatabaseJob) => boolean;
  getDateTime: (job: DatabaseJob) => string;
};

// Type alias for timeline events
type TimelineEvent = TimelineEventConfig;

const timelineEvents: TimelineEventConfig[] = [
  {
    type: 'created',
    personName: 'System',
    condition: (job) =>
      !!job.created_at &&
      (!job.raw_data_fetched_at || job.raw_data_fetched_at !== job.created_at),
    getDateTime: (job) => job.created_at,
  },
  {
    type: 'fetched',
    personName: 'Job Scraper',
    condition: (job) => !!job.raw_data_fetched_at,
    getDateTime: (job) => job.raw_data_fetched_at || new Date().toISOString(),
  },
  {
    type: 'processed',
    personName: 'AI Processor',
    condition: (job) =>
      job.processing_status === 'completed' &&
      job.updated_at !== job.created_at,
    getDateTime: (job) => job.updated_at,
  },
  {
    type: 'synced',
    personName: 'Airtable Sync',
    condition: (job) => !!job.airtable_synced_at,
    getDateTime: (job) => job.airtable_synced_at || new Date().toISOString(),
  },
  {
    type: 'posted',
    personName: 'Job Board Publisher',
    condition: (job) => !!job.last_posted_at,
    getDateTime: (job) => job.last_posted_at || new Date().toISOString(),
  },
  {
    type: 'monitored',
    personName: 'Monitor Service',
    condition: (job) => !!job.last_checked_at,
    getDateTime: (job) => job.last_checked_at || new Date().toISOString(),
  },
  {
    type: 'updated',
    personName: 'System',
    condition: (job) => !!job.updated_at && job.updated_at !== job.created_at,
    getDateTime: (job) => job.updated_at,
  },
];

/**
 * Get the person name for a timeline event
 */
function getEventPersonName(event: TimelineEvent, job: DatabaseJob): string {
  return event.type === 'fetched' && job.source_name
    ? job.source_name
    : event.personName;
}

/**
 * Generate comment for 'created' event type
 */
function generateCreatedComment(job: DatabaseJob): string {
  const sourceName =
    (job.source_name === 'manual_entry' || job.source_name === null) &&
    job.source_type
      ? getSourceDisplayName(job.source_type)
      : job.source_name;

  return sourceName
    ? `sourced the job from ${sourceName}`
    : 'created the job record';
}

/**
 * Generate comment for 'processed' event type with AI metadata
 */
function generateProcessedComment(job: DatabaseJob): string | undefined {
  // biome-ignore lint/suspicious/noExplicitAny: dynamic structure from JSONB
  const meta: any = job.ai_metadata || {};

  // Try common fields generated by generateMetadata()
  const tokens: number | undefined = meta?.usage?.totalTokens;
  const cost: number | undefined = meta?.cost?.total;
  const model: string | undefined = meta?.model || meta?.response?.model;
  const durationMs: number | undefined = meta?.duration;

  const details: string[] = [];
  if (model) {
    details.push(model);
  }
  if (typeof tokens === 'number') {
    details.push(`${tokens.toLocaleString()} tokens`);
  }
  if (typeof cost === 'number') {
    details.push(`$${cost.toFixed(6)}`);
  }
  if (typeof durationMs === 'number') {
    details.push(`${(durationMs / 1000).toFixed(2)}s`);
  }

  return details.length > 0
    ? `processed the job • ${details.join(' • ')}`
    : undefined;
}

/**
 * Generate comment for a timeline event
 */
function generateEventComment(
  event: TimelineEvent,
  job: DatabaseJob
): string | undefined {
  if (event.type === 'created') {
    return generateCreatedComment(job);
  }
  if (event.type === 'processed') {
    return generateProcessedComment(job);
  }
  return;
}

// Generate timeline activities from job data
export const generateTimelineActivities = (
  job: DatabaseJob
): TimelineActivity[] => {
  let activityId = 1;

  const activities = timelineEvents
    .filter((event) => event.condition(job))
    .map((event) => {
      const personName = getEventPersonName(event, job);
      const comment = generateEventComment(event, job);

      return createActivity(
        activityId++,
        event.type,
        personName,
        event.getDateTime(job),
        comment
      );
    });

  // Sort activities by date (newest first)
  return activities.sort(
    (a, b) => new Date(b.dateTime).getTime() - new Date(a.dateTime).getTime()
  );
};

// Utility function for combining class names
export function classNames(
  ...classes: (string | undefined | null | false)[]
): string {
  return classes.filter(Boolean).join(' ');
}
