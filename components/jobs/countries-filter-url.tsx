'use client';

import { MultiSelectUrlFilter } from '@/components/common/MultiSelectUrlFilter';
import { countries } from '@/lib/data/countries';

type CountriesFilterUrlProps = {
  disabled?: boolean;
};

/**
 * Countries filter component with URL state
 * Now uses the generic MultiSelectUrlFilter component
 */
export function CountriesFilterUrl({
  disabled = false,
}: CountriesFilterUrlProps) {
  return (
    <MultiSelectUrlFilter
      clearable={true}
      disabled={disabled}
      label="Countries"
      maxHeight="max-h-48"
      options={countries}
      paramName="countries"
      searchable={true}
      showCount={true}
    />
  );
}
