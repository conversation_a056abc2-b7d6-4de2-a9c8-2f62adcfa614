/**
 * Environment Configuration
 *
 * Environment validation, debug settings, and runtime configuration
 */

// Debug configuration
export const DEBUG_MODE = process.env.NODE_ENV === 'development';

// Default values for various operations
export const DEFAULTS = {
  SOURCE_URL: 'https://job-site.com/posting',
  READING_WPM: 200, // words per minute for reading time estimation
} as const;

/**
 * Parse boolean-like environment variables safely.
 * Accepts: '1', 'true', 'yes', 'on' (case-insensitive) as true.
 */
export function envFlag(name: string, defaultValue: boolean): boolean {
  const raw = process.env[name];
  if (raw === undefined) {
    return defaultValue;
  }
  const normalized = String(raw).trim().toLowerCase();
  return (
    normalized === '1' ||
    normalized === 'true' ||
    normalized === 'yes' ||
    normalized === 'on'
  );
}

// Validate all required environment variables
function validateEnvVars() {
  const required = [
    'QSTASH_TOKEN', // Still needed for Workflow client
    'SLACK_WEBHOOK_URL',
  ];

  const missing = required.filter((key) => !process.env[key]);
  if (missing.length > 0) {
    if (DEBUG_MODE) {
      return false; // Don't throw in development
    }
    // Log warning instead of throwing error to prevent client-side crashes
    // Don't import logger here to avoid circular dependencies - use console in this critical case
    // biome-ignore lint/suspicious/noConsole: Critical environment validation requires console output
    console.warn(
      `⚠️ Missing environment variables: ${missing.join(
        ', '
      )}. Some features may not work properly.`
    );
    return false;
  }
  return true;
}

// Environment status check
export const ENV_STATUS = {
  isFullyConfigured: validateEnvVars(),
  isDevelopment: DEBUG_MODE,
  isProduction: process.env.NODE_ENV === 'production',
  nodeVersion: process.version,
  missingVars: (() => {
    const required = [
      'QSTASH_TOKEN', // Still needed for Workflow client
      'SLACK_WEBHOOK_URL',
    ];
    return required.filter((key) => !process.env[key]);
  })(),
};

// Upstash configuration with fallbacks for development
export const UPSTASH_CONFIG = {
  qstash: {
    token: process.env.QSTASH_TOKEN || '', // Still used by Workflow client
  },
};

// Export validation function for manual use
export { validateEnvVars };

// Get comprehensive environment info for debugging
export function getEnvironmentInfo() {
  return {
    nodeEnv: process.env.NODE_ENV,
    nodeVersion: process.version,
    debugMode: DEBUG_MODE,
    isConfigured: ENV_STATUS.isFullyConfigured,
    missingVars: ENV_STATUS.missingVars,
    hasRequiredServices: {
      qstash: !!process.env.QSTASH_TOKEN,
      slack: !!process.env.SLACK_WEBHOOK_URL,
      openai: !!process.env.OPENAI_API_KEY,
      supabase:
        !!process.env.NEXT_PUBLIC_SUPABASE_URL &&
        !!process.env.SUPABASE_SERVICE_ROLE_KEY,
    },
  };
}
