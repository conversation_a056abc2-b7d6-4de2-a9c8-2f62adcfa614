/**
 * Job Constants
 * 
 * Consolidated job-related constants including types, statuses, 
 * career levels, workplace settings, and job board configuration
 */

import { CAREER_LEVELS } from '../career-levels';
import { CURRENCY_CODES } from '../data/currencies';
import { VISA_SPONSORSHIP_OPTIONS } from '../job-status';
import { JOB_TYPES } from '../job-types';
import { REMOTE_REGIONS, WORKPLACE_TYPES } from '../workplace';

// Re-export all job-related constants for centralized access
export { CAREER_LEVELS } from '../career-levels';
export { JOB_TYPES } from '../job-types';
export { WORKPLACE_TYPES, REMOTE_REGIONS } from '../workplace';
export { VISA_SPONSORSHIP_OPTIONS } from '../job-status';
export { CURRENCY_CODES } from '../data/currencies';

// Posting strategies for job boards
export const POSTING_STRATEGIES = [
  'newest_first',
  'best_match',
  'random',
] as const;

export type PostingStrategy = (typeof POSTING_STRATEGIES)[number];

export const POSTING_STRATEGY_DESCRIPTIONS: Record<PostingStrategy, string> = {
  newest_first: 'Post newest jobs first',
  best_match: 'Post jobs with highest relevance score first',
  random: 'Post jobs in random order',
} as const;

// Job source types for prioritization
export const JOB_SOURCES = [
  'wwr_rss',
  'jobdata_api',
  'workable',
  'manual_entry',
  'api_import',
  'csv_import',
] as const;

export type JobSource = (typeof JOB_SOURCES)[number];

export const JOB_SOURCE_DESCRIPTIONS: Record<JobSource, string> = {
  wwr_rss: 'We Work Remotely RSS Feed',
  jobdata_api: 'JobDataAPI.com Jobs',
  workable: 'Workable',
  manual_entry: 'Manually entered jobs',
  api_import: 'Third-party API imports',
  csv_import: 'CSV file imports',
} as const;

// Helper function to get proper display name for source
export const getSourceDisplayName = (sourceType: string | null): string => {
  if (!sourceType) {
    return 'Unknown Source';
  }

  // Check if we have a mapping for this source type
  if (sourceType in JOB_SOURCE_DESCRIPTIONS) {
    return JOB_SOURCE_DESCRIPTIONS[
      sourceType as keyof typeof JOB_SOURCE_DESCRIPTIONS
    ];
  }

  // Fallback: convert snake_case to Title Case
  return sourceType
    .split('_')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

// Job board configuration interface
export interface JobBoardConfig {
  id: string;
  name: string;
  description: string;
  enabled: boolean;

  // Airtable settings
  airtable: {
    baseId: string;
    tableName: string;
    pat?: string; // Optional if using shared PAT
  };

  // Job selection criteria
  filters: {
    // Job types to include
    types?: string[]; // e.g., ["Full-time", "Contract"]

    // Workplace preferences
    workplaceTypes?: string[]; // e.g., ["Remote", "Hybrid"]
    remoteRegions?: string[]; // e.g., ["Worldwide", "US Only"]

    // Location filters
    countries?: string[]; // e.g., ["United States", "Canada"]

    // Career levels
    careerLevels?: string[]; // e.g., ["Senior", "Lead", "Principal"]

    // Salary ranges
    salaryMin?: number;
    salaryMax?: number;
    salaryCurrencies?: string[]; // e.g., ["USD", "EUR"]

    // Keywords for title/description matching
    includeKeywords?: string[]; // e.g., ["React", "Node.js"]
    excludeKeywords?: string[]; // e.g., ["PHP", "WordPress"]

    // Other criteria
    visaSponsorship?: string; // "Yes", "No", "Not specified"
    languages?: string[]; // e.g., ["en", "es"]
  };

  // Posting configuration
  posting: {
    strategy: PostingStrategy;
    maxJobsPerPost: number;
    postingInterval: number; // minutes between posts
    maxJobAge: number; // days - don't post jobs older than this
    duplicateWindow: number; // hours - prevent duplicate posts within this window
  };

  // Content customization
  content: {
    includeCompanyLogo: boolean;
    includeSalary: boolean;
    includeLocation: boolean;
    customFields?: Record<string, string>; // Custom field mappings
  };
}

// Default job board configuration
export const DEFAULT_JOB_BOARD_CONFIG: Omit<JobBoardConfig, 'id' | 'name' | 'airtable'> = {
  description: 'Default job board configuration',
  enabled: true,
  filters: {
    types: JOB_TYPES.slice(), // All job types
    workplaceTypes: WORKPLACE_TYPES.slice(), // All workplace types
    careerLevels: CAREER_LEVELS.slice(), // All career levels
  },
  posting: {
    strategy: 'newest_first',
    maxJobsPerPost: 10,
    postingInterval: 60, // 1 hour
    maxJobAge: 30, // 30 days
    duplicateWindow: 24, // 24 hours
  },
  content: {
    includeCompanyLogo: true,
    includeSalary: true,
    includeLocation: true,
  },
};

// Validation functions for job data
export function isValidJobSource(source: string): source is JobSource {
  return JOB_SOURCES.includes(source as JobSource);
}

export function isValidPostingStrategy(strategy: string): strategy is PostingStrategy {
  return POSTING_STRATEGIES.includes(strategy as PostingStrategy);
}

// Get all available options for dropdowns/filters
export function getJobFilterOptions() {
  return {
    types: JOB_TYPES,
    careerLevels: CAREER_LEVELS,
    workplaceTypes: WORKPLACE_TYPES,
    remoteRegions: REMOTE_REGIONS,
    currencies: CURRENCY_CODES,
    visaSponsorshipOptions: VISA_SPONSORSHIP_OPTIONS,
    sources: JOB_SOURCES,
    postingStrategies: POSTING_STRATEGIES,
  };
}