/**
 * AI Configuration
 *
 * Centralized configuration for AI services, models, and processing limits
 */

// AI Model configuration
export const AI_CONFIG = {
  MODEL: 'gpt-4o-mini',
  TEMPERATURE: 0.1,
  PRICING: {
    'gpt-4o-mini': { input: 0.15, output: 0.6 },
    'gpt-4o': { input: 2.5, output: 10.0 },
    heuristic: { input: 0, output: 0 }, // For non-AI monitoring methods
    error: { input: 0, output: 0 }, // For error cases
  },
} as const;

// Input processing limits
export const INPUT_LIMITS = {
  CONTENT_MAX_CHARS: 50_000,
  PROMPT_MAX_CHARS: 10_000,
} as const;

// Schema field character limits for job extraction
export const SCHEMA_LIMITS = {
  benefits: 1500,
  application_requirements: 1000,
  skills: 500,
  qualifications: 800,
  education_requirements: 500,
  experience_requirements: 800,
  responsibilities: 1000,
} as const;

// AI service configuration validation
export function validateAIConfig(): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (!process.env.OPENAI_API_KEY) {
    errors.push('OPENAI_API_KEY not configured');
  }

  if (!AI_CONFIG.MODEL) {
    errors.push('AI model not configured');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

// Get AI service status for health checks
export function getAIServiceStatus() {
  const validation = validateAIConfig();

  return {
    configured: validation.isValid,
    model: AI_CONFIG.MODEL,
    hasApiKey: !!process.env.OPENAI_API_KEY,
    apiKeyPrefix: process.env.OPENAI_API_KEY?.substring(0, 8) || 'missing',
    errors: validation.errors,
  };
}
