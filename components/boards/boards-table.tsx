'use client';

import {
  type ColumnDef,
  type ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  type SortingState,
  useReactTable,
  type VisibilityState,
} from '@tanstack/react-table';
import { Plus, RefreshCw } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import type {
  JobBoardTimezone,
  PostingStrategy,
} from '@/lib/job-board-constants';
import type { JobBoardConfig } from '@/lib/job-board-service';
import { useBoardsStoreHydrated } from '@/lib/stores';
import type { ExtendedJobBoard } from '@/lib/stores/boards.store';
import { DataTablePagination } from '../jobs/data-table-pagination';
import { DataTableViewOptions } from '../jobs/data-table-view-options';
import { createBoardsColumns, type JobBoard } from './columns';
import { CreateBoardDialog } from './create-board-dialog';
import { EditBoardDialog } from './edit-board-dialog';
import { PatManagementDialog } from './pat-management-dialog';

type BoardsTableProps = {
  onDataChange?: () => void; // Callback to notify parent of data changes
  onBoardsLoaded?: (boards: JobBoard[]) => void; // Callback to pass boards data to parent
};

export function BoardsTable({
  onDataChange,
  onBoardsLoaded,
}: BoardsTableProps) {
  // Use the Zustand store with hydration protection
  const { boards, loading, error, actions } = useBoardsStoreHydrated();

  const [sorting, setSorting] = useState<SortingState>([
    { id: 'name', desc: false },
  ]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});

  // Modal states
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showPatDialog, setShowPatDialog] = useState(false);
  const [selectedBoard, setSelectedBoard] = useState<JobBoard | null>(null);

  // Load boards on mount
  useEffect(() => {
    actions.fetch();
  }, [actions]);

  // Notify parent component when boards change
  useEffect(() => {
    if (onBoardsLoaded && boards.length > 0) {
      // Convert ExtendedJobBoard to JobBoard format expected by callback
      const convertedBoards: JobBoard[] = boards.map((board) => ({
        id: board.id,
        name: board.name,
        description: board.description || '', // Ensure description is never undefined
        enabled: board.enabled ?? false,
        airtable: board.airtable,
        posting: board.posting
          ? {
              ...board.posting,
              strategy: (board.posting.strategy ||
                'newest_first') as PostingStrategy,
              timezone: (board.posting.timezone || 'UTC') as JobBoardTimezone,
            }
          : {
              dailyLimit: 10,
              strategy: 'newest_first' as PostingStrategy,
              avoidRepostingDays: 30,
              timezone: 'UTC' as JobBoardTimezone,
            },
        filters: board.filters || {},
        patStatus:
          (board.patStatus === 'invalid' ? 'missing' : board.patStatus) ||
          'missing',
      }));
      onBoardsLoaded(convertedBoards);
    }
  }, [boards, onBoardsLoaded]);

  const createJobBoard = async (config: JobBoardConfig) => {
    const result = await actions.createBoard(config);

    if (result.success) {
      onDataChange?.(); // Notify parent of data change
      setShowCreateDialog(false);
      toast.success('Job board created successfully');
    } else {
      toast.error('Failed to create job board', {
        description: result.error || 'Unknown error',
      });
    }
  };

  // Update job board
  const updateJobBoard = useCallback(
    async (updatedBoard: JobBoard) => {
      const result = await actions.updateBoard(updatedBoard.id, updatedBoard);

      if (result.success) {
        onDataChange?.(); // Notify parent of data change
        toast.success('Job board updated successfully');
      } else {
        toast.error('Failed to update job board', {
          description: result.error || 'Unknown error',
        });
      }
    },
    [actions, onDataChange]
  );

  // Delete job board
  const deleteJobBoard = useCallback(
    async (id: string) => {
      if (
        !confirm(
          'Are you sure you want to delete this job board? This action cannot be undone.'
        )
      ) {
        return;
      }

      const result = await actions.deleteBoard(id);

      if (result.success) {
        onDataChange?.(); // Notify parent of data change
        toast.success('Job board deleted successfully');
      } else {
        toast.error('Failed to delete job board', {
          description: result.error || 'Unknown error',
        });
      }
    },
    [actions, onDataChange]
  );

  // Store PAT for a board
  const storePat = useCallback(
    async (boardId: string, pat: string) => {
      const result = await actions.configurePAT(boardId, pat);

      if (result.success) {
        setShowPatDialog(false);
        setSelectedBoard(null);
        toast.success('PAT configured successfully');
      } else {
        toast.error('Failed to store PAT', {
          description: result.error || 'Unknown error',
        });
      }
    },
    [actions]
  );

  // Test Airtable connection
  const testConnection = useCallback(
    async (board: JobBoard) => {
      const toastId = toast.loading('Testing Airtable connection...');

      const result = await actions.testConnection(board.id);

      if (result.success) {
        toast.success('Connection successful!', { id: toastId });
      } else {
        toast.error(result.error || 'Connection failed', { id: toastId });
      }
    },
    [actions]
  );

  // Send job to Airtable
  const _sendToAirtable = useCallback(
    async (boardId: string, jobData: Record<string, unknown>) => {
      const board = boards.find((b) => b.id === boardId);
      if (!board) {
        toast.error('Board not found');
        return;
      }

      const toastId = toast.loading(`Sending job to ${board.name}...`);

      const result = await actions.sendToAirtable(boardId, jobData);

      if (result.success) {
        toast.success('Job sent to Airtable!', {
          id: toastId,
          description: 'View in Airtable',
          action: result.recordUrl
            ? {
                label: 'View',
                onClick: () => window.open(result.recordUrl, '_blank'),
              }
            : undefined,
        });
      } else {
        toast.error(`Failed to send: ${result.error}`, {
          id: toastId,
        });
      }
    },
    [boards, actions]
  );

  const toggleJobBoard = useCallback(
    async (board: JobBoard) => {
      const newStatus = board.enabled ? 'inactive' : 'active';
      await actions.updateBoard(board.id, { enabled: !board.enabled });
      toast.success(`Board ${newStatus === 'active' ? 'activated' : 'paused'}`);
    },
    [actions]
  );

  // Create columns with action handlers
  const boardsColumns = useMemo(
    () =>
      createBoardsColumns(
        (board) => {
          setSelectedBoard(board);
          setShowEditDialog(true);
        }, // onEdit
        toggleJobBoard, // onToggle
        deleteJobBoard, // onDelete
        (board) => {
          setSelectedBoard(board);
          setShowPatDialog(true);
        }, // onManagePat
        testConnection // onTestConnection
      ),
    [testConnection, deleteJobBoard, toggleJobBoard]
  );

  const table = useReactTable({
    data: boards,
    columns: boardsColumns as ColumnDef<ExtendedJobBoard>[],
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  const refreshBoards = useCallback(async () => {
    const promise = actions.fetch();

    await toast.promise(promise, {
      loading: 'Refreshing boards...',
      success: 'Boards refreshed successfully!',
      error: 'Failed to refresh boards',
    });
  }, [actions]);

  if (loading && boards.length === 0) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-muted-foreground">Loading job boards...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-64 flex-col items-center justify-center space-y-4">
        <div className="text-destructive">Error: {error}</div>
        <Button onClick={refreshBoards} variant="outline">
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header with search and actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Input
            className="max-w-sm"
            onChange={(event) =>
              table.getColumn('name')?.setFilterValue(event.target.value)
            }
            placeholder="Filter boards..."
            value={(table.getColumn('name')?.getFilterValue() as string) ?? ''}
          />
          <Button onClick={refreshBoards} size="sm" variant="outline">
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
        </div>
        <div className="flex items-center space-x-2">
          <DataTableViewOptions table={table} />
          <Button onClick={() => setShowCreateDialog(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Create Job Board
          </Button>
        </div>
      </div>

      {/* Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  data-state={row.getIsSelected() && 'selected'}
                  key={row.id}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell className="min-w-0" key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  className="h-24 text-center"
                  colSpan={boardsColumns.length}
                >
                  <div className="flex flex-col items-center justify-center space-y-2">
                    <div className="text-2xl">🎯</div>
                    <div className="font-medium text-lg text-muted-foreground">
                      No job boards configured
                    </div>
                    <div className="text-muted-foreground text-sm">
                      Create your first job board to start automatically posting
                      jobs
                    </div>
                    <Button
                      className="mt-2"
                      onClick={() => setShowCreateDialog(true)}
                    >
                      Create Your First Job Board
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <DataTablePagination table={table} />

      {/* Modal Dialogs */}
      <CreateBoardDialog
        onClose={() => setShowCreateDialog(false)}
        onSubmit={createJobBoard}
        open={showCreateDialog}
      />

      {selectedBoard && (
        <>
          <EditBoardDialog
            board={selectedBoard}
            onClose={() => {
              setShowEditDialog(false);
              setSelectedBoard(null);
            }}
            onSubmit={updateJobBoard}
            open={showEditDialog}
          />

          <PatManagementDialog
            board={selectedBoard}
            onClose={() => {
              setShowPatDialog(false);
              setSelectedBoard(null);
            }}
            onSubmit={(pat) => storePat(selectedBoard.id, pat)}
            open={showPatDialog}
          />
        </>
      )}
    </div>
  );
}
