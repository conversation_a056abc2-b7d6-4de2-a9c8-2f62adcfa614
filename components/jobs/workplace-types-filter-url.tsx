'use client';

import { MultiSelectUrlFilter } from '@/components/common/MultiSelectUrlFilter';
import { WORKPLACE_TYPES } from '@/lib/workplace';

interface WorkplaceTypesFilterUrlProps {
  disabled?: boolean;
}

/**
 * Workplace Types filter component with URL state
 * Now uses the generic MultiSelectUrlFilter component
 */
export function WorkplaceTypesFilterUrl({
  disabled = false,
}: WorkplaceTypesFilterUrlProps) {
  return (
    <MultiSelectUrlFilter
      label="Workplace Types"
      paramName="workplace"
      options={WORKPLACE_TYPES}
      disabled={disabled}
      searchable={false}
      clearable={false}
      showCount={true}
    />
  );
}
