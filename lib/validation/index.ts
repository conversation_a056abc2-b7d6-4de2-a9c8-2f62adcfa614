/**
 * Consolidated Validation Module
 *
 * Centralized validation utilities, patterns, and schemas
 * to ensure consistency and reduce duplication across the application.
 */

export {
  AirtableConfigSchema,
  ApifyWebhookSchema,
  EmailSchema,
  type ExtractJobRequest,
  ExtractJobRequestSchema,
  ExtractRequestSchema,
  HealthCheckSchema,
  type JobCreate,
  JobCreateSchema,
  type JobUpdate,
  JobUpdateSchema,
  type PaginationQuery,
  PaginationQuerySchema,
  type SearchQuery,
  TimestampSchema,
  URLSchema,
  UUIDSchema,
} from './api-validation';
// Re-export specific items to avoid conflicts
export {
  API_KEY_VALIDATIONS,
  validateAirtableBaseId,
  validateAirtableConfig,
  validateAirtablePat,
  validateAirtableTableName,
  validateApiKey,
  validateEnvironmentConfig,
  validatePatWithError,
  validateWebhookConfig,
} from './config-validation';
export * from './schema-validation';
// Re-export all validation utilities
export * from './validation-utils';
