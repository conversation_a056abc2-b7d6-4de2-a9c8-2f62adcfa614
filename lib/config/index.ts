/**
 * Centralized Configuration Index
 *
 * Single entry point for all configuration settings across the application.
 * This provides a clean API for accessing all configuration values.
 */

// Import functions for internal use
import { getAIServiceStatus } from './ai-config';
import { getEnvironmentInfo } from './environment-config';
import { getNotificationStatus } from './notification-config';

// AI Configuration
export {
  AI_CONFIG,
  getAIServiceStatus,
  INPUT_LIMITS,
  SCHEMA_LIMITS,
  validateAIConfig,
} from './ai-config';

// Environment Configuration
export {
  DEBUG_MODE,
  DEFAULTS,
  ENV_STATUS,
  envFlag,
  getEnvironmentInfo,
  UPSTASH_CONFIG,
} from './environment-config';
// Job Constants
export {
  // Job types and categories
  CAREER_LEVELS,
  CURRENCY_CODES,
  DEFAULT_JOB_BOARD_CONFIG,
  getJobFilterOptions,
  getSourceDisplayName,
  // Validation functions
  isValidJobSource,
  isValidPostingStrategy,
  JO<PERSON>_SOURCE_DESCRIPTIONS,
  JOB_SOURCES,
  JOB_TYPES,
  // Job board configuration
  type JobBoardConfig,
  type JobSource,
  // Job sources and posting
  POSTING_STRATEGIES,
  POSTING_STRATEGY_DESCRIPTIONS,
  type PostingStrategy,
  REMOTE_REGIONS,
  VISA_SPONSORSHIP_OPTIONS,
  WORKPLACE_TYPES,
} from './job-constants';
// Notification Configuration
export {
  getNotificationStatus,
  isNotificationEnabled,
  NOTIFICATION_LEVELS,
  type NotificationLevel,
  SLACK_CONFIG,
  SLACK_FLAGS,
  validateNotificationConfig,
} from './notification-config';

// Configuration validation
export type SystemConfigStatus = {
  ai: ReturnType<typeof getAIServiceStatus>;
  environment: ReturnType<typeof getEnvironmentInfo>;
  notifications: ReturnType<typeof getNotificationStatus>;
  overall: {
    healthy: boolean;
    errors: string[];
    warnings: string[];
  };
};

/**
 * Get comprehensive system configuration status
 */
export function getSystemConfigStatus(): SystemConfigStatus {
  const ai = getAIServiceStatus();
  const environment = getEnvironmentInfo();
  const notifications = getNotificationStatus();

  // Collect all errors and warnings
  const errors: string[] = [...ai.errors, ...notifications.errors];

  const warnings: string[] = [...notifications.warnings];

  // Add environment warnings
  if (environment.missingVars.length > 0) {
    warnings.push(
      `Missing environment variables: ${environment.missingVars.join(', ')}`
    );
  }

  const overall = {
    healthy: errors.length === 0 && environment.isConfigured,
    errors,
    warnings,
  };

  return {
    ai,
    environment,
    notifications,
    overall,
  };
}

/**
 * Validate that all critical systems are configured
 */
export function validateSystemConfiguration(): {
  isValid: boolean;
  criticalIssues: string[];
  recommendations: string[];
} {
  const status = getSystemConfigStatus();
  const criticalIssues: string[] = [];
  const recommendations: string[] = [];

  // Check AI configuration
  if (!status.ai.configured) {
    criticalIssues.push('AI service not configured properly');
  }

  // Check environment
  if (!status.environment.isConfigured) {
    criticalIssues.push('Required environment variables missing');
  }

  // Check notifications for production
  if (
    status.environment.nodeEnv === 'production' &&
    !status.notifications.configured
  ) {
    criticalIssues.push('Notification system not configured for production');
  }

  // Add recommendations based on warnings
  if (status.overall.warnings.length > 0) {
    recommendations.push(...status.overall.warnings);
  }

  return {
    isValid: criticalIssues.length === 0,
    criticalIssues,
    recommendations,
  };
}
