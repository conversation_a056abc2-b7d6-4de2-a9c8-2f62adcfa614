import type {
  ColumnFiltersState,
  SortingState,
  VisibilityState,
} from '@tanstack/react-table';
import { useState } from 'react';

export type UseDataTableOptions = {
  defaultSorting?: SortingState;
  defaultColumnVisibility?: VisibilityState;
  defaultPageSize?: number;
};

export function useDataTable(options: UseDataTableOptions = {}) {
  const {
    defaultSorting = [],
    defaultColumnVisibility = {},
    defaultPageSize = 10,
  } = options;

  const [sorting, setSorting] = useState<SortingState>(defaultSorting);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>(
    defaultColumnVisibility
  );
  const [rowSelection, setRowSelection] = useState({});

  const getTableState = () => ({
    sorting,
    columnFilters,
    columnVisibility,
    rowSelection,
  });

  const getTableProps = () => ({
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: getTableState(),
    initialState: {
      pagination: {
        pageSize: defaultPageSize,
      },
    },
  });

  return {
    sorting,
    setSorting,
    columnFilters,
    setColumnFilters,
    columnVisibility,
    setColumnVisibility,
    rowSelection,
    setRowSelection,
    getTableState,
    getTableProps,
  };
}
