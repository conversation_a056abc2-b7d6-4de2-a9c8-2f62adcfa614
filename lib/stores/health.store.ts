/**
 * Health Store - Manages system health monitoring data
 */

import { useEffect, useState } from 'react';
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import type { LoadingState } from './types';

/**
 * System Health Check Status
 */
type HealthCheckStatus = {
  status: 'healthy' | 'error' | 'unknown';
  latency: number;
  error?: string;
};

/**
 * System Health Data
 */
type SystemHealth = {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  version: string;
  environment: string;
  checks: {
    database: HealthCheckStatus;
    workflow: HealthCheckStatus;
    apify: HealthCheckStatus;
    slack: HealthCheckStatus;
    uptime: number;
  };
};

/**
 * Sources Health Data
 */
type SourcesHealth = {
  success: boolean;
  sources: Array<{
    id: string;
    name: string;
    type: 'API' | 'RSS' | 'Manual' | 'Generator';
    endpoint: string;
    enabled: boolean;
    description: string;
    config: Record<string, unknown>;
    created_at: string;
    updated_at: string;
    stats: {
      last_fetch_at: string | null;
      total_jobs_fetched: number;
      jobs_fetched_today: number;
      success_rate: number;
      avg_response_time_ms: number;
      error_count_24h: number;
      last_error: string | null;
      last_error_at: string | null;
      rate_limit_info: Record<string, unknown>;
    };
  }>;
};

/**
 * Job Statistics Data
 */
type JobStats = {
  success: boolean;
  data: {
    total: number;
    active: number;
    pending: number;
    processed: number;
    failed: number;
    today: number;
    this_week: number;
    this_month: number;
    success_rate: number;
    avg_processing_time: number;
    top_sources: Array<{
      name: string;
      count: number;
    }>;
  };
};

/**
 * Deduplication Data
 */
type DeduplicationData = {
  success: boolean;
  data: {
    total_duplicates: number;
    duplicates_today: number;
    duplicate_rate: number;
    total_jobs: number;
    unique_pairs_with_duplicates: number;
    most_common_duplicates: Array<{
      title: string;
      count: number;
    }>;
  };
};

/**
 * Monitoring Data
 */
type MonitoringData = {
  success: boolean;
  data?: {
    active_monitors: number;
    failed_monitors: number;
    avg_response_time: number;
    uptime_percentage: number;
  };
};

/**
 * Logs Data
 */
type LogsData = {
  success: boolean;
  data?: Record<string, number>;
};

/**
 * Complete Health Data
 */
type HealthData = {
  systemHealth: SystemHealth | undefined;
  sourcesHealth: SourcesHealth | undefined;
  connectedSourcesHealth: SourcesHealth | undefined;
  jobStats: JobStats | undefined;
  monitoringData: MonitoringData | undefined;
  logsData: LogsData | undefined;
  deduplicationData: DeduplicationData | undefined;
  lastUpdated: string;
};

/**
 * Health State Interface
 */
export interface HealthState extends LoadingState {
  // Data
  data: HealthData;

  // Auto-refresh settings
  autoRefresh: boolean;
  refreshInterval: number; // in milliseconds

  // Actions
  actions: {
    // Fetch all health data
    fetchAllHealthData: () => Promise<void>;

    // Fetch individual health components
    fetchSystemHealth: () => Promise<SystemHealth | null>;
    fetchSourcesHealth: () => Promise<SourcesHealth | null>;
    fetchJobStats: () => Promise<JobStats | null>;
    fetchDeduplicationData: () => Promise<DeduplicationData | null>;

    // Manual refresh
    refresh: () => Promise<void>;

    // Auto-refresh controls
    enableAutoRefresh: () => void;
    disableAutoRefresh: () => void;
    setRefreshInterval: (interval: number) => void;

    // Clear error
    clearError: () => void;

    // Reset store
    reset: () => void;
  };
}

/**
 * Initial state
 */
const initialState: Omit<HealthState, 'actions'> = {
  data: {
    systemHealth: undefined,
    sourcesHealth: undefined,
    connectedSourcesHealth: undefined,
    jobStats: undefined,
    monitoringData: undefined,
    logsData: undefined,
    deduplicationData: undefined,
    lastUpdated: new Date().toISOString(),
  },
  autoRefresh: true,
  refreshInterval: 30_000, // 30 seconds
  loading: false,
  error: null,
};

/**
 * Health Store with DevTools
 */
export const useHealthStore = create<HealthState>()(
  devtools(
    (set, get) => ({
      ...initialState,

      actions: {
        // Fetch all health data in parallel
        fetchAllHealthData: async () => {
          set({ loading: true, error: null });

          try {
            const [systemRes, sourcesRes, jobsRes, dedupRes] =
              await Promise.all([
                fetch('/api/health'),
                fetch('/api/sources'),
                fetch('/api/job-stats'),
                fetch('/api/dedup-stats'),
              ]);

            const [systemHealth, sourcesHealth, jobStats, deduplicationData] =
              await Promise.all([
                systemRes.json(),
                sourcesRes.json(),
                jobsRes.json(),
                dedupRes.json(),
              ]);

            set({
              data: {
                systemHealth,
                sourcesHealth,
                connectedSourcesHealth: sourcesHealth, // Use same data for detailed view
                jobStats,
                monitoringData: { success: false }, // No monitoring data available
                logsData: { success: false }, // No logs data available
                deduplicationData,
                lastUpdated: new Date().toISOString(),
              },
              loading: false,
            });
          } catch (error) {
            set({
              error:
                error instanceof Error
                  ? error.message
                  : 'Failed to fetch health data',
              loading: false,
              data: {
                ...get().data,
                lastUpdated: new Date().toISOString(),
              },
            });
          }
        },

        // Fetch system health
        fetchSystemHealth: async () => {
          try {
            const response = await fetch('/api/health');
            const systemHealth = await response.json();

            if (!response.ok) {
              throw new Error('Failed to fetch system health');
            }

            set((state) => ({
              data: {
                ...state.data,
                systemHealth,
                lastUpdated: new Date().toISOString(),
              },
            }));

            return systemHealth;
          } catch (error) {
            set({
              error:
                error instanceof Error
                  ? error.message
                  : 'Failed to fetch system health',
            });
            return null;
          }
        },

        // Fetch sources health
        fetchSourcesHealth: async () => {
          try {
            const response = await fetch('/api/sources');
            const sourcesHealth = await response.json();

            if (!response.ok) {
              throw new Error('Failed to fetch sources health');
            }

            set((state) => ({
              data: {
                ...state.data,
                sourcesHealth,
                connectedSourcesHealth: sourcesHealth,
                lastUpdated: new Date().toISOString(),
              },
            }));

            return sourcesHealth;
          } catch (error) {
            set({
              error:
                error instanceof Error
                  ? error.message
                  : 'Failed to fetch sources health',
            });
            return null;
          }
        },

        // Fetch job stats
        fetchJobStats: async () => {
          try {
            const response = await fetch('/api/job-stats');
            const jobStats = await response.json();

            if (!response.ok) {
              throw new Error('Failed to fetch job stats');
            }

            set((state) => ({
              data: {
                ...state.data,
                jobStats,
                lastUpdated: new Date().toISOString(),
              },
            }));

            return jobStats;
          } catch (error) {
            set({
              error:
                error instanceof Error
                  ? error.message
                  : 'Failed to fetch job stats',
            });
            return null;
          }
        },

        // Fetch deduplication data
        fetchDeduplicationData: async () => {
          try {
            const response = await fetch('/api/dedup-stats');
            const deduplicationData = await response.json();

            if (!response.ok) {
              throw new Error('Failed to fetch deduplication data');
            }

            set((state) => ({
              data: {
                ...state.data,
                deduplicationData,
                lastUpdated: new Date().toISOString(),
              },
            }));

            return deduplicationData;
          } catch (error) {
            set({
              error:
                error instanceof Error
                  ? error.message
                  : 'Failed to fetch deduplication data',
            });
            return null;
          }
        },

        // Manual refresh
        refresh: async () => {
          await get().actions.fetchAllHealthData();
        },

        // Enable auto-refresh
        enableAutoRefresh: () => {
          set({ autoRefresh: true });
        },

        // Disable auto-refresh
        disableAutoRefresh: () => {
          set({ autoRefresh: false });
        },

        // Set refresh interval
        setRefreshInterval: (interval) => {
          set({ refreshInterval: interval });
        },

        // Clear error
        clearError: () => {
          set({ error: null });
        },

        // Reset store
        reset: () => {
          set(initialState);
        },
      },
    }),
    { name: 'HealthStore' }
  )
);

/**
 * Hydrated hook for SSR-safe usage with auto-refresh
 */
export const useHealthStoreHydrated = () => {
  const store = useHealthStore();
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    setIsHydrated(true);
  }, []);

  // Auto-refresh effect
  useEffect(() => {
    if (!isHydrated) {
      return;
    }

    // Initial fetch only once after hydration
    store.actions.fetchAllHealthData();
  }, [isHydrated, store.actions]);

  // Separate effect for auto-refresh interval
  useEffect(() => {
    if (!(isHydrated && store.autoRefresh)) {
      return;
    }

    // Set up interval
    const intervalId = setInterval(() => {
      store.actions.fetchAllHealthData();
    }, store.refreshInterval);

    return () => clearInterval(intervalId);
  }, [isHydrated, store.autoRefresh, store.refreshInterval, store.actions]);

  if (!isHydrated) {
    return {
      ...initialState,
      // Avoid hydration mismatches by ensuring any dynamic fields are stable
      data: {
        ...initialState.data,
        // Provide an empty string so the UI can hide the timestamp pre-hydration
        lastUpdated: '',
      },
      actions: {
        fetchAllHealthData: async () => {
          // No-op during hydration
        },
        fetchSystemHealth: async () => null,
        fetchSourcesHealth: async () => null,
        fetchJobStats: async () => null,
        fetchDeduplicationData: async () => null,
        refresh: async () => {
          // No-op during hydration
        },
        enableAutoRefresh: () => {
          // No-op during hydration
        },
        disableAutoRefresh: () => {
          // No-op during hydration
        },
        setRefreshInterval: () => {
          // No-op during hydration
        },
        clearError: () => {
          // No-op during hydration
        },
        reset: () => {
          // No-op during hydration
        },
      },
    } as HealthState;
  }

  return store;
};
