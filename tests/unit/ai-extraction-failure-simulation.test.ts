import assert from 'node:assert/strict';
import test from 'node:test';
import { SCHEMA_LIMITS } from '../../lib/constants.js';
// Import actual production code
import { JobExtractionSchema } from '../../lib/job-schema.js';

// Simulate real AI extraction failures based on actual workflow errors
const simulateAIExtractionFailures = () => {
  return [
    // 1. Missing description field (common AI failure)
    {
      name: 'Missing description field',
      data: {
        title: 'Software Engineer',
        status: 'active',
        company: 'TechCorp',
        // description: missing!
      },
      expectedError: 'Required field missing',
    },

    // 2. Responsibilities field exceeding character limit (actual failure from workflow)
    {
      name: 'Responsibilities exceeding 1000 character limit',
      data: {
        title: 'Field Nurse Practitioner',
        description: 'Healthcare position',
        status: 'active',
        company: 'Healthcare Corp',
        responsibilities: `
          <ul>
          <li>Provide comprehensive primary care services to patients across the lifespan</li>
          <li>Conduct thorough physical examinations, health assessments, and diagnostic evaluations</li>
          <li>Develop and implement evidence-based treatment plans for acute and chronic conditions</li>
          <li>Prescribe medications and therapeutic interventions within scope of practice</li>
          <li>Order and interpret diagnostic tests including laboratory work, imaging studies, and specialized procedures</li>
          <li>Collaborate with multidisciplinary healthcare teams including physicians, specialists, and support staff</li>
          <li>Provide patient education on disease prevention, health promotion, and self-care management</li>
          <li>Maintain accurate and detailed electronic health records and documentation</li>
          <li>Participate in quality improvement initiatives and evidence-based practice implementation</li>
          <li>Ensure compliance with all regulatory requirements, policies, and professional standards</li>
          <li>Mentor and supervise junior nursing staff and healthcare students as appropriate</li>
          </ul>
        `.trim(),
      },
      expectedError: 'Character limit exceeded',
    },

    // 3. Invalid enum values (AI hallucination)
    {
      name: 'Invalid status enum value',
      data: {
        title: 'Software Engineer',
        description: 'A software engineering role',
        status: 'open', // Should be 'active', 'closed', 'filled', or 'expired'
        company: 'TechCorp',
      },
      expectedError: 'Invalid enum value',
    },

    // 4. Invalid workplace type (AI confusion)
    {
      name: 'Invalid workplace type',
      data: {
        title: 'Software Engineer',
        description: 'A software engineering role',
        status: 'active',
        company: 'TechCorp',
        workplace_type: 'Work from home', // Should be 'Remote', 'On-site', or 'Hybrid'
      },
      expectedError: 'Invalid enum value',
    },

    // 5. Invalid salary range (AI logic error)
    {
      name: 'Salary min greater than max',
      data: {
        title: 'Software Engineer',
        description: 'A software engineering role',
        status: 'active',
        company: 'TechCorp',
        salary_min: 150_000,
        salary_max: 100_000, // Min > Max
      },
      expectedError: 'Invalid salary range',
    },

    // 6. Invalid URL format (AI extraction error)
    {
      name: 'Invalid apply URL format',
      data: {
        title: 'Software Engineer',
        description: 'A software engineering role',
        status: 'active',
        company: 'TechCorp',
        apply_url: 'Send <NAME_EMAIL>', // Not a URL
      },
      expectedError: 'Invalid URL format',
    },

    // 7. Invalid date format (AI parsing error)
    {
      name: 'Invalid date format',
      data: {
        title: 'Software Engineer',
        description: 'A software engineering role',
        status: 'active',
        company: 'TechCorp',
        posted_date: 'January 15, 2024', // Should be ISO 8601
      },
      expectedError: 'Invalid date format',
    },

    // 8. String instead of array for career_level (AI type confusion)
    {
      name: 'Career level as string instead of array',
      data: {
        title: 'Software Engineer',
        description: 'A software engineering role',
        status: 'active',
        company: 'TechCorp',
        career_level: 'Senior', // Should be ['Senior']
      },
      expectedError: 'Invalid array type',
    },

    // 9. Invalid language code (AI hallucination)
    {
      name: 'Invalid language code',
      data: {
        title: 'Software Engineer',
        description: 'A software engineering role',
        status: 'active',
        company: 'TechCorp',
        languages: ['english'], // Should be ['en']
      },
      expectedError: 'Invalid language code',
    },

    // 10. Negative salary (AI extraction error)
    {
      name: 'Negative salary value',
      data: {
        title: 'Software Engineer',
        description: 'A software engineering role',
        status: 'active',
        company: 'TechCorp',
        salary_min: -50_000,
      },
      expectedError: 'Negative salary',
    },

    // 11. Multiple fields exceeding limits (AI verbosity)
    {
      name: 'Multiple fields exceeding character limits',
      data: {
        title: 'Software Engineer',
        description: 'A software engineering role',
        status: 'active',
        company: 'TechCorp',
        benefits: 'A'.repeat(SCHEMA_LIMITS.benefits + 100),
        skills: 'A'.repeat(SCHEMA_LIMITS.skills + 50),
        qualifications: 'A'.repeat(SCHEMA_LIMITS.qualifications + 200),
      },
      expectedError: 'Multiple character limits exceeded',
    },

    // 12. Empty required fields (AI extraction failure)
    {
      name: 'Empty title field',
      data: {
        title: '', // Empty string
        description: 'A software engineering role',
        status: 'active',
        company: 'TechCorp',
      },
      expectedError: 'Empty required field',
    },
  ];
};

// Test each simulated AI failure
test('Simulate real AI extraction failures', () => {
  const failures = simulateAIExtractionFailures();
  let failureCount = 0;

  for (const failure of failures) {
    try {
      JobExtractionSchema.parse(failure.data);
      assert.fail(`Should have failed: ${failure.name}`);
    } catch (error) {
      failureCount++;
      assert.ok(error, `Should throw validation error for: ${failure.name}`);
    }
  }
  assert.equal(
    failureCount,
    failures.length,
    'All simulated failures should be caught'
  );
});

// Test edge cases that might pass validation but cause issues downstream
test('Edge cases that pass validation but may cause issues', () => {
  const edgeCases = [
    {
      name: 'Very long title (within limits but unusual)',
      data: {
        title:
          'Senior Full-Stack Software Engineer with expertise in React, Node.js, Python, and Cloud Architecture',
        description: 'A software engineering role',
        status: 'active' as const,
        company: 'TechCorp',
      },
    },
    {
      name: 'Salary at maximum reasonable value',
      data: {
        title: 'CEO',
        description: 'Chief Executive Officer position',
        status: 'active' as const,
        company: 'TechCorp',
        salary_min: 10_000_000, // $10M
        salary_max: 20_000_000, // $20M
      },
    },
    {
      name: 'All optional fields as null',
      data: {
        title: 'Software Engineer',
        description: 'A software engineering role',
        status: 'active' as const,
        company: null,
        type: null,
        apply_url: null,
        apply_method: null,
        posted_date: null,
        salary_min: null,
        salary_max: null,
        salary_currency: null,
        salary_unit: null,
        workplace_type: null,
        remote_region: null,
        timezone_requirements: null,
        workplace_city: null,
        workplace_country: null,
        benefits: null,
        application_requirements: null,
        career_level: null,
        visa_sponsorship: null,
        languages: null,
        skills: null,
        qualifications: null,
        education_requirements: null,
        experience_requirements: null,
        responsibilities: null,
        featured: null,
        industry: null,
        occupational_category: null,
        valid_through: null,
        job_identifier: null,
        job_source_name: null,
        department: null,
      },
    },
  ];

  for (const edgeCase of edgeCases) {
    try {
      const result = JobExtractionSchema.parse(edgeCase.data);
      assert.ok(result, `Should parse edge case: ${edgeCase.name}`);
    } catch (_error) {
      assert.fail(`Edge case should pass validation: ${edgeCase.name}`);
    }
  }
});

// Test character limit boundaries precisely
test('Character limit boundary testing', () => {
  const fields = Object.keys(SCHEMA_LIMITS) as Array<
    keyof typeof SCHEMA_LIMITS
  >;

  for (const field of fields) {
    const limit = SCHEMA_LIMITS[field];

    // Test exactly at limit (should pass)
    const atLimitData = {
      title: 'Software Engineer',
      description: 'A software engineering role',
      status: 'active' as const,
      [field]: 'A'.repeat(limit),
    };

    try {
      JobExtractionSchema.parse(atLimitData);
    } catch (_error) {
      assert.fail(`Should pass at exact limit for ${field}`);
    }

    // Test one character over limit (should fail)
    const overLimitData = {
      title: 'Software Engineer',
      description: 'A software engineering role',
      status: 'active' as const,
      [field]: 'A'.repeat(limit + 1),
    };

    try {
      JobExtractionSchema.parse(overLimitData);
      assert.fail(`Should fail when over limit for ${field}`);
    } catch (_error) {}
  }
});

// Performance test with large data
test('Performance with maximum size data', () => {
  const startTime = Date.now();

  const maxSizeJob = {
    title: 'Software Engineer',
    description: 'A software engineering role',
    status: 'active' as const,
    company: 'TechCorp',
    benefits: 'A'.repeat(SCHEMA_LIMITS.benefits),
    application_requirements: 'A'.repeat(
      SCHEMA_LIMITS.application_requirements
    ),
    skills: 'A'.repeat(SCHEMA_LIMITS.skills),
    qualifications: 'A'.repeat(SCHEMA_LIMITS.qualifications),
    education_requirements: 'A'.repeat(SCHEMA_LIMITS.education_requirements),
    experience_requirements: 'A'.repeat(SCHEMA_LIMITS.experience_requirements),
    responsibilities: 'A'.repeat(SCHEMA_LIMITS.responsibilities),
  };

  try {
    const result = JobExtractionSchema.parse(maxSizeJob);
    const duration = Date.now() - startTime;
    assert.ok(result, 'Should parse maximum size job');
    assert.ok(duration < 100, 'Should parse within reasonable time (< 100ms)');
  } catch (error) {
    assert.fail(`Should parse maximum size job: ${error}`);
  }
});
