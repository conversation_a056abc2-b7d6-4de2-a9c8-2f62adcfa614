/**
 * Notification Configuration
 *
 * Slack notifications, alerting settings, and communication preferences
 */

import { envFlag } from './environment-config';

// Slack configuration with fallback
export const SLACK_CONFIG = {
  webhookUrl: process.env.SLACK_WEBHOOK_URL || '',
};

/**
 * Granular Slack notification flags
 * Defaults are conservative to prevent noise: only critical alerts enabled by default.
 */
export const SLACK_FLAGS = {
  enableCritical: envFlag('SLACK_ENABLE_CRITICAL', true),
  enableAlert: envFlag('SLACK_ENABLE_ALERT', false),
  enableInfo: envFlag('SLACK_ENABLE_INFO', false),
  enablePipeline: envFlag('SLACK_ENABLE_PIPELINE', false),
  // When true, pipeline notifications are sent only if context.success === false
  pipelineOnlyFailures: envFlag('SLACK_PIPELINE_ONLY_FAILURES', true),
  // Comma-separated list of pipeline steps to allow (e.g., "SOURCED,PROCESSED"). Empty means all steps.
  pipelineSteps: (() => {
    const raw = process.env.SLACK_PIPELINE_STEPS;
    if (!raw) {
      return new Set<string>();
    }
    return new Set(
      raw
        .split(',')
        .map((s) => s.trim().toUpperCase())
        .filter((s) => s.length > 0)
    );
  })(),
} as const;

// Notification levels for different types of events
export const NOTIFICATION_LEVELS = {
  CRITICAL: 'critical',
  ALERT: 'alert',
  INFO: 'info',
  PIPELINE: 'pipeline',
} as const;

export type NotificationLevel =
  (typeof NOTIFICATION_LEVELS)[keyof typeof NOTIFICATION_LEVELS];

// Check if a specific notification type is enabled
export function isNotificationEnabled(level: NotificationLevel): boolean {
  switch (level) {
    case NOTIFICATION_LEVELS.CRITICAL:
      return SLACK_FLAGS.enableCritical;
    case NOTIFICATION_LEVELS.ALERT:
      return SLACK_FLAGS.enableAlert;
    case NOTIFICATION_LEVELS.INFO:
      return SLACK_FLAGS.enableInfo;
    case NOTIFICATION_LEVELS.PIPELINE:
      return SLACK_FLAGS.enablePipeline;
    default:
      return false;
  }
}

// Validate notification configuration
export function validateNotificationConfig(): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!SLACK_CONFIG.webhookUrl) {
    errors.push('SLACK_WEBHOOK_URL not configured');
  }

  if (SLACK_FLAGS.enableCritical && !SLACK_CONFIG.webhookUrl) {
    errors.push(
      'Critical notifications enabled but Slack webhook not configured'
    );
  }

  if (!SLACK_FLAGS.enableCritical) {
    warnings.push(
      'Critical notifications are disabled - important alerts may be missed'
    );
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

// Get notification service status
export function getNotificationStatus() {
  const validation = validateNotificationConfig();

  return {
    configured: validation.isValid,
    slackEnabled: !!SLACK_CONFIG.webhookUrl,
    enabledLevels: {
      critical: SLACK_FLAGS.enableCritical,
      alert: SLACK_FLAGS.enableAlert,
      info: SLACK_FLAGS.enableInfo,
      pipeline: SLACK_FLAGS.enablePipeline,
    },
    pipelineSettings: {
      onlyFailures: SLACK_FLAGS.pipelineOnlyFailures,
      allowedSteps: Array.from(SLACK_FLAGS.pipelineSteps),
    },
    errors: validation.errors,
    warnings: validation.warnings,
  };
}
