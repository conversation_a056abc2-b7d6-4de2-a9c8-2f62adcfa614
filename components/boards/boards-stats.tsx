'use client';

import { Bar<PERSON>hart3, CheckCircle, Target, Zap } from 'lucide-react';
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useBoardsStoreHydrated } from '@/lib/stores';

type BoardStats = {
  totalBoards: number;
  activeBoards: number;
  dailyCapacity: number;
  totalPosted: number;
};

type BoardsStatsProps = {
  refreshTrigger?: number; // Optional prop to trigger refresh
};

export function BoardsStats({ refreshTrigger }: BoardsStatsProps) {
  // Use the Zustand store with hydration protection
  const { boards, loading, actions } = useBoardsStoreHydrated();

  // Compute stats from boards data
  const stats = React.useMemo<BoardStats>(() => {
    return {
      totalBoards: boards.length,
      activeBoards: boards.filter((b) => b.enabled).length,
      dailyCapacity: boards.reduce(
        (sum, b) => sum + (b.posting?.dailyLimit || 0),
        0
      ),
      totalPosted: boards.reduce((sum, b) => sum + (b.totalPosted || 0), 0),
    };
  }, [boards]);

  // Load boards on mount
  React.useEffect(() => {
    actions.fetch();
  }, [actions]);

  // Refresh stats when refreshTrigger changes
  React.useEffect(() => {
    if (refreshTrigger !== undefined && refreshTrigger > 0) {
      actions.fetch();
    }
  }, [refreshTrigger, actions]);

  if (loading && boards.length === 0) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {['total', 'enabled', 'posted-today', 'success-rate'].map(
          (statType) => (
            <Card key={statType}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="font-medium text-sm">
                  <div className="h-4 w-20 animate-pulse rounded bg-muted" />
                </CardTitle>
                <div className="h-4 w-4 animate-pulse rounded bg-muted" />
              </CardHeader>
              <CardContent>
                <div className="mb-1 h-8 w-16 animate-pulse rounded bg-muted" />
                <div className="h-3 w-24 animate-pulse rounded bg-muted" />
              </CardContent>
            </Card>
          )
        )}
      </div>
    );
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="font-medium text-sm">Total Boards</CardTitle>
          <Target className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="font-bold text-2xl">{stats.totalBoards}</div>
          <p className="text-muted-foreground text-xs">Configured job boards</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="font-medium text-sm">Active Boards</CardTitle>
          <CheckCircle className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="font-bold text-2xl text-green-600">
            {stats.activeBoards}
          </div>
          <p className="text-muted-foreground text-xs">Currently enabled</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="font-medium text-sm">Daily Capacity</CardTitle>
          <Zap className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="font-bold text-2xl text-purple-600">
            {stats.dailyCapacity}
          </div>
          <p className="text-muted-foreground text-xs">Jobs per day limit</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="font-medium text-sm">Total Posted</CardTitle>
          <BarChart3 className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="font-bold text-2xl text-orange-600">
            {stats.totalPosted}
          </div>
          <p className="text-muted-foreground text-xs">All-time job posts</p>
        </CardContent>
      </Card>
    </div>
  );
}
