import { openai } from "@ai-sdk/openai";
import { generateObject } from "ai";
import { replaceTemplateVariables } from "../api-utils";
import { AI_CONFIG } from "../config/ai-config";
import { JobExtractionSchema } from "../job-schema";
import { logger } from "../logger";
import { JOB_EXTRACTION_PROMPT } from "../prompts";
import { retryAIExtraction } from "../retry-utils";

/**
 * Core AI extraction logic
 *
 * Handles the actual AI processing with OpenAI, including:
 * - Prompt preparation and template variable replacement
 * - AI model invocation with retry logic
 * - Raw AI response handling
 */

export interface ExtractJobInput {
  sourcedAt: string;
  sourceUrl: string;
  content: string;
}

export interface AIExtractionResult {
  object: unknown;
  usage: {
    inputTokens?: number;
    outputTokens?: number;
    totalTokens?: number;
  };
  response?: {
    id?: string;
    modelId?: string;
    timestamp?: Date;
  };
  finishReason?: string;
  promptUsed: string;
  promptLength: number;
}

/**
 * Performs the core AI extraction using OpenAI
 */
export async function performAIExtraction(
  post: ExtractJobInput
): Promise<AIExtractionResult> {
  // Use default prompt with placeholder replacement
  const finalPrompt = replaceTemplateVariables(JOB_EXTRACTION_PROMPT, {
    content: post.content,
  });

  logger.debug("Generated extraction prompt", {
    promptLength: finalPrompt.length,
    hasOpenAIKey: !!process.env.OPENAI_API_KEY,
    openaiKeyPrefix: process.env.OPENAI_API_KEY?.substring(0, 8) || "undefined",
  });

  const result = await retryAIExtraction(async () => {
    return await generateObject({
      model: openai(AI_CONFIG.MODEL),
      prompt: finalPrompt,
      schema: JobExtractionSchema,
    });
  });

  logger.debug("AI extraction successful", {
    inputTokens: result.usage?.inputTokens || 0,
    outputTokens: result.usage?.outputTokens || 0,
    totalTokens: result.usage?.totalTokens || 0,
    finishReason: result.finishReason,
  });

  return {
    ...result,
    promptUsed: finalPrompt,
    promptLength: finalPrompt.length,
  };
}

/**
 * Validates if we have the necessary configuration for AI extraction
 */
export function validateAIConfig(): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!process.env.OPENAI_API_KEY) {
    errors.push("OPENAI_API_KEY not configured");
  }

  if (!AI_CONFIG.MODEL) {
    errors.push("AI model not configured");
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Gets the current AI configuration for debugging
 */
export function getAIConfig() {
  return {
    model: AI_CONFIG.MODEL,
    hasApiKey: !!process.env.OPENAI_API_KEY,
    apiKeyPrefix: process.env.OPENAI_API_KEY?.substring(0, 8) || "undefined",
    environment: process.env.NODE_ENV,
    nodeVersion: process.version,
  };
}
