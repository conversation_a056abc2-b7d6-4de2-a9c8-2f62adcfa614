import { type NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase';
import { logger } from '@/lib/utils';
import { triggerJobProcessing } from '@/lib/workflow-client';

type WorkflowStats = {
  active: number;
  completed: number;
  failed: number;
  partial_failure: number;
  total_today: number;
  jobs_processed_today: number;
};

type WorkflowQueue = {
  pending: number;
  processing: number;
};

type WorkflowRun = {
  id: string;
  workflow_type: string;
  status: 'running' | 'completed' | 'failed' | 'partial_failure';
  started_at: string;
  completed_at?: string;
  job_count: number;
  error_message?: string;
};

type WorkflowStatus = {
  stats: WorkflowStats;
  queue: WorkflowQueue;
  recent_runs: WorkflowRun[];
  timestamp: string;
};

async function getWorkflowStats(): Promise<WorkflowStats> {
  const supabase = createClient();
  const today = new Date().toISOString().split('T')[0];

  // Get workflow run statistics for today
  const { data: runs } = await supabase
    .from('workflow_runs')
    .select('status, job_count')
    .gte('started_at', `${today}T00:00:00.000Z`)
    .lt('started_at', `${today}T23:59:59.999Z`);

  const stats = {
    active: 0,
    completed: 0,
    failed: 0,
    partial_failure: 0,
    total_today: runs?.length || 0,
    jobs_processed_today: 0,
  };

  if (runs) {
    for (const run of runs) {
      stats.jobs_processed_today += run.job_count || 0;

      switch (run.status) {
        case 'running':
          stats.active++;
          break;
        case 'completed':
          stats.completed++;
          break;
        case 'failed':
          stats.failed++;
          break;
        case 'partial_failure':
          stats.partial_failure++;
          break;
      }
    }
  }

  return stats;
}

async function getWorkflowQueue(): Promise<WorkflowQueue> {
  const supabase = createClient();

  // Count jobs by processing status
  const { data: jobCounts } = await supabase
    .from('jobs')
    .select('processing_status')
    .in('processing_status', ['pending', 'processing']);

  const queue = {
    pending: 0,
    processing: 0,
  };

  if (jobCounts) {
    for (const job of jobCounts) {
      if (job.processing_status === 'pending') {
        queue.pending++;
      } else if (job.processing_status === 'processing') {
        queue.processing++;
      }
    }
  }

  return queue;
}

async function getRecentWorkflowRuns(): Promise<WorkflowRun[]> {
  const supabase = createClient();

  const { data: runs } = await supabase
    .from('workflow_runs')
    .select('*')
    .order('started_at', { ascending: false })
    .limit(20);

  return runs || [];
}

/**
 * GET /api/workflows/status
 * Returns current workflow statistics, queue status, and recent runs
 */
export async function GET(): Promise<NextResponse> {
  try {
    const [stats, queue, recent_runs] = await Promise.all([
      getWorkflowStats(),
      getWorkflowQueue(),
      getRecentWorkflowRuns(),
    ]);

    const response: WorkflowStatus = {
      stats,
      queue,
      recent_runs,
      timestamp: new Date().toISOString(),
    };

    return NextResponse.json(response);
  } catch (error) {
    logger.error('Failed to get workflow status', { error });
    return NextResponse.json(
      { error: 'Failed to get workflow status' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/workflows/status
 * Handles workflow actions like triggering processing
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const body = await request.json();
    const { action, batchSize = 10, source } = body;

    if (action === 'trigger_processing') {
      logger.info('Manual workflow trigger requested', {
        batchSize,
        source,
      });

      const workflowRunId = await triggerJobProcessing(batchSize, source);

      return NextResponse.json({
        success: true,
        message: 'Workflow triggered successfully',
        workflowRunId,
        messageId: workflowRunId, // For dashboard compatibility
        batchSize,
        source,
        timestamp: new Date().toISOString(),
      });
    }

    return NextResponse.json({ error: 'Unknown action' }, { status: 400 });
  } catch (error) {
    logger.error('Failed to execute workflow action', { error });
    return NextResponse.json(
      { error: 'Failed to execute workflow action' },
      { status: 500 }
    );
  }
}
