import { DEBUG_MODE } from "./config/environment-config";
import { createNotifier } from "./notifier";

/**
 * Slack notification types for different alert levels
 */
type SlackAlertLevel = "critical" | "warning" | "info";

/**
 * Pipeline step types for tracking job processing flow
 */
type PipelineStep =
  | "SOURCED"
  | "DEDUPED"
  | "QUEUED"
  | "PROCESSED"
  | "STORED"
  | "MONITORED"
  | "SYNCED";

/**
 * Pipeline notification context
 */
interface PipelineContext {
  step: PipelineStep;
  source?: string;
  jobCount?: number;
  batchId?: string;
  duration?: number;
  success?: boolean;
  error?: string;
  stats?: Record<string, unknown>;
  metadata?: Record<string, unknown>;
}

const notifier = createNotifier();

/**
 * Logging utility - compliant with Ultracite rules (no direct console usage)
 * Only logs when DEBUG_MODE is enabled, with Slack alerts for critical issues
 */
export const logger = {
  log: (..._args: unknown[]) => {
    if (DEBUG_MODE) {
      // biome-ignore lint/suspicious/noConsole: Debug logging
      console.log(..._args);
    }
  },
  error: (..._args: unknown[]) => {
    if (DEBUG_MODE) {
      // biome-ignore lint/suspicious/noConsole: Debug logging
      console.error(..._args);
    }
  },
  info: (..._args: unknown[]) => {
    if (DEBUG_MODE) {
      // biome-ignore lint/suspicious/noConsole: Debug logging
      console.info(..._args);
    }
  },
  warn: (..._args: unknown[]) => {
    if (DEBUG_MODE) {
      // biome-ignore lint/suspicious/noConsole: Debug logging
      console.warn(..._args);
    }
  },
  debug: (..._args: unknown[]) => {
    if (DEBUG_MODE) {
      // biome-ignore lint/suspicious/noConsole: Debug logging
      console.debug(..._args);
    }
  },

  // Enhanced Slack alert methods for production monitoring
  /**
   * Send critical alert to both console and Slack
   * Use for system failures, data corruption, service outages
   */
  critical: (message: string, details?: unknown) => {
    const fullMessage = details
      ? `${message}\n\nDetails: ${JSON.stringify(details, null, 2)}`
      : message;

    if (DEBUG_MODE) {
      // biome-ignore lint/suspicious/noConsole: Critical error logging
      console.error("🚨 CRITICAL:", fullMessage);
    }

    // Send via notifier (non-blocking)
    void notifier.critical(fullMessage);
  },

  /**
   * Send warning alert to both console and Slack
   * Use for degraded performance, rate limits, non-fatal errors
   */
  alert: (message: string, details?: unknown) => {
    const fullMessage = details
      ? `${message}\n\nDetails: ${JSON.stringify(details, null, 2)}`
      : message;

    if (DEBUG_MODE) {
      // biome-ignore lint/suspicious/noConsole: Alert logging
      console.warn("⚠️ ALERT:", fullMessage);
    }

    // Send via notifier (non-blocking)
    void notifier.alert(fullMessage);
  },

  /**
   * Send info notification to Slack only (not console spam)
   * Use for important milestones, successful deployments, metrics
   */
  notify: (message: string, details?: unknown) => {
    const fullMessage = details
      ? `${message}\n\nDetails: ${JSON.stringify(details, null, 2)}`
      : message;

    // Only log to console in debug mode
    if (DEBUG_MODE) {
      // biome-ignore lint/suspicious/noConsole: Notification logging
      console.info("📢 NOTIFY:", fullMessage);
    }

    // Send via notifier (non-blocking)
    void notifier.info(fullMessage);
  },

  /**
   * Track pipeline step completion with rich context
   * Provides granular visibility into job processing flow
   * Use for development observability and debugging
   */
  pipeline: (context: PipelineContext) => {
    const stepEmojis = {
      SOURCED: "📥",
      DEDUPED: "🔍",
      QUEUED: "📤",
      PROCESSED: "🧠",
      STORED: "💾",
      MONITORED: "👁️",
      SYNCED: "🔄",
    };

    const emoji = stepEmojis[context.step] || "⚙️"; // Fallback emoji for unknown steps
    const timestamp = new Date().toISOString();

    // Build the main message
    let message = `${emoji} Pipeline Step: ${context.step}`;

    if (context.source) {
      message += ` | Source: ${context.source}`;
    }

    if (context.jobCount !== undefined) {
      message += ` | Jobs: ${context.jobCount}`;
    }

    if (context.duration !== undefined) {
      message += ` | Duration: ${context.duration}ms`;
    }

    if (context.success !== undefined) {
      message += ` | ${context.success ? "✅ Success" : "❌ Failed"}`;
    }

    // Build detailed context
    const details: Record<string, unknown> = {
      timestamp,
      step: context.step,
    };

    if (context.batchId) {
      details.batchId = context.batchId;
    }
    if (context.error) {
      details.error = context.error;
    }
    if (context.stats) {
      details.stats = context.stats;
    }
    if (context.metadata) {
      details.metadata = context.metadata;
    }

    // Log to console in debug mode
    if (DEBUG_MODE) {
      // biome-ignore lint/suspicious/noConsole: Pipeline tracking
      console.info("🔄 PIPELINE:", message, details);
    }

    // Send via notifier with structured format
    const slackMessage = `${message}\n\n**Context:**\n${JSON.stringify(
      details,
      null,
      2
    )}`;
    void notifier.pipeline({
      ...context,
      metadata: { ...(context.metadata || {}), formatted: slackMessage },
    });
  },
};
