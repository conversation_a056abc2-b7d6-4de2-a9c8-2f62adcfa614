/**
 * Webhook Utility Functions
 *
 * Common utilities for webhook processing including
 * error handling, response formatting, and validation helpers.
 */

import type { NextRequest } from 'next/server';
import { logger } from '@/lib/logger';

// Webhook security utilities
export type WebhookSecurityConfig = {
  requiredHeaders?: Record<string, string>;
  allowedIPs?: string[];
  rateLimitWindowMs?: number;
  maxRequestsPerWindow?: number;
};

/**
 * Validate webhook security requirements
 */
export function validateWebhookSecurity(
  request: NextRequest,
  config: WebhookSecurityConfig = {}
): { valid: boolean; error?: string } {
  // Check required headers
  if (config.requiredHeaders) {
    for (const [headerName, expectedValue] of Object.entries(
      config.requiredHeaders
    )) {
      const actualValue = request.headers.get(headerName);
      if (!actualValue || actualValue !== expectedValue) {
        return {
          valid: false,
          error: `Missing or invalid header: ${headerName}`,
        };
      }
    }
  }

  // Check allowed IPs (if configured)
  if (config.allowedIPs && config.allowedIPs.length > 0) {
    const clientIP = getClientIP(request);
    if (!(clientIP && config.allowedIPs.includes(clientIP))) {
      return {
        valid: false,
        error: `IP address not allowed: ${clientIP}`,
      };
    }
  }

  return { valid: true };
}

/**
 * Extract client IP from request
 */
export function getClientIP(request: NextRequest): string | null {
  // Check various headers for the real IP
  const headers = [
    'x-forwarded-for',
    'x-real-ip',
    'x-client-ip',
    'cf-connecting-ip', // Cloudflare
    'fastly-client-ip', // Fastly
    'x-cluster-client-ip',
    'x-forwarded',
    'forwarded-for',
    'forwarded',
  ];

  for (const header of headers) {
    const value = request.headers.get(header);
    if (value) {
      // x-forwarded-for can contain multiple IPs, take the first one
      const ip = value.split(',')[0].trim();
      if (ip && ip !== 'unknown') {
        return ip;
      }
    }
  }

  // Note: NextRequest doesn't have an ip property in the current version
  // This would need to be handled by the hosting platform (Vercel, etc.)
  return null;
}

/**
 * Generate webhook processing metrics
 */
export type WebhookMetrics = {
  startTime: number;
  endTime?: number;
  processingTimeMs?: number;
  memoryUsage?: {
    start: NodeJS.MemoryUsage;
    end?: NodeJS.MemoryUsage;
    delta?: number;
  };
};

export function createWebhookMetrics(): WebhookMetrics {
  return {
    startTime: Date.now(),
    memoryUsage: {
      start: process.memoryUsage(),
    },
  };
}

export function finalizeWebhookMetrics(
  metrics: WebhookMetrics
): WebhookMetrics {
  const endTime = Date.now();
  const endMemory = process.memoryUsage();

  return {
    ...metrics,
    endTime,
    processingTimeMs: endTime - metrics.startTime,
    memoryUsage: {
      start: metrics.memoryUsage?.start || process.memoryUsage(),
      end: endMemory,
      delta: endMemory.heapUsed - (metrics.memoryUsage?.start.heapUsed || 0),
    },
  };
}

/**
 * Standard webhook error handler
 */
export function handleWebhookError(
  error: unknown,
  context: {
    serviceName: string;
    runId?: string;
    startTime: number;
  }
): {
  success: false;
  error: string;
  message: string;
  processingTime: string;
  runId?: string;
} {
  const errorMessage = error instanceof Error ? error.message : 'Unknown error';

  logger.error(`${context.serviceName} webhook failed`, {
    error,
    runId: context.runId,
    processingTime: `${Date.now() - context.startTime}ms`,
  });

  return {
    success: false,
    error: errorMessage,
    message: 'Webhook acknowledged but processing failed',
    processingTime: `${Date.now() - context.startTime}ms`,
    runId: context.runId,
  };
}

/**
 * Webhook request size validation
 */
export function validateWebhookRequestSize(
  request: NextRequest,
  maxSizeBytes = 50 * 1024 * 1024 // 50MB default
): { valid: boolean; error?: string } {
  const contentLength = request.headers.get('content-length');

  if (contentLength) {
    const size = Number.parseInt(contentLength, 10);
    if (size > maxSizeBytes) {
      return {
        valid: false,
        error: `Request too large: ${size} bytes (max: ${maxSizeBytes} bytes)`,
      };
    }
  }

  return { valid: true };
}

/**
 * Parse webhook payload with error handling
 */
export async function parseWebhookPayload(
  request: NextRequest
): Promise<{ data?: unknown; error?: string }> {
  try {
    const data = await request.json();
    return { data };
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : 'Failed to parse JSON';
    return { error: `Invalid JSON payload: ${errorMessage}` };
  }
}

/**
 * Calculate job processing statistics
 */
export type JobProcessingStats = {
  totalJobs: number;
  savedJobs: number;
  skippedJobs: number;
  errorJobs: number;
  successRate: number;
  processingRate: number; // jobs per second
  averageJobSize?: number; // bytes per job
};

export function calculateJobStats(
  totalJobs: number,
  savedJobs: number,
  skippedJobs: number,
  errorJobs: number,
  processingTimeMs: number,
  totalDataSize?: number
): JobProcessingStats {
  const successRate = totalJobs > 0 ? (savedJobs / totalJobs) * 100 : 0;
  const processingRate =
    processingTimeMs > 0 ? (totalJobs / processingTimeMs) * 1000 : 0;
  const averageJobSize =
    totalDataSize && totalJobs > 0 ? totalDataSize / totalJobs : undefined;

  return {
    totalJobs,
    savedJobs,
    skippedJobs,
    errorJobs,
    successRate: Math.round(successRate * 100) / 100,
    processingRate: Math.round(processingRate * 100) / 100,
    averageJobSize: averageJobSize ? Math.round(averageJobSize) : undefined,
  };
}

/**
 * Webhook response builder
 */
export class WebhookResponseBuilder {
  private readonly response: Record<string, unknown> = {};

  static create() {
    return new WebhookResponseBuilder();
  }

  success(success: boolean) {
    this.response.success = success;
    return this;
  }

  message(message: string) {
    this.response.message = message;
    return this;
  }

  runId(runId: string) {
    this.response.runId = runId;
    return this;
  }

  stats(stats: Partial<JobProcessingStats>) {
    Object.assign(this.response, stats);
    return this;
  }

  processingTime(timeMs: number) {
    this.response.processingTime = `${timeMs}ms`;
    return this;
  }

  workflow(triggered: boolean, messageId?: string) {
    this.response.workflowTriggered = triggered;
    if (messageId) {
      this.response.workflowMessageId = messageId;
    }
    return this;
  }

  error(error: string) {
    this.response.error = error;
    return this;
  }

  custom(key: string, value: unknown) {
    this.response[key] = value;
    return this;
  }

  build() {
    return this.response;
  }
}
