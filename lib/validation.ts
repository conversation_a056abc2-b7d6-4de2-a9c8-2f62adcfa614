/**
 * Legacy Validation Module
 * 
 * This file is maintained for backwards compatibility.
 * New code should use the consolidated validation modules in lib/validation/
 * 
 * @deprecated Use lib/validation/ modules instead
 */

// Re-export everything from the new consolidated validation modules
export * from './validation';

// Legacy exports for backwards compatibility
export {
  validateAirtableBaseId,
  validateAirtablePat, 
  validateAirtableTableName,
  validatePatWithError,
  validateAirtableConfig as validateAirtableConfigWithErrors,
} from './validation/config-validation';

export {
  ExtractRequestSchema,
  ExtractJobRequestSchema,
  type ExtractJobRequest,
} from './validation/api-validation';
