import type { JobStatus } from '../job-status';
import { isJobExpired } from '../job-status';
import { logger } from '../logger';

/**
 * Job post-processing utilities
 * 
 * Handles the post-processing of extracted job data,
 * including fallback logic, status management, and
 * data normalization after AI extraction.
 */

export interface ExtractedJobData {
  apply_url?: string | null;
  status?: JobStatus | null;
  valid_through?: string | null;
  [key: string]: unknown;
}

/**
 * Apply fallback logic for apply_url field
 * 
 * If the AI didn't extract a valid apply_url, use the source URL as fallback
 */
export function applyFallbackLogic(
  extractedJob: ExtractedJobData,
  sourceUrl: string
): void {
  if (!extractedJob.apply_url || extractedJob.apply_url.trim() === '') {
    logger.debug('Applying apply_url fallback', {
      originalValue: extractedJob.apply_url,
      fallbackValue: sourceUrl,
    });
    extractedJob.apply_url = sourceUrl;
  }
}

/**
 * Apply status logic based on job expiration and other business rules
 */
export function applyStatusLogic(extractedJob: ExtractedJobData): void {
  // Set default status to active if not provided
  if (!extractedJob.status) {
    logger.debug('Setting default job status to active');
    extractedJob.status = 'active';
  }

  // Auto-expire job if valid_through date has passed
  if (
    extractedJob.status === 'active' &&
    isJobExpired(extractedJob.valid_through || null)
  ) {
    logger.debug('Auto-expiring job based on valid_through date', {
      validThrough: extractedJob.valid_through,
      previousStatus: extractedJob.status,
    });
    extractedJob.status = 'expired';
  }
}

/**
 * Normalize and clean extracted job data
 */
export function normalizeJobData(extractedJob: ExtractedJobData): void {
  // Trim whitespace from string fields
  if (typeof extractedJob.title === 'string') {
    extractedJob.title = extractedJob.title.trim();
  }

  if (typeof extractedJob.company === 'string') {
    extractedJob.company = extractedJob.company.trim();
  }

  if (typeof extractedJob.location === 'string') {
    extractedJob.location = extractedJob.location.trim();
  }

  if (typeof extractedJob.description === 'string') {
    extractedJob.description = extractedJob.description.trim();
  }

  // Normalize URLs
  if (typeof extractedJob.apply_url === 'string' && extractedJob.apply_url) {
    extractedJob.apply_url = normalizeUrl(extractedJob.apply_url);
  }

  if (typeof extractedJob.company_url === 'string' && extractedJob.company_url) {
    extractedJob.company_url = normalizeUrl(extractedJob.company_url);
  }
}

/**
 * Normalize URL format
 */
function normalizeUrl(url: string): string {
  try {
    // Add protocol if missing
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = `https://${url}`;
    }

    // Validate and normalize URL
    const parsedUrl = new URL(url);
    return parsedUrl.toString();
  } catch (error) {
    // If URL is invalid, return as-is with warning
    logger.warn('Invalid URL format during normalization', {
      url,
      error: error instanceof Error ? error.message : String(error),
    });
    return url;
  }
}

/**
 * Apply all post-processing steps to extracted job data
 */
export function applyJobPostProcessing(
  extractedJob: ExtractedJobData,
  sourceUrl: string
): void {
  logger.debug('Starting job post-processing', {
    hasApplyUrl: !!extractedJob.apply_url,
    currentStatus: extractedJob.status,
    validThrough: extractedJob.valid_through,
  });

  // Apply processing steps in order
  normalizeJobData(extractedJob);
  applyFallbackLogic(extractedJob, sourceUrl);
  applyStatusLogic(extractedJob);

  logger.debug('Job post-processing completed', {
    finalStatus: extractedJob.status,
    finalApplyUrl: extractedJob.apply_url?.substring(0, 100) || 'none',
  });
}

/**
 * Validate that required fields are present after post-processing
 */
export function validateProcessedJob(extractedJob: ExtractedJobData): {
  isValid: boolean;
  missingFields: string[];
  warnings: string[];
} {
  const missingFields: string[] = [];
  const warnings: string[] = [];

  // Check required fields
  if (!extractedJob.title || typeof extractedJob.title !== 'string') {
    missingFields.push('title');
  }

  if (!extractedJob.company || typeof extractedJob.company !== 'string') {
    missingFields.push('company');
  }

  if (!extractedJob.apply_url || typeof extractedJob.apply_url !== 'string') {
    missingFields.push('apply_url');
  }

  // Check for potential issues
  if (extractedJob.status === 'expired') {
    warnings.push('Job is marked as expired');
  }

  if (
    extractedJob.apply_url &&
    typeof extractedJob.apply_url === 'string' &&
    (!extractedJob.apply_url.startsWith('http://') &&
      !extractedJob.apply_url.startsWith('https://'))
  ) {
    warnings.push('apply_url may have invalid format');
  }

  return {
    isValid: missingFields.length === 0,
    missingFields,
    warnings,
  };
}