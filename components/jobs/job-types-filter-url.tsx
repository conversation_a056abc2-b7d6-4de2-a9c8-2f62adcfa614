'use client';

import { MultiSelectUrlFilter } from '@/components/common/MultiSelectUrlFilter';
import { JOB_TYPES } from '@/lib/job-types';

interface JobTypesFilterUrlProps {
  disabled?: boolean;
}

/**
 * Job Types filter component with URL state
 * Now uses the generic MultiSelectUrlFilter component
 */
export function JobTypesFilterUrl({
  disabled = false,
}: JobTypesFilterUrlProps) {
  return (
    <MultiSelectUrlFilter
      label="Job Types"
      paramName="types"
      options={JOB_TYPES}
      disabled={disabled}
      searchable={false}
      clearable={false}
      showCount={true}
    />
  );
}
