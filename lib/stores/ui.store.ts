/**
 * UI Store - Manages global UI state (modals, toasts, notifications, etc.)
 */

import { useEffect, useState } from 'react';
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import type { LoadingState } from './types';

/**
 * Modal Configuration
 */
type ModalConfig = {
  id: string;
  type: 'dialog' | 'sheet' | 'popover' | 'alert';
  title?: string;
  content?: React.ReactNode;
  data?: Record<string, unknown>;
  onClose?: () => void;
  closeOnOutsideClick?: boolean;
  closeOnEscape?: boolean;
};

/**
 * Toast Configuration
 */
type ToastConfig = {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title?: string;
  message: string;
  duration?: number; // in milliseconds
  action?: {
    label: string;
    onClick: () => void;
  };
  dismissible?: boolean;
};

/**
 * Notification Configuration
 */
type NotificationConfig = {
  id: string;
  type: 'info' | 'warning' | 'error' | 'success';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  persistent?: boolean; // Whether it should persist across sessions
  data?: Record<string, unknown>;
};

/**
 * Sidebar State
 */
type SidebarState = {
  isOpen: boolean;
  isCollapsed: boolean;
  activeSection?: string;
};

/**
 * Theme Configuration
 */
type ThemeConfig = {
  mode: 'light' | 'dark' | 'system';
  accentColor: string;
  radius: 'small' | 'medium' | 'large';
};

/**
 * UI State Interface
 */
export interface UIState extends LoadingState {
  // Modal management
  modals: ModalConfig[];
  activeModal: string | null;

  // Toast management
  toasts: ToastConfig[];

  // Notification management
  notifications: NotificationConfig[];
  unreadCount: number;

  // Sidebar state
  sidebar: SidebarState;

  // Theme configuration
  theme: ThemeConfig;

  // Global loading states
  globalLoading: Record<string, boolean>;

  // Page title and meta
  pageTitle: string;
  breadcrumbs: Array<{ label: string; href?: string }>;

  // Actions
  actions: {
    // Modal management
    openModal: (config: Omit<ModalConfig, 'id'>) => string;
    closeModal: (id: string) => void;
    closeAllModals: () => void;
    updateModal: (id: string, updates: Partial<ModalConfig>) => void;

    // Toast management
    showToast: (config: Omit<ToastConfig, 'id'>) => string;
    hideToast: (id: string) => void;
    clearAllToasts: () => void;

    // Notification management
    addNotification: (
      config: Omit<NotificationConfig, 'id' | 'timestamp' | 'read'>
    ) => string;
    markNotificationRead: (id: string) => void;
    markAllNotificationsRead: () => void;
    removeNotification: (id: string) => void;
    clearAllNotifications: () => void;

    // Sidebar management
    toggleSidebar: () => void;
    setSidebarOpen: (isOpen: boolean) => void;
    setSidebarCollapsed: (isCollapsed: boolean) => void;
    setActiveSection: (section: string) => void;

    // Theme management
    setThemeMode: (mode: ThemeConfig['mode']) => void;
    setAccentColor: (color: string) => void;
    setRadius: (radius: ThemeConfig['radius']) => void;

    // Global loading management
    setGlobalLoading: (key: string, loading: boolean) => void;
    clearGlobalLoading: (key: string) => void;

    // Page management
    setPageTitle: (title: string) => void;
    setBreadcrumbs: (
      breadcrumbs: Array<{ label: string; href?: string }>
    ) => void;

    // Clear error
    clearError: () => void;

    // Reset store
    reset: () => void;
  };
}

/**
 * Generate unique ID
 */
function generateId(): string {
  return `ui_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Initial state
 */
const initialState: Omit<UIState, 'actions'> = {
  modals: [],
  activeModal: null,
  toasts: [],
  notifications: [],
  unreadCount: 0,
  sidebar: {
    isOpen: true,
    isCollapsed: false,
    activeSection: undefined,
  },
  theme: {
    mode: 'system',
    accentColor: '#007acc',
    radius: 'medium',
  },
  globalLoading: {},
  pageTitle: '',
  breadcrumbs: [],
  loading: false,
  error: null,
};

/**
 * UI Store with DevTools
 */
export const useUIStore = create<UIState>()(
  devtools(
    (set, get) => ({
      ...initialState,

      actions: {
        // Modal management
        openModal: (config) => {
          const id = generateId();
          const modal: ModalConfig = {
            id,
            closeOnOutsideClick: true,
            closeOnEscape: true,
            ...config,
          };

          set((state) => ({
            modals: [...state.modals, modal],
            activeModal: id,
          }));

          return id;
        },

        closeModal: (id) => {
          set((state) => ({
            modals: state.modals.filter((modal) => modal.id !== id),
            activeModal: state.activeModal === id ? null : state.activeModal,
          }));
        },

        closeAllModals: () => {
          set({
            modals: [],
            activeModal: null,
          });
        },

        updateModal: (id, updates) => {
          set((state) => ({
            modals: state.modals.map((modal) =>
              modal.id === id ? { ...modal, ...updates } : modal
            ),
          }));
        },

        // Toast management
        showToast: (config) => {
          const id = generateId();
          const toast: ToastConfig = {
            id,
            duration: 5000,
            dismissible: true,
            ...config,
          };

          set((state) => ({
            toasts: [...state.toasts, toast],
          }));

          // Auto-dismiss toast if duration is set
          if (toast.duration && toast.duration > 0) {
            setTimeout(() => {
              get().actions.hideToast(id);
            }, toast.duration);
          }

          return id;
        },

        hideToast: (id) => {
          set((state) => ({
            toasts: state.toasts.filter((toast) => toast.id !== id),
          }));
        },

        clearAllToasts: () => {
          set({ toasts: [] });
        },

        // Notification management
        addNotification: (config) => {
          const id = generateId();
          const notification: NotificationConfig = {
            id,
            timestamp: new Date().toISOString(),
            read: false,
            ...config,
          };

          set((state) => ({
            notifications: [notification, ...state.notifications],
            unreadCount: state.unreadCount + 1,
          }));

          return id;
        },

        markNotificationRead: (id) => {
          set((state) => {
            const notification = state.notifications.find((n) => n.id === id);
            if (!notification || notification.read) {
              return state;
            }

            return {
              notifications: state.notifications.map((n) =>
                n.id === id ? { ...n, read: true } : n
              ),
              unreadCount: Math.max(0, state.unreadCount - 1),
            };
          });
        },

        markAllNotificationsRead: () => {
          set((state) => ({
            notifications: state.notifications.map((n) => ({
              ...n,
              read: true,
            })),
            unreadCount: 0,
          }));
        },

        removeNotification: (id) => {
          set((state) => {
            const notification = state.notifications.find((n) => n.id === id);
            const wasUnread = notification && !notification.read;

            return {
              notifications: state.notifications.filter((n) => n.id !== id),
              unreadCount: wasUnread
                ? Math.max(0, state.unreadCount - 1)
                : state.unreadCount,
            };
          });
        },

        clearAllNotifications: () => {
          set({
            notifications: [],
            unreadCount: 0,
          });
        },

        // Sidebar management
        toggleSidebar: () => {
          set((state) => ({
            sidebar: {
              ...state.sidebar,
              isOpen: !state.sidebar.isOpen,
            },
          }));
        },

        setSidebarOpen: (isOpen) => {
          set((state) => ({
            sidebar: {
              ...state.sidebar,
              isOpen,
            },
          }));
        },

        setSidebarCollapsed: (isCollapsed) => {
          set((state) => ({
            sidebar: {
              ...state.sidebar,
              isCollapsed,
            },
          }));
        },

        setActiveSection: (activeSection) => {
          set((state) => ({
            sidebar: {
              ...state.sidebar,
              activeSection,
            },
          }));
        },

        // Theme management
        setThemeMode: (mode) => {
          set((state) => ({
            theme: {
              ...state.theme,
              mode,
            },
          }));
        },

        setAccentColor: (accentColor) => {
          set((state) => ({
            theme: {
              ...state.theme,
              accentColor,
            },
          }));
        },

        setRadius: (radius) => {
          set((state) => ({
            theme: {
              ...state.theme,
              radius,
            },
          }));
        },

        // Global loading management
        setGlobalLoading: (key, loading) => {
          set((state) => ({
            globalLoading: {
              ...state.globalLoading,
              [key]: loading,
            },
          }));
        },

        clearGlobalLoading: (key) => {
          set((state) => {
            const newGlobalLoading = { ...state.globalLoading };
            delete newGlobalLoading[key];
            return { globalLoading: newGlobalLoading };
          });
        },

        // Page management
        setPageTitle: (pageTitle) => {
          set({ pageTitle });
        },

        setBreadcrumbs: (breadcrumbs) => {
          set({ breadcrumbs });
        },

        // Clear error
        clearError: () => {
          set({ error: null });
        },

        // Reset store
        reset: () => {
          set(initialState);
        },
      },
    }),
    { name: 'UIStore' }
  )
);

/**
 * Hydrated hook for SSR-safe usage
 */
export const useUIStoreHydrated = () => {
  const store = useUIStore();
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    setIsHydrated(true);
  }, []);

  if (!isHydrated) {
    return {
      ...initialState,
      actions: {
        openModal: () => '',
        closeModal: () => {
          // No-op during hydration
        },
        closeAllModals: () => {
          // No-op during hydration
        },
        updateModal: () => {
          // No-op during hydration
        },
        showToast: () => '',
        hideToast: () => {
          // No-op during hydration
        },
        clearAllToasts: () => {
          // No-op during hydration
        },
        addNotification: () => '',
        markNotificationRead: () => {
          // No-op during hydration
        },
        markAllNotificationsRead: () => {
          // No-op during hydration
        },
        removeNotification: () => {
          // No-op during hydration
        },
        clearAllNotifications: () => {
          // No-op during hydration
        },
        toggleSidebar: () => {
          // No-op during hydration
        },
        setSidebarOpen: () => {
          // No-op during hydration
        },
        setSidebarCollapsed: () => {
          // No-op during hydration
        },
        setActiveSection: () => {
          // No-op during hydration
        },
        setThemeMode: () => {
          // No-op during hydration
        },
        setAccentColor: () => {
          // No-op during hydration
        },
        setRadius: () => {
          // No-op during hydration
        },
        setGlobalLoading: () => {
          // No-op during hydration
        },
        clearGlobalLoading: () => {
          // No-op during hydration
        },
        setPageTitle: () => {
          // No-op during hydration
        },
        setBreadcrumbs: () => {
          // No-op during hydration
        },
        clearError: () => {
          // No-op during hydration
        },
        reset: () => {
          // No-op during hydration
        },
      },
    } as UIState;
  }

  return store;
};
