'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { CAREER_LEVELS } from '@/lib/career-levels';
import { countries } from '@/lib/data/countries';
import { CURRENCY_CODES } from '@/lib/data/currencies';
import { LANGUAGE_CODES } from '@/lib/data/languages';
import { JOB_TYPES } from '@/lib/job-types';
import { REMOTE_REGIONS, WORKPLACE_TYPES } from '@/lib/workplace';

type JobBoardFormData = {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  airtableBaseId: string;
  airtableTableName: string;
  dailyLimit: number;
  strategy: 'newest_first' | 'best_match' | 'random';
  avoidRepostingDays: number;
  types: string[];
  workplaceTypes: string[];
  careerLevels: string[];
  salaryMin: string;
  salaryMax: string;
  salaryCurrencies: string[];
  includeKeywords: string;
  excludeKeywords: string;
  countries: string[];
  languages: string[];
  remoteRegions: string[];
};

type JobBoardConfig = {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  airtable: {
    baseId: string;
    tableName: string;
  };
  posting: {
    dailyLimit: number;
    strategy: 'newest_first' | 'best_match' | 'random';
    avoidRepostingDays: number;
  };
  filters: Record<string, unknown>;
};

type CreateBoardDialogProps = {
  open: boolean;
  onClose: () => void;
  onSubmit: (config: JobBoardConfig) => Promise<void>;
};

// Filter section component moved outside to avoid nested component definition
// Type for array fields in JobBoardFormData
type ArrayFieldName = keyof Pick<
  JobBoardFormData,
  | 'types'
  | 'workplaceTypes'
  | 'careerLevels'
  | 'salaryCurrencies'
  | 'countries'
  | 'languages'
  | 'remoteRegions'
>;

const FilterSection = ({
  title,
  items,
  selectedItems,
  field,
  columns = 3,
  helpText,
  setFormData,
}: {
  title: string;
  items: readonly string[] | string[];
  selectedItems: string[];
  field: ArrayFieldName;
  columns?: number;
  helpText?: string;
  setFormData: (updater: (prev: JobBoardFormData) => JobBoardFormData) => void;
}) => {
  const handleCheckboxChange = (
    fieldName: ArrayFieldName,
    item: string,
    checked: boolean
  ) => {
    setFormData((prev) => {
      const currentArray = prev[fieldName] || [];
      return {
        ...prev,
        [fieldName]: checked
          ? [...currentArray, item]
          : currentArray.filter((v) => v !== item),
      };
    });
  };

  return (
    <div className="space-y-2">
      <Label className="font-medium text-sm">{title}</Label>
      <div
        className={`grid grid-cols-${columns} max-h-40 gap-2 overflow-y-auto rounded-md border bg-gray-50 p-3`}
      >
        {items.map((item) => (
          <div className="flex items-center space-x-2" key={item}>
            <Checkbox
              checked={selectedItems.includes(item)}
              id={`${field}-${item}`}
              onCheckedChange={(checked) =>
                handleCheckboxChange(field, item, !!checked)
              }
            />
            <Label
              className="cursor-pointer text-xs"
              htmlFor={`${field}-${item}`}
            >
              {item}
            </Label>
          </div>
        ))}
      </div>
      {helpText && <p className="text-muted-foreground text-xs">{helpText}</p>}
    </div>
  );
};

export function CreateBoardDialog({
  open,
  onClose,
  onSubmit,
}: CreateBoardDialogProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Form state for managing dynamic board configuration
  const [formData, setFormData] = useState<JobBoardFormData>({
    id: '',
    name: '',
    description: '',
    enabled: true,
    airtableBaseId: '',
    airtableTableName: 'Jobs',
    dailyLimit: 10,
    strategy: 'newest_first' as 'newest_first' | 'best_match' | 'random',
    avoidRepostingDays: 30,
    // Filter fields
    types: [] as string[],
    workplaceTypes: [] as string[],
    careerLevels: [] as string[],
    salaryMin: '',
    salaryMax: '',
    salaryCurrencies: [] as string[],
    includeKeywords: '',
    excludeKeywords: '',
    countries: [] as string[],
    languages: [] as string[],
    remoteRegions: [] as string[],
  });

  // Helper function to validate form data
  const validateFormData = (): string | null => {
    if (!formData.name.trim()) {
      return 'Name is required';
    }
    if (!formData.id.trim()) {
      return 'ID is required';
    }
    if (!formData.airtableBaseId.trim()) {
      return 'Airtable Base ID is required';
    }
    return null;
  };

  // Helper function to build basic filters
  const buildBasicFilters = () => {
    const filters: Record<string, unknown> = {};

    if (formData.types.length > 0) {
      filters.types = formData.types;
    }
    if (formData.workplaceTypes.length > 0) {
      filters.workplaceTypes = formData.workplaceTypes;
    }
    if (formData.careerLevels.length > 0) {
      filters.careerLevels = formData.careerLevels;
    }

    return filters;
  };

  // Helper function to build salary filters
  const buildSalaryFilters = (filters: Record<string, unknown>) => {
    if (formData.salaryMin) {
      filters.salaryMin = Number(formData.salaryMin);
    }
    if (formData.salaryMax) {
      filters.salaryMax = Number(formData.salaryMax);
    }
    if (formData.salaryCurrencies.length > 0) {
      filters.salaryCurrencies = formData.salaryCurrencies;
    }
  };

  // Helper function to build keyword filters
  const buildKeywordFilters = (filters: Record<string, unknown>) => {
    if (formData.includeKeywords.trim()) {
      filters.includeKeywords = formData.includeKeywords
        .split(',')
        .map((k) => k.trim())
        .filter((k) => k);
    }
    if (formData.excludeKeywords.trim()) {
      filters.excludeKeywords = formData.excludeKeywords
        .split(',')
        .map((k) => k.trim())
        .filter((k) => k);
    }
  };

  // Helper function to build location filters
  const buildLocationFilters = (filters: Record<string, unknown>) => {
    if (formData.countries.length > 0) {
      filters.countries = formData.countries;
    }
    if (formData.languages.length > 0) {
      filters.languages = formData.languages;
    }
    if (formData.remoteRegions.length > 0) {
      filters.remoteRegions = formData.remoteRegions;
    }
  };

  // Helper function to build filters object (now simplified)
  const buildFilters = () => {
    const filters = buildBasicFilters();
    buildSalaryFilters(filters);
    buildKeywordFilters(filters);
    buildLocationFilters(filters);
    return filters;
  };

  // Helper function to build config object
  const buildConfig = () => {
    const filters = buildFilters();

    return {
      id: formData.id,
      name: formData.name,
      description: formData.description,
      enabled: formData.enabled,
      airtable: {
        baseId: formData.airtableBaseId,
        tableName: formData.airtableTableName,
      },
      posting: {
        dailyLimit: formData.dailyLimit,
        strategy: formData.strategy,
        avoidRepostingDays: formData.avoidRepostingDays,
      },
      filters,
    };
  };

  // Helper function to reset form data
  const resetForm = () => {
    setFormData({
      id: '',
      name: '',
      description: '',
      enabled: true,
      airtableBaseId: '',
      airtableTableName: 'Jobs',
      dailyLimit: 10,
      strategy: 'newest_first',
      avoidRepostingDays: 30,
      types: [],
      workplaceTypes: [],
      careerLevels: [],
      salaryMin: '',
      salaryMax: '',
      salaryCurrencies: [],
      includeKeywords: '',
      excludeKeywords: '',
      countries: [],
      languages: [],
      remoteRegions: [],
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    // Validate form data
    const validationError = validateFormData();
    if (validationError) {
      setError(validationError);
      return;
    }

    const config = buildConfig();

    setLoading(true);
    try {
      await onSubmit(config);
      resetForm();
    } catch (submitError) {
      setError(
        submitError instanceof Error ? submitError.message : 'Unknown error'
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog onOpenChange={onClose} open={open}>
      <DialogContent className="max-h-[90vh] max-w-4xl overflow-hidden">
        <DialogHeader>
          <DialogTitle>Create Job Board</DialogTitle>
          <DialogDescription>
            Configure a new automated job posting board with filtering and
            Airtable integration.
          </DialogDescription>
        </DialogHeader>

        <form className="space-y-6" onSubmit={handleSubmit}>
          {error && (
            <div className="rounded-md bg-destructive/15 px-3 py-2 text-destructive text-sm">
              {error}
            </div>
          )}

          <Tabs className="w-full" defaultValue="basic">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="basic">Basic Info</TabsTrigger>
              <TabsTrigger value="posting">Posting Settings</TabsTrigger>
              <TabsTrigger value="filters">Job Filters</TabsTrigger>
            </TabsList>

            <TabsContent className="space-y-4" value="basic">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="id">Job Board ID *</Label>
                  <Input
                    id="id"
                    onChange={(e) =>
                      setFormData((prev) => ({ ...prev, id: e.target.value }))
                    }
                    placeholder="e.g., remote-react-jobs"
                    required
                    value={formData.id}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="name">Name *</Label>
                  <Input
                    id="name"
                    onChange={(e) =>
                      setFormData((prev) => ({ ...prev, name: e.target.value }))
                    }
                    placeholder="e.g., Remote React Jobs"
                    required
                    value={formData.name}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      description: e.target.value,
                    }))
                  }
                  placeholder="Brief description of this job board"
                  rows={2}
                  value={formData.description}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="airtableBaseId">Airtable Base ID *</Label>
                  <Input
                    id="airtableBaseId"
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        airtableBaseId: e.target.value,
                      }))
                    }
                    placeholder="app..."
                    required
                    value={formData.airtableBaseId}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="airtableTableName">Table Name</Label>
                  <Input
                    id="airtableTableName"
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        airtableTableName: e.target.value,
                      }))
                    }
                    placeholder="Jobs"
                    value={formData.airtableTableName}
                  />
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  checked={formData.enabled}
                  id="enabled"
                  onCheckedChange={(checked) =>
                    setFormData((prev) => ({ ...prev, enabled: !!checked }))
                  }
                />
                <Label htmlFor="enabled">Enable this job board</Label>
              </div>
            </TabsContent>

            <TabsContent className="space-y-4" value="posting">
              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="dailyLimit">Daily Limit</Label>
                  <Input
                    id="dailyLimit"
                    max="50"
                    min="1"
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        dailyLimit: Number(e.target.value),
                      }))
                    }
                    type="number"
                    value={formData.dailyLimit}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="strategy">Strategy</Label>
                  <Select
                    onValueChange={(value) =>
                      setFormData((prev) => ({
                        ...prev,
                        strategy: value as JobBoardFormData['strategy'],
                      }))
                    }
                    value={formData.strategy}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="newest_first">Newest First</SelectItem>
                      <SelectItem value="best_match">Best Match</SelectItem>
                      <SelectItem value="random">Random</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="avoidRepostingDays">
                    Avoid Reposting (days)
                  </Label>
                  <Input
                    id="avoidRepostingDays"
                    max="365"
                    min="1"
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        avoidRepostingDays: Number(e.target.value),
                      }))
                    }
                    type="number"
                    value={formData.avoidRepostingDays}
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent className="space-y-6" value="filters">
              <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
                <div className="space-y-4">
                  <FilterSection
                    field="types"
                    items={JOB_TYPES}
                    selectedItems={formData.types}
                    setFormData={setFormData}
                    title="Job Types"
                  />

                  <FilterSection
                    field="workplaceTypes"
                    items={WORKPLACE_TYPES.filter(
                      (type) => type !== 'Not specified'
                    )}
                    selectedItems={formData.workplaceTypes}
                    setFormData={setFormData}
                    title="Workplace Types"
                  />

                  <FilterSection
                    columns={2}
                    field="remoteRegions"
                    items={REMOTE_REGIONS}
                    selectedItems={formData.remoteRegions}
                    setFormData={setFormData}
                    title="Remote Regions"
                  />

                  <FilterSection
                    field="careerLevels"
                    items={CAREER_LEVELS.filter(
                      (level) => level !== 'Not Specified'
                    )}
                    selectedItems={formData.careerLevels}
                    setFormData={setFormData}
                    title="Career Levels"
                  />
                </div>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>Salary Range</Label>
                    <div className="grid grid-cols-2 gap-2">
                      <Input
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            salaryMin: e.target.value,
                          }))
                        }
                        placeholder="Min salary"
                        type="number"
                        value={formData.salaryMin}
                      />
                      <Input
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            salaryMax: e.target.value,
                          }))
                        }
                        placeholder="Max salary"
                        type="number"
                        value={formData.salaryMax}
                      />
                    </div>
                  </div>

                  <FilterSection
                    columns={4}
                    field="salaryCurrencies"
                    items={CURRENCY_CODES}
                    selectedItems={formData.salaryCurrencies}
                    setFormData={setFormData}
                    title="Accepted Currencies"
                  />

                  <div className="space-y-2">
                    <Label htmlFor="includeKeywords">Include Keywords</Label>
                    <Input
                      id="includeKeywords"
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          includeKeywords: e.target.value,
                        }))
                      }
                      placeholder="React, TypeScript, Node.js (comma-separated)"
                      value={formData.includeKeywords}
                    />
                    <p className="text-muted-foreground text-xs">
                      Jobs must contain at least one of these keywords
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="excludeKeywords">Exclude Keywords</Label>
                    <Input
                      id="excludeKeywords"
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          excludeKeywords: e.target.value,
                        }))
                      }
                      placeholder="PHP, WordPress (comma-separated)"
                      value={formData.excludeKeywords}
                    />
                    <p className="text-muted-foreground text-xs">
                      Jobs with these keywords will be filtered out
                    </p>
                  </div>

                  <FilterSection
                    columns={2}
                    field="countries"
                    items={countries}
                    selectedItems={formData.countries}
                    setFormData={setFormData}
                    title="Target Countries"
                  />

                  <FilterSection
                    columns={6}
                    field="languages"
                    items={LANGUAGE_CODES}
                    selectedItems={formData.languages}
                    setFormData={setFormData}
                    title="Required Languages"
                  />
                </div>
              </div>
            </TabsContent>
          </Tabs>

          <DialogFooter>
            <Button onClick={onClose} type="button" variant="outline">
              Cancel
            </Button>
            <Button disabled={loading} type="submit">
              {loading ? 'Creating...' : 'Create Job Board'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
