'use client';

import { useEffect, useRef } from 'react';
import { toast } from 'sonner';
import { createClient } from '@/lib/supabase';

type JobInsertEvent = {
  eventType: 'INSERT';
  new: {
    id: string;
    source_type: string;
    source_name: string;
    title: string;
    company: string | null;
    created_at: string;
  };
};

type UseRealTimeJobTrackerProps = {
  sourceId: string;
  sourceName: string;
  isActive: boolean;
  onJobsReceived?: (count: number) => void;
  onComplete?: () => void;
};

export function useRealTimeJobTracker({
  sourceId,
  sourceName,
  isActive,
  onJobsReceived,
  onComplete,
}: UseRealTimeJobTrackerProps) {
  // biome-ignore lint/suspicious/noExplicitAny: Supabase subscription type is complex
  const subscriptionRef = useRef<any>(null);
  const jobCountRef = useRef(0);
  const startTimeRef = useRef<number>(Date.now());
  const completionTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (!isActive) {
      // Clean up when not active
      if (subscriptionRef.current) {
        subscriptionRef.current.unsubscribe();
        subscriptionRef.current = null;
      }
      if (completionTimeoutRef.current) {
        clearTimeout(completionTimeoutRef.current);
        completionTimeoutRef.current = null;
      }
      jobCountRef.current = 0;
      return;
    }

    const supabase = createClient();
    startTimeRef.current = Date.now();
    jobCountRef.current = 0;

    // Map sourceId to source_type for filtering
    const sourceTypeMap: Record<string, string> = {
      'jobdata-api': 'jobdata_api',
      workable: 'workable',
      'wwr-rss': 'wwr_rss',
    };

    const sourceType = sourceTypeMap[sourceId];
    if (!sourceType) {
      // Unknown source ID - silently skip tracking
      return;
    }

    // Subscribe to job insertions for this specific source
    const subscription = supabase
      .channel(`job-insertions-${sourceId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'jobs',
          filter: `source_type=eq.${sourceType}`,
        },
        (payload: JobInsertEvent) => {
          const job = payload.new;

          // Only count jobs created after we started tracking (avoid counting old jobs)
          const jobCreatedAt = new Date(job.created_at).getTime();
          if (jobCreatedAt >= startTimeRef.current - 5000) {
            // 5s buffer for timing
            jobCountRef.current += 1;

            // Show real-time toast for job batches
            if (jobCountRef.current % 10 === 0 || jobCountRef.current === 1) {
              toast.success(
                `🔄 ${jobCountRef.current} jobs ingested from ${sourceName}`,
                {
                  description: `Latest: ${job.title} at ${
                    job.company || 'Unknown Company'
                  }`,
                  duration: 3000,
                }
              );
            }

            // Call callback if provided
            onJobsReceived?.(jobCountRef.current);

            // Reset completion timeout (jobs are still coming in)
            if (completionTimeoutRef.current) {
              clearTimeout(completionTimeoutRef.current);
            }

            // Set completion timeout - if no jobs for 30 seconds, consider complete
            completionTimeoutRef.current = setTimeout(() => {
              if (jobCountRef.current > 0) {
                toast.success(
                  `✅ ${sourceName} completed: ${jobCountRef.current} jobs total`,
                  {
                    description: `Successfully ingested ${jobCountRef.current} jobs`,
                    duration: 5000,
                  }
                );
                onComplete?.();
              }

              // Clean up subscription
              if (subscriptionRef.current) {
                subscriptionRef.current.unsubscribe();
                subscriptionRef.current = null;
              }
            }, 30_000); // 30 second timeout
          }
        }
      )
      .subscribe();

    subscriptionRef.current = subscription;

    // Cleanup function
    return () => {
      if (subscriptionRef.current) {
        subscriptionRef.current.unsubscribe();
      }
      if (completionTimeoutRef.current) {
        clearTimeout(completionTimeoutRef.current);
      }
    };
  }, [isActive, sourceId, sourceName, onJobsReceived, onComplete]);

  return {
    jobCount: jobCountRef.current,
    isTracking: isActive && !!subscriptionRef.current,
  };
}

export default useRealTimeJobTracker;
