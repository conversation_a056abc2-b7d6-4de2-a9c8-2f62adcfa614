/**
 * Legacy Validation Module
 *
 * This file is maintained for backwards compatibility.
 * New code should use the consolidated validation modules in lib/validation/
 *
 * @deprecated Use lib/validation/ modules instead
 */

// Re-export everything from the new consolidated validation modules
export * from './validation';
export {
  type ExtractJobRequest,
  ExtractJobRequestSchema,
  ExtractRequestSchema,
} from './validation/api-validation';
// Legacy exports for backwards compatibility
export {
  validateAirtableBaseId,
  validateAirtableConfig as validateAirtableConfigWithErrors,
  validateAirtablePat,
  validateAirtableTableName,
  validatePatWithError,
} from './validation/config-validation';
