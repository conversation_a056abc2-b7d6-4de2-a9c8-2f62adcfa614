'use client';

import {
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  Database,
  Globe,
  MessageSquare,
  Minus,
  RefreshCw,
  XCircle,
  Zap,
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';

type SourceData = {
  id: string;
  name: string;
  type: 'API' | 'RSS' | 'Manual' | 'Generator';
  endpoint: string;
  enabled: boolean;
  description: string;
  config: Record<string, unknown>;
  created_at: string;
  updated_at: string;
  stats: {
    last_fetch_at: string | null;
    total_jobs_fetched: number;
    jobs_fetched_today: number;
    success_rate: number;
    avg_response_time_ms: number;
    error_count_24h: number;
    last_error: string | null;
    last_error_at: string | null;
    rate_limit_info: Record<string, unknown>;
  };
};

type ConnectedSourcesHealthData = {
  success: boolean;
  sources?: SourceData[];
  count?: number;
};

type ConnectedSourcesHealthCardProps = {
  data?: ConnectedSourcesHealthData;
};

export function ConnectedSourcesHealthCard({
  data,
}: ConnectedSourcesHealthCardProps) {
  if (!data?.success) {
    return (
      <Card className="col-span-full lg:col-span-2">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 font-medium text-sm">
            <Database className="h-4 w-4" />
            Connected Sources Health
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2">
            <div className="h-4 w-4 animate-spin">
              <RefreshCw className="h-4 w-4" />
            </div>
            <div className="text-muted-foreground">Loading sources...</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const sources = data.sources || [];
  const activeSources = sources;
  const healthySources = sources.filter(
    (source) => getSourceHealthStatus(source) === 'healthy'
  );

  function getSourceHealthStatus(
    source: SourceData
  ): 'healthy' | 'warning' | 'critical' {
    const stats = source.stats;
    if (!stats) {
      return 'critical';
    }

    const hasRecentErrors = (stats.error_count_24h ?? 0) > 0;
    const hasHighErrorRate = (stats.error_count_24h ?? 0) >= 5;
    const hasLowSuccessRate = (stats.success_rate ?? 0) < 0.7;
    const hasSlowResponse = (stats.avg_response_time_ms ?? 0) > 5000;

    if (hasHighErrorRate || hasLowSuccessRate) {
      return 'critical';
    }
    if (hasRecentErrors || hasSlowResponse) {
      return 'warning';
    }
    return 'healthy';
  }

  function getStatusIcon(status: string) {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="h-3 w-3 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="h-3 w-3 text-yellow-500" />;
      case 'critical':
        return <XCircle className="h-3 w-3 text-red-500" />;
      case 'disabled':
        return <Minus className="h-3 w-3 text-gray-500" />;
      default:
        return <Clock className="h-3 w-3 text-gray-500" />;
    }
  }

  function getStatusBadgeColor(status: string) {
    switch (status) {
      case 'healthy':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'critical':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'disabled':
        return 'bg-gray-100 text-gray-600 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-600 border-gray-200';
    }
  }

  function getTypeIcon(type: string) {
    switch (type) {
      case 'API':
        return <Globe className="h-3 w-3" />;
      case 'RSS':
        return <Activity className="h-3 w-3" />;
      case 'Manual':
        return <MessageSquare className="h-3 w-3" />;
      case 'Generator':
        return <Zap className="h-3 w-3" />;
      default:
        return <Database className="h-3 w-3" />;
    }
  }

  function formatLastFetch(timestamp: string | null): string {
    if (!timestamp) {
      return 'Never';
    }

    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) {
      return 'Just now';
    }
    if (diffMins < 60) {
      return `${diffMins}m ago`;
    }
    if (diffHours < 24) {
      return `${diffHours}h ago`;
    }
    return `${diffDays}d ago`;
  }

  function formatResponseTime(ms: number): string {
    if (ms < 1000) {
      return `${ms}ms`;
    }
    return `${(ms / 1000).toFixed(1)}s`;
  }

  function getSuccessRateColor(successRate: number): string {
    if (successRate > 0.8) {
      return 'text-green-600';
    }
    if (successRate > 0.5) {
      return 'text-yellow-600';
    }
    return 'text-red-600';
  }

  const totalJobsToday = sources.reduce(
    (sum, source) => sum + (source.stats?.jobs_fetched_today ?? 0),
    0
  );
  const avgResponseTime =
    sources.length > 0
      ? Math.round(
          sources.reduce(
            (sum, source) => sum + (source.stats?.avg_response_time_ms ?? 0),
            0
          ) / sources.length
        )
      : 0;

  return (
    <Card className="col-span-full lg:col-span-2">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 font-medium text-sm">
          <Database className="h-4 w-4" />
          Connected Sources Health
          <Badge className="ml-auto" variant="outline">
            {activeSources.length}/{sources.length} active
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Overview Stats */}
        <div className="grid grid-cols-4 gap-4">
          <div className="text-center">
            <div className="font-bold text-green-600 text-lg">
              {healthySources.length}
            </div>
            <div className="text-muted-foreground text-xs">Healthy</div>
          </div>
          <div className="text-center">
            <div className="font-bold text-lg">{totalJobsToday}</div>
            <div className="text-muted-foreground text-xs">Jobs Today</div>
          </div>
          <div className="text-center">
            <div className="font-bold text-lg">
              {formatResponseTime(avgResponseTime)}
            </div>
            <div className="text-muted-foreground text-xs">Avg Response</div>
          </div>
          <div className="text-center">
            <div className="font-bold text-lg">
              {activeSources.length > 0
                ? Math.round(
                    (healthySources.length / activeSources.length) * 100
                  )
                : 0}
              %
            </div>
            <div className="text-muted-foreground text-xs">Health Rate</div>
          </div>
        </div>

        {/* Health Progress Bar */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span>Overall Health</span>
            <span className="font-medium">
              {activeSources.length > 0
                ? Math.round(
                    (healthySources.length / activeSources.length) * 100
                  )
                : 0}
              %
            </span>
          </div>
          <Progress
            className="h-2"
            value={
              activeSources.length > 0
                ? (healthySources.length / activeSources.length) * 100
                : 0
            }
          />
        </div>

        {/* Source Details */}
        <div className="space-y-3">
          <div className="font-medium text-sm">Source Details</div>
          {sources.map((source) => {
            const status = getSourceHealthStatus(source);
            const stats = source.stats;

            return (
              <div className="space-y-2 rounded-lg border p-3" key={source.id}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {getTypeIcon(source.type)}
                    <span className="font-medium text-sm">{source.name}</span>
                    <Badge className="text-xs" variant="outline">
                      {source.type}
                    </Badge>
                  </div>
                  <Badge
                    className={`text-xs ${getStatusBadgeColor(status)}`}
                    variant="outline"
                  >
                    {getStatusIcon(status)}
                    <span className="ml-1">{status}</span>
                  </Badge>
                </div>

                <div className="grid grid-cols-2 gap-4 text-xs">
                  <div className="space-y-1">
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground">Last Fetch:</span>
                      <span>{formatLastFetch(stats.last_fetch_at)}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground">
                        Success Rate:
                      </span>
                      <span
                        className={getSuccessRateColor(stats.success_rate ?? 0)}
                      >
                        {((stats.success_rate ?? 0) * 100).toFixed(1)}%
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground">
                        Response Time:
                      </span>
                      <span
                        className={
                          (stats.avg_response_time_ms ?? 0) > 3000
                            ? 'text-yellow-600'
                            : 'text-green-600'
                        }
                      >
                        {formatResponseTime(stats.avg_response_time_ms ?? 0)}
                      </span>
                    </div>
                  </div>
                  <div className="space-y-1">
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground">Total Jobs:</span>
                      <span>
                        {(stats.total_jobs_fetched ?? 0).toLocaleString()}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground">Today:</span>
                      <span className="font-medium">
                        {stats.jobs_fetched_today ?? 0}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground">
                        Errors (24h):
                      </span>
                      <span
                        className={
                          (stats.error_count_24h ?? 0) > 0
                            ? 'text-red-600'
                            : 'text-green-600'
                        }
                      >
                        {stats.error_count_24h ?? 0}
                      </span>
                    </div>
                  </div>
                </div>

                {stats.last_error && (stats.error_count_24h ?? 0) > 0 && (
                  <div className="border-t pt-2">
                    <div className="text-red-600 text-xs">
                      <span className="font-medium">Last Error:</span>{' '}
                      {stats.last_error}
                    </div>
                    <div className="text-muted-foreground text-xs">
                      {formatLastFetch(stats.last_error_at)}
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </div>

        {sources.length === 0 && (
          <div className="rounded-lg border-2 border-dashed p-6 text-center">
            <Database className="mx-auto mb-2 h-8 w-8 text-muted-foreground" />
            <div className="font-medium text-sm">No Sources Configured</div>
            <div className="text-muted-foreground text-xs">
              Configure data sources to see health information
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
