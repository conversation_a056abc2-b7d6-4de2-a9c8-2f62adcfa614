import assert from 'node:assert/strict';
import test from 'node:test';
import { CAREER_LEVELS } from '../../lib/career-levels.js';
import { DEFAULTS, SCHEMA_LIMITS } from '../../lib/constants.js';
import { CURRENCY_CODES } from '../../lib/data/currencies.js';
import { LANGUAGE_CODES } from '../../lib/data/languages.js';
// Import actual production code
import {
  JOB_DATA_TO_DB_FIELD_MAP,
  mapFields,
  mapJobDataToDbFields,
} from '../../lib/field-mapping.js';
import { JobExtractionSchema, JobSchema } from '../../lib/job-schema.js';
import { JOB_STATUSES } from '../../lib/job-status.js';
import { JOB_TYPES } from '../../lib/job-types.js';
import {
  mapJob<PERSON><PERSON><PERSON><PERSON><PERSON>ields,
  mapWWRFields,
  validate<PERSON>ob,
} from '../../lib/job-validation.js';
import { estimateReadingTime } from '../../lib/text-utils.js';
import type { JobData } from '../../lib/types.js';
import {
  arrayToString,
  isFilterActive,
  stringToArray,
} from '../../lib/utils/filter-utils.js';
import { REMOTE_REGIONS, WORKPLACE_TYPES } from '../../lib/workplace.js';

// Test data using real schema types
const createValidJobData = (overrides: Partial<JobData> = {}): JobData => ({
  sourcedAt: '2024-01-15T10:30:00.000Z',
  sourceUrl: 'https://example.com/job/123',
  title: 'Senior Software Engineer',
  description:
    'We are looking for a senior software engineer to join our team...',
  status: 'active',
  company: 'TechCorp Inc.',
  type: 'Full-time',
  apply_url: 'https://techcorp.com/careers/senior-engineer',
  apply_method: 'link',
  posted_date: '2024-01-15T08:00:00.000Z',
  salary_min: 120_000,
  salary_max: 180_000,
  salary_currency: 'USD',
  salary_unit: 'year',
  workplace_type: 'Remote',
  remote_region: 'Worldwide',
  timezone_requirements: 'UTC-8 to UTC+2',
  workplace_city: null,
  workplace_country: null,
  benefits: 'Health insurance, 401k, unlimited PTO',
  application_requirements:
    "Bachelor's degree in Computer Science or equivalent experience",
  valid_through: '2025-03-15T23:59:59.000Z',
  job_identifier: 'TECH-2024-001',
  job_source_name: 'TechCorp Careers',
  department: 'Engineering',
  travel_required: false,
  career_level: ['Senior'],
  visa_sponsorship: 'No',
  languages: ['en'],
  skills: 'JavaScript, TypeScript, React, Node.js, PostgreSQL',
  qualifications: '5+ years of full-stack development experience',
  education_requirements:
    "Bachelor's degree in Computer Science or related field",
  experience_requirements: '5+ years of professional software development',
  responsibilities:
    'Design and implement scalable web applications, mentor junior developers',
  featured: false,
  industry: 'Technology',
  occupational_category: 'Software Development',
  ...overrides,
});

const createJobDataApiData = (overrides: Record<string, unknown> = {}) => ({
  id: 'jobdata_123',
  title: 'Software Engineer',
  content: JSON.stringify({
    description: 'A great software engineering role',
    requirements: '5+ years experience',
    benefits: 'Health insurance, 401k',
  }),
  location: 'San Francisco, CA',
  salary: '100000-150000',
  posted_at: '2024-01-15T08:00:00.000Z',
  url: 'https://jobdata.com/job/123',
  raw_data: {
    original_job: {
      id: 'jobdata_123',
      company: {
        name: 'TechCorp',
      },
    },
  },
  ...overrides,
});

const createWWRJobData = (overrides: Record<string, unknown> = {}) => ({
  title: 'TechCorp: Senior Software Engineer',
  content:
    'We are looking for a senior engineer. To apply: https://techcorp.com/apply',
  url: 'https://weworkremotely.com/job/123',
  posted_at: '2024-01-15T08:00:00.000Z',
  ...overrides,
});

// Real Production Field Mapping Tests
test('JOB_DATA_TO_DB_FIELD_MAP contains expected mappings', () => {
  assert.equal(
    JOB_DATA_TO_DB_FIELD_MAP.sourcedAt,
    'sourced_at',
    'Should map sourcedAt to sourced_at'
  );
  assert.equal(
    JOB_DATA_TO_DB_FIELD_MAP.sourceUrl,
    'source_url',
    'Should map sourceUrl to source_url'
  );

  // Test that it's a const assertion (readonly)
  assert.ok(
    typeof JOB_DATA_TO_DB_FIELD_MAP === 'object',
    'Should be an object'
  );
});

test('mapFields transforms field names according to mapping', () => {
  const source = {
    sourcedAt: '2024-01-15T10:30:00.000Z',
    sourceUrl: 'https://example.com/job',
    title: 'Software Engineer',
    unmappedField: 'should remain',
  };

  const fieldMap = {
    sourcedAt: 'sourced_at',
    sourceUrl: 'source_url',
  };

  const result = mapFields(source, fieldMap);

  assert.equal(
    result.sourced_at,
    '2024-01-15T10:30:00.000Z',
    'Should map sourcedAt to sourced_at'
  );
  assert.equal(
    result.source_url,
    'https://example.com/job',
    'Should map sourceUrl to source_url'
  );
  assert.equal(
    result.title,
    'Software Engineer',
    'Should preserve unmapped fields'
  );
  assert.equal(
    result.unmappedField,
    'should remain',
    'Should preserve unmapped fields'
  );
  assert.equal(
    result.sourcedAt,
    undefined,
    'Should not include original mapped fields'
  );
  assert.equal(
    result.sourceUrl,
    undefined,
    'Should not include original mapped fields'
  );
});

test('mapFields handles empty source object', () => {
  const result = mapFields({}, { test: 'mapped_test' });
  assert.deepEqual(result, {}, 'Should return empty object for empty source');
});

test('mapFields handles empty field map', () => {
  const source = { title: 'Test', company: 'TestCorp' };
  const result = mapFields(source, {});
  assert.deepEqual(
    result,
    source,
    'Should return source unchanged when no field map'
  );
});

test('mapFields skips undefined values', () => {
  const source = {
    sourcedAt: '2024-01-15T10:30:00.000Z',
    sourceUrl: undefined,
    title: 'Test',
  };

  const fieldMap = {
    sourcedAt: 'sourced_at',
    sourceUrl: 'source_url',
  };

  const result = mapFields(source, fieldMap);

  assert.equal(
    result.sourced_at,
    '2024-01-15T10:30:00.000Z',
    'Should map defined values'
  );
  assert.equal(result.title, 'Test', 'Should preserve unmapped fields');
  assert.ok(!('source_url' in result), 'Should skip undefined values');
  assert.ok(
    !('sourceUrl' in result),
    'Should not include original undefined fields'
  );
});

test('mapFields preserves null values', () => {
  const source = {
    sourcedAt: null,
    title: 'Test',
  };

  const fieldMap = {
    sourcedAt: 'sourced_at',
  };

  const result = mapFields(source, fieldMap);

  assert.equal(result.sourced_at, null, 'Should preserve null values');
  assert.equal(result.title, 'Test', 'Should preserve unmapped fields');
});

test('mapJobDataToDbFields transforms JobData to database format', () => {
  const jobData = createValidJobData();
  const result = mapJobDataToDbFields(jobData);

  // Test field name transformations
  assert.equal(
    result.sourced_at,
    jobData.sourcedAt,
    'Should map sourcedAt to sourced_at'
  );
  assert.equal(
    result.source_url,
    jobData.sourceUrl,
    'Should map sourceUrl to source_url'
  );

  // Test that other fields are preserved
  assert.equal(result.title, jobData.title, 'Should preserve title');
  assert.equal(result.company, jobData.company, 'Should preserve company');
  assert.equal(
    result.description,
    jobData.description,
    'Should preserve description'
  );

  // Test that original camelCase fields are not included
  assert.ok(!('sourcedAt' in result), 'Should not include original sourcedAt');
  assert.ok(!('sourceUrl' in result), 'Should not include original sourceUrl');
});

// Real Production Validation Tests
test('validateJob validates job data correctly', () => {
  const validJobData = {
    title: 'Software Engineer',
    company: 'TechCorp',
    description: 'A great job opportunity',
    sourceUrl: 'https://example.com/job',
  };

  const result = validateJob(validJobData);

  assert.equal(
    typeof result.isValid,
    'boolean',
    'Should return isValid boolean'
  );
  assert.ok(
    Array.isArray(result.missingFields),
    'Should return missingFields array'
  );
  assert.equal(
    typeof result.externalId,
    'string',
    'Should generate external ID'
  );
  assert.ok(
    result.externalId.length > 0,
    'Should generate non-empty external ID'
  );
});

test('validateJob identifies missing required fields', () => {
  const invalidJobData = {
    // Missing title and description
    company: 'TechCorp',
    sourceUrl: 'https://example.com/job',
  };

  const result = validateJob(invalidJobData);

  assert.equal(result.isValid, false, 'Should identify invalid data');
  assert.ok(result.missingFields.length > 0, 'Should identify missing fields');
  assert.ok(result.externalId.length > 0, 'Should still generate external ID');
});

test('mapJobDataApiFields transforms JobDataAPI data correctly', () => {
  const jobDataApiData = createJobDataApiData();
  const result = mapJobDataApiFields(jobDataApiData);

  // Test basic field mapping
  assert.equal(result.title, jobDataApiData.title, 'Should map title');
  assert.equal(
    result.company,
    'TechCorp',
    'Should extract company from nested structure'
  );
  assert.equal(
    result.source_url,
    jobDataApiData.url,
    'Should map url to source_url'
  );
  assert.equal(
    result.external_id,
    `jobdataapi_${jobDataApiData.id}`,
    'Should map id to external_id with prefix'
  );

  // Test that description is set as placeholder
  assert.equal(
    result.description,
    '',
    'Should set empty description placeholder'
  );
});

test('mapJobDataApiFields handles invalid JSON content', () => {
  const jobDataApiData = createJobDataApiData({
    content: 'invalid json content',
  });

  const result = mapJobDataApiFields(jobDataApiData);

  // Should not throw and should handle gracefully
  assert.equal(
    result.title,
    jobDataApiData.title,
    'Should still map basic fields'
  );
  assert.equal(
    result.external_id,
    `jobdataapi_${jobDataApiData.id}`,
    'Should still map external_id with prefix'
  );
});

test('mapWWRFields transforms WeWorkRemotely data correctly', () => {
  const wwrData = createWWRJobData();
  const result = mapWWRFields(wwrData);

  // Test company extraction from title
  assert.equal(result.company, 'TechCorp', 'Should extract company from title');
  assert.equal(
    result.title,
    'TechCorp: Senior Software Engineer',
    'Should preserve full title'
  );

  // Test apply URL extraction from content - might be undefined if not found
  assert.ok(
    result.apply_url === undefined || typeof result.apply_url === 'string',
    'Should handle apply URL extraction'
  );
  // The source_url is set to the extracted apply URL, not the original job URL
  assert.ok(
    typeof result.source_url === 'string' || result.source_url === null,
    'Should set source_url based on extracted URL'
  );
});

test('mapWWRFields handles title without company prefix', () => {
  const wwrData = createWWRJobData({
    title: 'Senior Software Engineer', // No company prefix
  });

  const result = mapWWRFields(wwrData);

  assert.equal(
    result.title,
    'Senior Software Engineer',
    'Should use full title when no company prefix'
  );
  assert.equal(
    result.company,
    null,
    'Should set company to null when not extractable'
  );
});

test('mapWWRFields handles content without apply URL', () => {
  const wwrData = createWWRJobData({
    content: 'We are looking for a senior engineer. Great opportunity!',
  });

  const result = mapWWRFields(wwrData);

  assert.ok(
    result.apply_url === null || result.apply_url === undefined,
    'Should set apply_url to null or undefined when not found'
  );
  assert.equal(
    result.description,
    wwrData.content,
    'Should use content as description'
  );
});

// Real Production Schema Tests
test('JobExtractionSchema validates with real production schema', () => {
  const validJobData = {
    title: 'Senior Software Engineer',
    description: 'We are seeking a Senior Software Engineer...',
    status: 'active' as const,
    company: 'TechCorp Inc.',
    type: 'Full-time' as const,
    apply_url: 'https://techcorp.com/careers',
    apply_method: 'link' as const,
    posted_date: '2024-01-15T08:00:00.000Z',
    salary_min: 120_000,
    salary_max: 180_000,
    salary_currency: 'USD' as const,
    salary_unit: 'year' as const,
    workplace_type: 'Remote' as const,
    remote_region: 'Worldwide' as const,
    timezone_requirements: null,
    workplace_city: null,
    workplace_country: null,
    benefits: 'Health insurance, 401k, unlimited PTO',
    application_requirements: "5+ years experience, Bachelor's degree",
    valid_through: '2025-03-15T23:59:59.000Z',
    job_identifier: 'TECH-2024-001',
    job_source_name: 'TechCorp Careers',
    department: 'Engineering',
    travel_required: false,
    career_level: ['Senior'],
    visa_sponsorship: 'No',
    languages: ['en'],
    skills: 'JavaScript, TypeScript, React, Node.js, PostgreSQL',
    qualifications: '5+ years of software development experience',
    education_requirements: "Bachelor's degree in Computer Science",
    experience_requirements: '5+ years in software development',
    responsibilities: 'Design and develop scalable web applications',
    featured: false,
    industry: 'Technology',
    occupational_category: 'Software Development',
  };

  try {
    const result = JobExtractionSchema.parse(validJobData);
    assert.ok(result, 'Should parse valid job data with real schema');
    assert.equal(result.title, 'Senior Software Engineer');
    assert.equal(result.salary_min, 120_000);
    assert.equal(result.career_level?.[0], 'Senior');
    assert.equal(result.languages?.[0], 'en');
  } catch (error) {
    assert.fail(`Real schema should parse valid data: ${error}`);
  }
});

test('JobExtractionSchema enforces character limits', () => {
  const jobDataWithLongFields = {
    title: 'Software Engineer',
    description: 'A job description',
    status: 'active' as const,
    benefits: 'A'.repeat(SCHEMA_LIMITS.benefits + 100), // Exceeds limit
    skills: 'B'.repeat(SCHEMA_LIMITS.skills + 100), // Exceeds limit
    company: null,
    type: null,
    apply_url: null,
    apply_method: null,
    posted_date: null,
    salary_min: null,
    salary_max: null,
    salary_currency: null,
    salary_unit: null,
    workplace_type: null,
    remote_region: null,
    timezone_requirements: null,
    workplace_city: null,
    workplace_country: null,
    application_requirements: null,
    valid_through: null,
    job_identifier: null,
    job_source_name: null,
    department: null,
    travel_required: null,
    career_level: null,
    visa_sponsorship: null,
    languages: null,
    qualifications: null,
    education_requirements: null,
    experience_requirements: null,
    responsibilities: null,
    featured: null,
    industry: null,
    occupational_category: null,
  };

  try {
    JobExtractionSchema.parse(jobDataWithLongFields);
    assert.fail('Should reject data exceeding character limits');
  } catch (error) {
    assert.ok(
      error,
      'Should throw validation error for character limit violations'
    );
  }
});

test('JobSchema includes metadata fields', () => {
  const jobWithMetadata = createValidJobData();

  try {
    const result = JobSchema.parse(jobWithMetadata);
    assert.ok(result, 'Should parse job with metadata');
    assert.equal(result.sourcedAt, '2024-01-15T10:30:00.000Z');
    assert.equal(result.sourceUrl, 'https://example.com/job/123');
  } catch (error) {
    assert.fail(`JobSchema should parse valid data: ${error}`);
  }
});

// Real Production Utility Tests
test('estimateReadingTime calculates time correctly using real constants', () => {
  const shortText = 'Hello world';
  const result = estimateReadingTime(shortText);

  // Should use DEFAULTS.READING_WPM from constants
  const expectedTime = Math.ceil(2 / DEFAULTS.READING_WPM);
  assert.equal(
    result,
    expectedTime,
    'Should calculate reading time using production constants'
  );
});

test('estimateReadingTime handles empty text', () => {
  const result = estimateReadingTime('');
  assert.equal(typeof result, 'number', 'Should return a number');
  assert.ok(result >= 0, 'Should return non-negative value');
});

test('arrayToString converts arrays to comma-separated strings', () => {
  assert.equal(
    arrayToString(['a', 'b', 'c']),
    'a,b,c',
    'Should join array with commas'
  );
  assert.equal(
    arrayToString([]),
    '',
    'Should return empty string for empty array'
  );
  assert.equal(
    arrayToString(undefined),
    '',
    'Should return empty string for undefined'
  );
});

test('stringToArray converts comma-separated strings to arrays', () => {
  assert.deepEqual(
    stringToArray('a,b,c'),
    ['a', 'b', 'c'],
    'Should split string by commas'
  );
  assert.deepEqual(
    stringToArray('a, b , c'),
    ['a', 'b', 'c'],
    'Should trim whitespace'
  );
  assert.deepEqual(
    stringToArray(''),
    [],
    'Should return empty array for empty string'
  );
  assert.deepEqual(
    stringToArray('a,,b'),
    ['a', 'b'],
    'Should filter out empty strings'
  );
});

test('isFilterActive identifies active filter values', () => {
  // Active values
  assert.equal(
    isFilterActive('test'),
    true,
    'Non-empty string should be active'
  );
  assert.equal(
    isFilterActive(['item']),
    true,
    'Non-empty array should be active'
  );
  assert.equal(isFilterActive(42), true, 'Number should be active');
  assert.equal(isFilterActive(true), true, 'Boolean true should be active');
  assert.equal(isFilterActive(false), true, 'Boolean false should be active');

  // Inactive values
  assert.equal(
    isFilterActive(undefined),
    false,
    'Undefined should be inactive'
  );
  assert.equal(isFilterActive(null), false, 'Null should be inactive');
  assert.equal(isFilterActive(''), false, 'Empty string should be inactive');
  assert.equal(isFilterActive('all'), false, "String 'all' should be inactive");
  assert.equal(isFilterActive([]), false, 'Empty array should be inactive');
});

// Real Production Constants Tests
test('SCHEMA_LIMITS contains expected character limits', () => {
  assert.ok(
    typeof SCHEMA_LIMITS.benefits === 'number',
    'Should have benefits limit'
  );
  assert.ok(
    typeof SCHEMA_LIMITS.skills === 'number',
    'Should have skills limit'
  );
  assert.ok(
    typeof SCHEMA_LIMITS.responsibilities === 'number',
    'Should have responsibilities limit'
  );
  assert.ok(
    typeof SCHEMA_LIMITS.qualifications === 'number',
    'Should have qualifications limit'
  );
  assert.ok(
    typeof SCHEMA_LIMITS.education_requirements === 'number',
    'Should have education_requirements limit'
  );
  assert.ok(
    typeof SCHEMA_LIMITS.experience_requirements === 'number',
    'Should have experience_requirements limit'
  );
  assert.ok(
    typeof SCHEMA_LIMITS.application_requirements === 'number',
    'Should have application_requirements limit'
  );

  // All limits should be positive
  assert.ok(SCHEMA_LIMITS.benefits > 0, 'Benefits limit should be positive');
  assert.ok(SCHEMA_LIMITS.skills > 0, 'Skills limit should be positive');
  assert.ok(
    SCHEMA_LIMITS.responsibilities > 0,
    'Responsibilities limit should be positive'
  );
});

test('DEFAULTS contains expected reading speed', () => {
  assert.ok(
    typeof DEFAULTS.READING_WPM === 'number',
    'Should have reading WPM constant'
  );
  assert.ok(DEFAULTS.READING_WPM > 0, 'Reading WPM should be positive');
  assert.ok(
    DEFAULTS.READING_WPM >= 100 && DEFAULTS.READING_WPM <= 300,
    'Reading WPM should be reasonable'
  );
});

// Real Production Enum Tests
test('Production enums are properly defined', () => {
  // Test that enums are arrays with expected values
  assert.ok(Array.isArray(CURRENCY_CODES), 'CURRENCY_CODES should be array');
  assert.ok(Array.isArray(LANGUAGE_CODES), 'LANGUAGE_CODES should be array');
  assert.ok(Array.isArray(CAREER_LEVELS), 'CAREER_LEVELS should be array');
  assert.ok(Array.isArray(JOB_TYPES), 'JOB_TYPES should be array');
  assert.ok(Array.isArray(JOB_STATUSES), 'JOB_STATUSES should be array');
  assert.ok(Array.isArray(WORKPLACE_TYPES), 'WORKPLACE_TYPES should be array');
  assert.ok(Array.isArray(REMOTE_REGIONS), 'REMOTE_REGIONS should be array');

  // Test that enums have reasonable lengths
  assert.ok(CURRENCY_CODES.length > 0, 'Should have currency codes');
  assert.ok(LANGUAGE_CODES.length > 0, 'Should have language codes');
  assert.ok(CAREER_LEVELS.length > 0, 'Should have career levels');
  assert.ok(JOB_TYPES.length > 0, 'Should have job types');
  assert.ok(JOB_STATUSES.length > 0, 'Should have job statuses');
  assert.ok(WORKPLACE_TYPES.length > 0, 'Should have workplace types');
  assert.ok(REMOTE_REGIONS.length > 0, 'Should have remote regions');
});

// Real Data Flow Integration Tests
test('Complete data transformation pipeline', () => {
  // 1. Start with raw job data
  const rawJobData = createValidJobData();

  // 2. Validate with schema
  const schemaResult = JobSchema.safeParse(rawJobData);
  assert.ok(schemaResult.success, 'Should pass schema validation');

  // 3. Transform to database format
  const dbFields = mapJobDataToDbFields(rawJobData);
  assert.equal(
    dbFields.sourced_at,
    rawJobData.sourcedAt,
    'Should transform field names'
  );
  assert.equal(
    dbFields.source_url,
    rawJobData.sourceUrl,
    'Should transform field names'
  );

  // 4. Validate with custom validation
  const validationResult = validateJob(rawJobData);
  assert.equal(validationResult.isValid, true, 'Should pass custom validation');

  // 5. Test utility functions
  const readingTime = estimateReadingTime(rawJobData.description);
  assert.ok(readingTime > 0, 'Should estimate reading time');

  // 6. Test filter utilities
  const careerLevelString = arrayToString(rawJobData.career_level || []);
  const careerLevelArray = stringToArray(careerLevelString);
  assert.deepEqual(
    careerLevelArray,
    rawJobData.career_level,
    'Should round-trip array conversion'
  );
});

test('JobDataAPI to database transformation pipeline', () => {
  // 1. Start with JobDataAPI format
  const jobDataApiData = createJobDataApiData();

  // 2. Transform to our format
  const transformedData = mapJobDataApiFields(jobDataApiData);

  // 3. Validate the transformation
  assert.equal(
    transformedData.external_id,
    `jobdataapi_${jobDataApiData.id}`,
    'Should preserve external ID with prefix'
  );
  assert.equal(
    transformedData.source_url,
    jobDataApiData.url,
    'Should map URL correctly'
  );
  assert.ok(
    typeof transformedData.description === 'string',
    'Should extract description'
  );

  // 4. Test that it can be validated
  const validationResult = validateJob(transformedData);
  assert.ok(
    validationResult.externalId.length > 0,
    'Should generate external ID'
  );
});

test('WeWorkRemotely to database transformation pipeline', () => {
  // 1. Start with WWR format
  const wwrData = createWWRJobData();

  // 2. Transform to our format
  const transformedData = mapWWRFields(wwrData);

  // 3. Validate the transformation
  assert.equal(transformedData.company, 'TechCorp', 'Should extract company');
  assert.ok(
    transformedData.apply_url === undefined ||
      typeof transformedData.apply_url === 'string',
    'Should handle apply URL extraction'
  );
  assert.ok(
    typeof transformedData.source_url === 'string' ||
      transformedData.source_url === null,
    'Should set source URL based on extracted URL'
  );

  // 4. Test that it can be validated
  const validationResult = validateJob(transformedData);
  assert.ok(
    validationResult.externalId.length > 0,
    'Should generate external ID'
  );
});
