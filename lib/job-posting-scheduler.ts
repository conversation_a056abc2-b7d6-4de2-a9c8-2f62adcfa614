import { recordJobBoardPosting } from './database-utils';
import {
  getActiveJobBoards,
  type JobBoardConfig,
  matchesJobBoard,
} from './job-board-config';
import type { DatabaseJob } from './storage';
import { createServerClient } from './supabase';
import { logger } from './utils';

type PostingCandidate = {
  job: DatabaseJob;
  score: number; // Relevance score for "best_match" strategy
};

type PostingResult = {
  jobId: string;
  boardId: string;
  success: boolean;
  airtableRecordId?: string;
  error?: string;
};

/**
 * Check if we can post more jobs to a board today
 */
async function canPostToBoard(
  boardId: string,
  dailyLimit: number
): Promise<boolean> {
  const supabase = await createServerClient();
  const today = new Date().toISOString().split('T')[0];

  const { data, error } = await supabase
    .from('job_board_stats')
    .select('posted_count')
    .eq('board_id', boardId)
    .eq('date', today)
    .single();

  if (error && error.code !== 'PGRST116') {
    // PGRST116 = no rows returned
    logger.error(`Error checking board stats for ${boardId}:`, error);
    return false;
  }

  const postedToday = data?.posted_count || 0;
  return postedToday < dailyLimit;
}

/**
 * Get jobs eligible for posting to a specific board
 */
// biome-ignore lint/complexity/noExcessiveCognitiveComplexity: Complex eligibility logic with multiple database queries and conditions is necessary
async function getEligibleJobs(
  board: JobBoardConfig
): Promise<PostingCandidate[]> {
  const supabase = await createServerClient();

  // Get jobs that haven't been posted to this board yet
  const { data: jobs, error } = await supabase
    .from('eligible_jobs_for_posting')
    .select('*')
    .limit(100); // Process in batches

  if (error) {
    logger.error('Error fetching eligible jobs:', error);
    return [];
  }

  // Check which jobs have already been posted to this board
  const jobIds = jobs.map((job) => job.id);
  const { data: existingPostings } = await supabase
    .from('job_board_postings')
    .select('job_id')
    .eq('board_id', board.id)
    .in('job_id', jobIds);

  const postedJobIds = new Set(existingPostings?.map((p) => p.job_id) || []);

  // Filter jobs that match board criteria and haven't been posted
  const candidates: PostingCandidate[] = [];

  for (const job of jobs) {
    if (postedJobIds.has(job.id)) {
      continue;
    }

    // Check if job matches board filters
    if (!matchesJobBoard(job, board)) {
      continue;
    }

    // Check company reposting rules
    if (board.posting.avoidRepostingDays > 0) {
      // biome-ignore lint/nursery/noAwaitInLoop: Sequential database queries required to check eligibility per job
      const { data: recentPosts } = await supabase
        .from('job_board_postings')
        .select('posted_at')
        .eq('board_id', board.id)
        .eq('company', job.company)
        .gte(
          'posted_at',
          new Date(
            Date.now() - board.posting.avoidRepostingDays * 24 * 60 * 60 * 1000
          ).toISOString()
        )
        .limit(1);

      if (recentPosts && recentPosts.length > 0) {
        continue;
      }
    }

    // Calculate relevance score for "best_match" strategy
    let score = 0;

    // Score based on keyword matches
    if (board.filters.includeKeywords) {
      const jobText = `${job.title} ${job.description} ${
        job.skills || ''
      }`.toLowerCase();
      score +=
        board.filters.includeKeywords.filter((keyword) =>
          jobText.includes(keyword.toLowerCase())
        ).length * 10;
    }

    // Score based on salary match
    if (board.filters.salaryMin && job.salary_min) {
      const salaryRatio = job.salary_min / board.filters.salaryMin;
      score += Math.min(salaryRatio * 5, 10);
    }

    // Score based on career level match
    if (board.filters.careerLevels && job.career_level) {
      const matchingLevels = job.career_level.filter((level: string) =>
        board.filters.careerLevels?.includes(level)
      ).length;
      score += matchingLevels * 5;
    }

    // Boost score for preferred sources
    if (board.filters.sourcePriority && job.source_type) {
      const priority = board.filters.sourcePriority.indexOf(job.source_type);
      if (priority >= 0) {
        score += (board.filters.sourcePriority.length - priority) * 3;
      }
    }

    candidates.push({ job, score });
  }

  // Sort based on posting strategy
  switch (board.posting.strategy) {
    case 'newest_first':
      candidates.sort(
        (a, b) =>
          new Date(b.job.created_at).getTime() -
          new Date(a.job.created_at).getTime()
      );
      break;

    case 'best_match':
      candidates.sort((a, b) => b.score - a.score);
      break;

    case 'random':
      // Fisher-Yates shuffle
      for (let i = candidates.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [candidates[i], candidates[j]] = [candidates[j], candidates[i]];
      }
      break;
    default:
      // Default to newest_first if strategy is not recognized
      candidates.sort(
        (a, b) =>
          new Date(b.job.posted_date || 0).getTime() -
          new Date(a.job.posted_date || 0).getTime()
      );
      break;
  }

  return candidates;
}

/**
 * Post a job to Airtable for a specific board
 */
async function postJobToAirtable(
  job: DatabaseJob,
  board: JobBoardConfig
): Promise<string> {
  // Use board-specific Airtable config - no environment variable fallbacks
  const pat = board.airtable.pat;
  const baseId = board.airtable.baseId;
  const tableName = board.airtable.tableName;

  if (!(pat && baseId && tableName)) {
    throw new Error(
      `Airtable not fully configured for board ${board.id}. Missing pat, baseId, or tableName.`
    );
  }

  // Send to Airtable using existing endpoint
  const response = await fetch(
    `${process.env.NEXT_PUBLIC_BASE_URL || ''}/api/airtable-send`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        jobData: job,
        // Use board-specific config from database
        airtableConfig: {
          pat,
          baseId,
          tableName,
        },
      }),
    }
  );

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to post to Airtable');
  }

  const result = await response.json();
  return result.recordId || result.id || 'unknown';
}

/**
 * Record a posting in the database
 */
async function recordPosting(
  jobId: string,
  boardId: string,
  status: 'posted' | 'failed',
  airtableRecordId?: string,
  errorMessage?: string
): Promise<void> {
  // Use centralized utility for recording the posting
  await recordJobBoardPosting(
    jobId,
    boardId,
    status,
    airtableRecordId,
    errorMessage
  );

  if (status === 'posted') {
    const supabase = await createServerClient();
    const now = new Date();
    const today = now.toISOString().split('T')[0];

    // Update job record
    const { data: job } = await supabase
      .from('jobs')
      .select('posted_to_boards')
      .eq('id', jobId)
      .single();

    const postedToBoards = job?.posted_to_boards || [];
    if (!postedToBoards.includes(boardId)) {
      postedToBoards.push(boardId);
    }

    await supabase
      .from('jobs')
      .update({
        posted_to_boards: postedToBoards,
        last_posted_at: now.toISOString(),
      })
      .eq('id', jobId);

    // Update daily stats
    await supabase.from('job_board_stats').upsert(
      {
        board_id: boardId,
        date: today,
        posted_count: 1,
        last_posted_at: now.toISOString(),
      },
      {
        onConflict: 'board_id,date',
        count: 'exact',
      }
    );

    // Increment the count if record already exists
    await supabase.rpc('increment_posted_count', {
      p_board_id: boardId,
      p_date: today,
    });
  }
}

/**
 * Process postings for a single board
 */
async function processBoard(board: JobBoardConfig): Promise<PostingResult[]> {
  const results: PostingResult[] = [];

  // Check if we can post more today
  if (!(await canPostToBoard(board.id, board.posting.dailyLimit))) {
    logger.info(`Daily limit reached for board ${board.id}`);
    return results;
  }

  // Get eligible jobs
  const candidates = await getEligibleJobs(board);
  logger.info(`Found ${candidates.length} eligible jobs for board ${board.id}`);

  // Calculate how many we can post
  const supabase = await createServerClient();
  const today = new Date().toISOString().split('T')[0];
  const { data: stats } = await supabase
    .from('job_board_stats')
    .select('posted_count')
    .eq('board_id', board.id)
    .eq('date', today)
    .single();

  const postedToday = stats?.posted_count || 0;
  const remainingSlots = board.posting.dailyLimit - postedToday;
  const toPost = Math.min(candidates.length, remainingSlots);

  // Post jobs
  for (let i = 0; i < toPost; i++) {
    const { job } = candidates[i];

    try {
      // biome-ignore lint/nursery/noAwaitInLoop: Sequential posting required to respect rate limits and avoid overwhelming APIs
      const airtableRecordId = await postJobToAirtable(job, board);
      await recordPosting(job.id, board.id, 'posted', airtableRecordId);

      results.push({
        jobId: job.id,
        boardId: board.id,
        success: true,
        airtableRecordId,
      });

      logger.info(`Posted job ${job.id} to board ${board.id}`);

      // Small delay to avoid rate limits
      await new Promise((resolve) => setTimeout(resolve, 1000));
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      await recordPosting(job.id, board.id, 'failed', undefined, errorMessage);

      results.push({
        jobId: job.id,
        boardId: board.id,
        success: false,
        error: errorMessage,
      });

      logger.error(`Failed to post job ${job.id} to board ${board.id}:`, error);
    }
  }

  return results;
}

/**
 * Main scheduler function - process all active boards
 */
export async function runJobPostingScheduler(): Promise<{
  success: boolean;
  results: Record<string, PostingResult[]>;
  summary: {
    totalPosted: number;
    totalFailed: number;
    boardsSummary: Record<string, { posted: number; failed: number }>;
  };
}> {
  const boards = getActiveJobBoards();
  const allResults: Record<string, PostingResult[]> = {};
  let totalPosted = 0;
  let totalFailed = 0;
  const boardsSummary: Record<string, { posted: number; failed: number }> = {};

  logger.info(
    `Starting job posting scheduler for ${boards.length} active boards`
  );

  for (const board of boards) {
    try {
      // biome-ignore lint/nursery/noAwaitInLoop: Sequential board processing required to avoid rate limit conflicts between different boards
      const results = await processBoard(board);
      allResults[board.id] = results;

      const posted = results.filter((r) => r.success).length;
      const failed = results.filter((r) => !r.success).length;

      boardsSummary[board.id] = { posted, failed };
      totalPosted += posted;
      totalFailed += failed;

      logger.info(`Board ${board.id}: posted ${posted}, failed ${failed}`);
    } catch (error) {
      logger.error(`Error processing board ${board.id}:`, error);
      allResults[board.id] = [];
      boardsSummary[board.id] = { posted: 0, failed: 0 };
    }
  }

  return {
    success: true,
    results: allResults,
    summary: {
      totalPosted,
      totalFailed,
      boardsSummary,
    },
  };
}

/**
 * Create the RPC function for incrementing posted count
 * Run this SQL in your Supabase dashboard:
 *
 * CREATE OR REPLACE FUNCTION increment_posted_count(p_board_id TEXT, p_date DATE)
 * RETURNS void AS $$
 * BEGIN
 *   UPDATE job_board_stats
 *   SET posted_count = posted_count + 1,
 *       last_posted_at = NOW()
 *   WHERE board_id = p_board_id AND date = p_date;
 * END;
 * $$ LANGUAGE plpgsql;
 */
