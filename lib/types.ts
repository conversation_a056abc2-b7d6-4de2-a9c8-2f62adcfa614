import type { JobStatus } from './job-status';

// Job-related interfaces (extensible objects)
export type JobData = {
  sourcedAt: string;
  sourceUrl: string;
  title: string;
  company: string | null;
  type: string | null;
  description: string;
  apply_url: string | null;
  apply_method: string | null;
  posted_date: string | null;
  status: JobStatus;
  salary_min: number | null;
  salary_max: number | null;
  salary_currency: string | null;
  salary_unit: string | null;
  workplace_type: string | null;
  remote_region: string | null;
  timezone_requirements: string | null;
  workplace_city: string | null;
  workplace_country: string | null;
  benefits: string | null;
  application_requirements: string | null;
  valid_through: string | null;
  job_identifier: string | null;
  job_source_name: string | null;
  source_name: string | null;
  department: string | null;
  travel_required: boolean | null;
  career_level: string[] | null;
  visa_sponsorship: string | null;
  languages: string[] | null;
  skills: string | null;
  qualifications: string | null;
  education_requirements: string | null;
  experience_requirements: string | null;
  responsibilities: string | null;
  featured: boolean | null;
  industry: string | null;
  occupational_category: string | null;
};

export type JobMetadata = {
  id: string;
  timestamp: string;
  filename: string;
  processing_time: number;
  cost_usd: number;
  tokens_input: number;
  tokens_output: number;
  tokens_total: number;
  model: string;
  source_content: string;
};

export type SavedJob = {
  job: JobData;
  metadata: JobMetadata;
};

// API Response types (simple data structures)
export type ExtractResponse = {
  job: JobData;
  metadata: {
    duration: number;
    timestamp: string;
    usage: {
      promptTokens: number;
      completionTokens: number;
      totalTokens: number;
    };
    cost: {
      input: number;
      output: number;
      total: number;
    };
    model?: string;
    finishReason?: string;
    response?: {
      id: string;
      model: string;
      timestamp: Date;
    };
    contentLength?: number;
    estimatedReadingTime?: number;
    promptUsed?: string;
    promptLength?: number;
  };
};

// Airtable-related interfaces
export type AirtableConfig = {
  hasConfig: boolean;
  baseId: string | null;
  tableName: string | null;
  hasPat: boolean;
};

// Error types
export type ApiError = {
  error: string;
  details?: string;
  parsedValue?: unknown;
};

// Loading states
export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

// Tab types
export type TabId = 'airtable' | 'monitor' | 'database';

export type Tab = {
  id: TabId;
  label: string;
  icon: string;
};
