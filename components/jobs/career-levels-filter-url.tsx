'use client';

import { MultiSelectUrlFilter } from '@/components/common/MultiSelectUrlFilter';
import { CAREER_LEVELS } from '@/lib/career-levels';

interface CareerLevelsFilterUrlProps {
  disabled?: boolean;
}

/**
 * Career Levels filter component with URL state
 * Now uses the generic MultiSelectUrlFilter component
 */
export function CareerLevelsFilterUrl({
  disabled = false,
}: CareerLevelsFilterUrlProps) {
  return (
    <MultiSelectUrlFilter
      label="Career Levels"
      paramName="levels"
      options={CAREER_LEVELS}
      disabled={disabled}
      searchable={false}
      maxHeight="max-h-48"
      clearable={false}
      showCount={true}
    />
  );
}
