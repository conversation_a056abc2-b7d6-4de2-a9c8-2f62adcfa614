import {
  Alert<PERSON>ircle,
  <PERSON>ert<PERSON>riangle,
  CheckCircle,
  Clock,
  Minus,
  Server,
  Settings,
  XCircle,
} from 'lucide-react';
import type { ReactElement } from 'react';
import { Badge } from '@/components/ui/badge';

/**
 * Shared Health Status Utilities
 *
 * Consolidates common health status patterns used across health components,
 * including status determination, icon mapping, color schemes, and formatters.
 */

// Standard health status types
export type HealthStatus =
  | 'healthy'
  | 'degraded'
  | 'warning'
  | 'unhealthy'
  | 'critical'
  | 'error'
  | 'disabled'
  | 'unknown';

// Health check interface
export type HealthCheck = {
  name: string;
  status: HealthStatus;
  value?: string | number;
  latency?: number;
  error?: string;
  lastCheck?: string;
};

// Health metrics for calculations
export type HealthMetrics = {
  successRate?: number;
  errorCount?: number;
  uptime?: number;
  responseTime?: number;
};

/**
 * Get status icon component for health status
 */
export function getHealthStatusIcon(
  status: HealthStatus,
  size: 'sm' | 'md' = 'md'
): ReactElement {
  const sizeClass = size === 'sm' ? 'h-3 w-3' : 'h-4 w-4';

  switch (status) {
    case 'healthy':
      return <CheckCircle className={`${sizeClass} text-green-500`} />;
    case 'degraded':
    case 'warning':
      return <AlertTriangle className={`${sizeClass} text-yellow-500`} />;
    case 'unhealthy':
    case 'critical':
    case 'error':
      return <XCircle className={`${sizeClass} text-red-500`} />;
    case 'disabled':
      return <Minus className={`${sizeClass} text-gray-500`} />;
    case 'unknown':
      return <Clock className={`${sizeClass} text-gray-500`} />;
    default:
      return <Server className={`${sizeClass} text-gray-500`} />;
  }
}

/**
 * Get CSS classes for health status badge
 */
export function getHealthStatusColor(status: HealthStatus): string {
  switch (status) {
    case 'healthy':
      return 'bg-green-100 text-green-800';
    case 'degraded':
    case 'warning':
      return 'bg-yellow-100 text-yellow-800';
    case 'unhealthy':
    case 'critical':
    case 'error':
      return 'bg-red-100 text-red-800';
    case 'disabled':
      return 'bg-gray-100 text-gray-800';
    case 'unknown':
      return 'bg-gray-100 text-gray-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}

/**
 * Health status badge component
 */
export type HealthStatusBadgeProps = {
  status: HealthStatus;
  showIcon?: boolean;
  size?: 'sm' | 'md';
  className?: string;
};

export function HealthStatusBadge({
  status,
  showIcon = true,
  size = 'md',
  className = '',
}: HealthStatusBadgeProps) {
  return (
    <Badge className={`${getHealthStatusColor(status)} ${className}`}>
      {showIcon && getHealthStatusIcon(status, size)}
      {showIcon && ' '}
      {status.toUpperCase()}
    </Badge>
  );
}

/**
 * Service icon mapping for consistency
 */
export const SERVICE_ICONS = {
  database: <Server className="h-4 w-4 text-blue-500" />,
  workflow: <Settings className="h-4 w-4 text-purple-500" />,
  apify: <Server className="h-4 w-4 text-green-500" />,
  slack: <AlertCircle className="h-4 w-4 text-indigo-500" />,
  environment: <Settings className="h-4 w-4" />,
  sources: <Server className="h-4 w-4" />,
  monitoring: <AlertCircle className="h-4 w-4" />,
} as const;

/**
 * Determine health status based on metrics
 */
export function calculateHealthStatus(metrics: HealthMetrics): HealthStatus {
  const { successRate = 0, errorCount = 0, responseTime } = metrics;

  // Critical if high error count or very low success rate
  if (errorCount >= 5 || successRate < 0.5) {
    return 'critical';
  }

  // Warning if some errors or degraded performance
  if (errorCount > 0 || (responseTime && responseTime > 5000)) {
    return 'warning';
  }

  // Healthy if good success rate
  if (successRate > 0.8) {
    return 'healthy';
  }

  // Unknown if no data
  return 'unknown';
}

/**
 * Format uptime duration
 */
export function formatUptime(seconds: number): string {
  const days = Math.floor(seconds / 86_400);
  const hours = Math.floor((seconds % 86_400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);

  if (days > 0) {
    return `${days}d ${hours}h ${minutes}m`;
  }
  if (hours > 0) {
    return `${hours}h ${minutes}m`;
  }
  return `${minutes}m`;
}

/**
 * Format latency with appropriate units
 */
export function formatLatency(milliseconds: number): string {
  if (milliseconds < 1000) {
    return `${milliseconds}ms`;
  }
  return `${(milliseconds / 1000).toFixed(1)}s`;
}

/**
 * Calculate health rate percentage
 */
export function calculateHealthRate(
  healthyCount: number,
  totalCount: number
): number {
  if (totalCount === 0) {
    return 0;
  }
  return Math.round((healthyCount / totalCount) * 100);
}

/**
 * Get overall health status from multiple checks
 */
export function getOverallHealthStatus(checks: HealthCheck[]): HealthStatus {
  if (checks.length === 0) {
    return 'unknown';
  }

  const statuses = checks.map((check) => check.status);

  // If any critical/error, overall is critical
  if (
    statuses.includes('critical') ||
    statuses.includes('error') ||
    statuses.includes('unhealthy')
  ) {
    return 'critical';
  }

  // If any warnings/degraded, overall is warning
  if (statuses.includes('warning') || statuses.includes('degraded')) {
    return 'warning';
  }

  // If all healthy, overall is healthy
  if (statuses.every((status) => status === 'healthy')) {
    return 'healthy';
  }

  // Default to unknown
  return 'unknown';
}

/**
 * Health check row component for consistent display
 */
export type HealthCheckRowProps = {
  check: HealthCheck;
  icon?: ReactElement;
  showLatency?: boolean;
  className?: string;
};

export function HealthCheckRow({
  check,
  icon,
  showLatency = true,
  className = '',
}: HealthCheckRowProps) {
  return (
    <div className={`flex items-center justify-between ${className}`}>
      <div className="flex items-center gap-2">
        {icon}
        <span className="text-sm">{check.name}</span>
      </div>
      <div className="flex items-center gap-2">
        {getHealthStatusIcon(check.status, 'sm')}
        {showLatency && check.latency && (
          <span className="text-muted-foreground text-sm">
            {formatLatency(check.latency)}
          </span>
        )}
        {check.value && (
          <span className="text-muted-foreground text-sm">{check.value}</span>
        )}
      </div>
    </div>
  );
}

/**
 * Loading state for health cards
 */
export function HealthCardLoading({ title }: { title: string }) {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <span className="font-bold text-2xl">Loading...</span>
        <Badge variant="secondary">--</Badge>
      </div>
      <p className="text-muted-foreground text-xs">{title}</p>
    </div>
  );
}
