import { NextResponse } from 'next/server';
import { getAirtablePat } from '@/lib/secrets-manager';
import { createServiceRoleClient } from '@/lib/supabase';
import { logger } from '@/lib/utils';

// Admin client with service role for bypassing RLS
const supabaseAdmin = createServiceRoleClient();

// Airtable schema types
type AirtableField = {
  id: string;
  name: string;
  type: string;
  options?: {
    choices?: Array<{
      id: string;
      name: string;
      color?: string;
    }>;
    [key: string]: unknown;
  };
};

type AirtableTable = {
  id: string;
  name: string;
  fields: AirtableField[];
};

type AirtableBaseResponse = {
  tables: AirtableTable[];
};

type AirtableConfig = {
  pat: string;
  baseId: string;
  tableName: string;
};

type SchemaResponse = {
  success: boolean;
  tableName: string;
  tableId: string;
  fields: AirtableField[];
  allTables: string[];
  error?: string;
};

async function getConfigFromBoard(
  boardId: string
): Promise<AirtableConfig | null> {
  try {
    const pat = await getAirtablePat(boardId);

    // Use admin client to bypass RLS when reading job board configs
    const { data: board } = await supabaseAdmin
      .from('job_board_configs')
      .select('airtable_base_id, airtable_table_name')
      .eq('id', boardId)
      .single();

    if (!(board && pat)) {
      return null;
    }

    return {
      pat,
      baseId: board.airtable_base_id,
      tableName: board.airtable_table_name,
    };
  } catch (error) {
    logger.error(`Error getting config for board ${boardId}:`, error);
    return null;
  }
}

async function fetchAirtableSchema(
  config: AirtableConfig
): Promise<SchemaResponse> {
  const { pat, baseId, tableName } = config;

  try {
    const headers = {
      Authorization: `Bearer ${pat}`,
      'Content-Type': 'application/json',
    };

    // Fetch base schema to get all tables and their fields
    const response = await fetch(
      `https://api.airtable.com/v0/meta/bases/${baseId}/tables`,
      { headers }
    );

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    const data: AirtableBaseResponse = await response.json();
    const tables = data.tables || [];

    // Find the specific table
    const targetTable = tables.find((table) => table.name === tableName);

    if (!targetTable) {
      return {
        success: false,
        tableName,
        tableId: '',
        fields: [],
        allTables: tables.map((t) => t.name),
        error: `Table "${tableName}" not found in base`,
      };
    }

    return {
      success: true,
      tableName: targetTable.name,
      tableId: targetTable.id,
      fields: targetTable.fields,
      allTables: tables.map((t) => t.name),
    };
  } catch (error) {
    logger.error('Error fetching Airtable schema:', error);
    return {
      success: false,
      tableName,
      tableId: '',
      fields: [],
      allTables: [],
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * GET /api/board-schema/[boardId]
 * Fetches the Airtable schema for a specific job board
 */
export async function GET(
  _request: Request,
  { params }: { params: Promise<{ boardId: string }> }
) {
  try {
    const { boardId } = await params;

    if (!boardId) {
      return NextResponse.json(
        { error: 'Board ID is required' },
        { status: 400 }
      );
    }

    // Get board configuration
    const config = await getConfigFromBoard(boardId);

    if (!config) {
      return NextResponse.json(
        {
          error: 'Board not found or incomplete Airtable configuration',
          details:
            'Make sure the board exists and has valid Airtable credentials configured',
        },
        { status: 404 }
      );
    }

    // Fetch schema from Airtable
    const schemaData = await fetchAirtableSchema(config);

    if (!schemaData.success) {
      return NextResponse.json(
        {
          error: 'Failed to fetch Airtable schema',
          details: schemaData.error,
          tableName: schemaData.tableName,
          allTables: schemaData.allTables,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(schemaData);
  } catch (error) {
    logger.error('Board schema API error:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
