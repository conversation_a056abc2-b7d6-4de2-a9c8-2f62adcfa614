import assert from 'node:assert/strict';
import test from 'node:test';

// Template variable replacement utility - isolated implementation
function replaceTemplateVariables(
  template: string,
  variables: Record<string, string>
): string {
  let result = template;
  for (const [key, value] of Object.entries(variables)) {
    result = result.replace(new RegExp(`\\{${key}\\}`, 'g'), value);
  }
  return result;
}

// URL validation utilities
function isValidHttpUrl(url: string | null | undefined): boolean {
  if (!url) {
    return false;
  }
  return url.startsWith('http://') || url.startsWith('https://');
}

function isValidEmail(email: string | null | undefined): boolean {
  if (!email) {
    return false;
  }
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

function normalizeUrl(url: string): string {
  try {
    const urlObj = new URL(url);
    // Remove trailing slash and normalize
    return urlObj.href.replace(/\/$/, '');
  } catch {
    return url;
  }
}

// Error handling utilities
type ApiError = {
  message: string;
  status: number;
  code?: string;
  details?: Record<string, unknown>;
};

function createApiError(
  message: string,
  status: number,
  code?: string,
  details?: Record<string, unknown>
): ApiError {
  return { message, status, code, details };
}

function formatApiError(error: ApiError): Record<string, unknown> {
  const formatted: Record<string, unknown> = {
    error: error.message,
    status: error.status,
  };

  if (error.code) {
    formatted.code = error.code;
  }

  if (error.details) {
    formatted.details = error.details;
  }

  formatted.timestamp = new Date().toISOString();

  return formatted;
}

// Request validation utilities
function validateRequiredFields(
  data: Record<string, unknown>,
  requiredFields: string[]
): { isValid: boolean; missingFields: string[] } {
  const missingFields = requiredFields.filter(
    (field) =>
      !(field in data) || data[field] === null || data[field] === undefined
  );

  return {
    isValid: missingFields.length === 0,
    missingFields,
  };
}

function sanitizeInput(input: string): string {
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .slice(0, 1000); // Limit length
}

// Response formatting utilities
function createSuccessResponse(
  data: unknown,
  message?: string
): Record<string, unknown> {
  return {
    success: true,
    data,
    message: message || 'Operation completed successfully',
    timestamp: new Date().toISOString(),
  };
}

function createHealthCheckResponse(config: {
  service: string;
  status?: string;
  features?: string[];
  environment?: Record<string, unknown>;
}): Record<string, unknown> {
  return {
    service: config.service,
    status: config.status || 'healthy',
    features: config.features || [],
    environment: config.environment || {},
    timestamp: new Date().toISOString(),
  };
}

// Metadata generation utilities
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

function generateMetadata(operation: string): Record<string, unknown> {
  return {
    operation,
    requestId: generateRequestId(),
    timestamp: new Date().toISOString(),
    version: '1.0.0',
  };
}

// Query parameter utilities
function parseQueryParams(
  searchParams: URLSearchParams
): Record<string, string | string[]> {
  const params: Record<string, string | string[]> = {};

  for (const [key, value] of searchParams.entries()) {
    if (key in params) {
      // Convert to array if multiple values
      const existing = params[key];
      if (Array.isArray(existing)) {
        existing.push(value);
      } else {
        params[key] = [existing, value];
      }
    } else {
      params[key] = value;
    }
  }

  return params;
}

function buildQueryString(params: Record<string, unknown>): string {
  const searchParams = new URLSearchParams();

  for (const [key, value] of Object.entries(params)) {
    if (value !== null && value !== undefined) {
      if (Array.isArray(value)) {
        for (const item of value) {
          searchParams.append(key, String(item));
        }
      } else {
        searchParams.set(key, String(value));
      }
    }
  }

  return searchParams.toString();
}

// Test data
const testTemplate =
  'Hello {name}, your job is {title} at {company}. Contact: {email}';
const testVariables = {
  name: 'John',
  title: 'Software Engineer',
  company: 'TechCorp',
  email: '<EMAIL>',
};

const testUrls = {
  valid: [
    'https://example.com',
    'http://localhost:3000',
    'https://subdomain.example.com/path?query=1',
  ],
  invalid: [
    'ftp://example.com',
    'mailto:<EMAIL>',
    "javascript:alert('xss')",
    'not-a-url',
    '',
    null,
    undefined,
  ],
};

const testEmails = {
  valid: ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
  invalid: [
    'invalid-email',
    '@example.com',
    'test@',
    'test.example.com',
    '',
    null,
    undefined,
  ],
};

// Template Variable Replacement Tests
test('replaceTemplateVariables replaces all variables correctly', () => {
  const result = replaceTemplateVariables(testTemplate, testVariables);
  const expected =
    'Hello John, your job is Software Engineer at TechCorp. Contact: <EMAIL>';

  assert.equal(result, expected);
  assert.ok(
    !result.includes('{'),
    'Should not contain any unreplaced placeholders'
  );
});

test('replaceTemplateVariables handles missing variables', () => {
  const partialVariables = { name: 'John', title: 'Engineer' };
  const result = replaceTemplateVariables(testTemplate, partialVariables);

  assert.ok(result.includes('John'), 'Should replace available variables');
  assert.ok(result.includes('Engineer'), 'Should replace available variables');
  assert.ok(
    result.includes('{company}'),
    'Should leave missing variables as placeholders'
  );
  assert.ok(
    result.includes('{email}'),
    'Should leave missing variables as placeholders'
  );
});

test('replaceTemplateVariables handles empty template', () => {
  const result = replaceTemplateVariables('', testVariables);
  assert.equal(result, '');
});

test('replaceTemplateVariables handles empty variables', () => {
  const result = replaceTemplateVariables(testTemplate, {});
  assert.equal(
    result,
    testTemplate,
    'Should return original template unchanged'
  );
});

test('replaceTemplateVariables handles special characters', () => {
  const template = 'Message: {message}';
  const variables = {
    message: "Hello & welcome! <script>alert('test')</script>",
  };
  const result = replaceTemplateVariables(template, variables);

  assert.ok(
    result.includes('Hello & welcome!'),
    'Should handle special characters'
  );
  assert.ok(
    result.includes('<script>'),
    'Should not sanitize in template replacement'
  );
});

// URL Validation Tests
test('isValidHttpUrl validates HTTP URLs correctly', () => {
  for (const url of testUrls.valid) {
    assert.ok(isValidHttpUrl(url), `Should validate ${url} as valid`);
  }

  for (const url of testUrls.invalid) {
    assert.equal(
      isValidHttpUrl(url),
      false,
      `Should validate ${url} as invalid`
    );
  }
});

test('normalizeUrl standardizes URLs', () => {
  assert.equal(normalizeUrl('https://example.com/'), 'https://example.com');
  assert.equal(
    normalizeUrl('https://example.com/path/'),
    'https://example.com/path'
  );
  assert.equal(normalizeUrl('https://example.com'), 'https://example.com');
});

test('normalizeUrl handles invalid URLs gracefully', () => {
  assert.equal(normalizeUrl('not-a-url'), 'not-a-url');
  assert.equal(normalizeUrl(''), '');
});

// Email Validation Tests
test('isValidEmail validates email addresses correctly', () => {
  for (const email of testEmails.valid) {
    assert.ok(isValidEmail(email), `Should validate ${email} as valid`);
  }

  for (const email of testEmails.invalid) {
    assert.equal(
      isValidEmail(email),
      false,
      `Should validate ${email} as invalid`
    );
  }
});

// Error Handling Tests
test('createApiError creates properly structured error objects', () => {
  const error = createApiError('Test error', 400, 'VALIDATION_ERROR', {
    field: 'title',
  });

  assert.equal(error.message, 'Test error');
  assert.equal(error.status, 400);
  assert.equal(error.code, 'VALIDATION_ERROR');
  assert.deepEqual(error.details, { field: 'title' });
});

test('formatApiError formats errors for API responses', () => {
  const error = createApiError('Test error', 500, 'INTERNAL_ERROR');
  const formatted = formatApiError(error);

  assert.equal(formatted.error, 'Test error');
  assert.equal(formatted.status, 500);
  assert.equal(formatted.code, 'INTERNAL_ERROR');
  assert.ok(formatted.timestamp, 'Should include timestamp');
  assert.ok(
    typeof formatted.timestamp === 'string',
    'Timestamp should be string'
  );
});

test('formatApiError handles minimal error objects', () => {
  const error = createApiError('Simple error', 404);
  const formatted = formatApiError(error);

  assert.equal(formatted.error, 'Simple error');
  assert.equal(formatted.status, 404);
  assert.equal(formatted.code, undefined);
  assert.equal(formatted.details, undefined);
});

// Request Validation Tests
test('validateRequiredFields identifies missing fields', () => {
  const data = { title: 'Test', company: 'TechCorp' };
  const required = ['title', 'company', 'description'];

  const result = validateRequiredFields(data, required);

  assert.equal(result.isValid, false);
  assert.deepEqual(result.missingFields, ['description']);
});

test('validateRequiredFields passes with all fields present', () => {
  const data = { title: 'Test', company: 'TechCorp', description: 'A job' };
  const required = ['title', 'company', 'description'];

  const result = validateRequiredFields(data, required);

  assert.equal(result.isValid, true);
  assert.deepEqual(result.missingFields, []);
});

test('validateRequiredFields handles null and undefined values', () => {
  const data = { title: 'Test', company: null, description: undefined };
  const required = ['title', 'company', 'description'];

  const result = validateRequiredFields(data, required);

  assert.equal(result.isValid, false);
  assert.deepEqual(result.missingFields, ['company', 'description']);
});

test('sanitizeInput cleans user input', () => {
  assert.equal(sanitizeInput('  Hello World  '), 'Hello World');
  assert.equal(
    sanitizeInput("<script>alert('xss')</script>"),
    "scriptalert('xss')/script"
  );
  assert.equal(sanitizeInput('Normal text'), 'Normal text');
});

test('sanitizeInput limits input length', () => {
  const longInput = 'a'.repeat(2000);
  const result = sanitizeInput(longInput);

  assert.equal(result.length, 1000);
  assert.ok(result.startsWith('aaa'), 'Should preserve beginning of input');
});

// Response Formatting Tests
test('createSuccessResponse formats success responses correctly', () => {
  const data = { id: 1, title: 'Test Job' };
  const response = createSuccessResponse(data, 'Job created successfully');

  assert.equal(response.success, true);
  assert.deepEqual(response.data, data);
  assert.equal(response.message, 'Job created successfully');
  assert.ok(response.timestamp, 'Should include timestamp');
});

test('createSuccessResponse uses default message when none provided', () => {
  const data = { test: 'data' };
  const response = createSuccessResponse(data);

  assert.equal(response.message, 'Operation completed successfully');
});

test('createHealthCheckResponse formats health check responses', () => {
  const config = {
    service: 'job-processor',
    status: 'healthy',
    features: ['ai-extraction', 'monitoring'],
    environment: { version: '1.0.0', node_env: 'production' },
  };

  const response = createHealthCheckResponse(config);

  assert.equal(response.service, 'job-processor');
  assert.equal(response.status, 'healthy');
  assert.deepEqual(response.features, ['ai-extraction', 'monitoring']);
  assert.deepEqual(response.environment, {
    version: '1.0.0',
    node_env: 'production',
  });
  assert.ok(response.timestamp, 'Should include timestamp');
});

test('createHealthCheckResponse uses defaults for optional fields', () => {
  const config = { service: 'test-service' };
  const response = createHealthCheckResponse(config);

  assert.equal(response.service, 'test-service');
  assert.equal(response.status, 'healthy');
  assert.deepEqual(response.features, []);
  assert.deepEqual(response.environment, {});
});

// Metadata Generation Tests
test('generateRequestId creates unique identifiers', () => {
  const id1 = generateRequestId();
  const id2 = generateRequestId();

  assert.ok(id1.startsWith('req_'), 'Should have req_ prefix');
  assert.ok(id2.startsWith('req_'), 'Should have req_ prefix');
  assert.notEqual(id1, id2, 'Should generate unique IDs');
  assert.ok(id1.length > 10, 'Should be reasonably long');
});

test('generateMetadata creates complete metadata objects', () => {
  const metadata = generateMetadata('job-extraction');

  assert.equal(metadata.operation, 'job-extraction');
  assert.ok(metadata.requestId, 'Should include request ID');
  assert.ok(metadata.timestamp, 'Should include timestamp');
  assert.equal(metadata.version, '1.0.0');
  assert.ok(
    typeof metadata.requestId === 'string',
    'Request ID should be string'
  );
  assert.ok(
    typeof metadata.timestamp === 'string',
    'Timestamp should be string'
  );
});

// Query Parameter Tests
test('parseQueryParams handles single values', () => {
  const searchParams = new URLSearchParams('name=John&age=30');
  const result = parseQueryParams(searchParams);

  assert.deepEqual(result, { name: 'John', age: '30' });
});

test('parseQueryParams handles multiple values for same key', () => {
  const searchParams = new URLSearchParams(
    'tags=javascript&tags=react&tags=nodejs'
  );
  const result = parseQueryParams(searchParams);

  assert.deepEqual(result, { tags: ['javascript', 'react', 'nodejs'] });
});

test('parseQueryParams handles mixed single and multiple values', () => {
  const searchParams = new URLSearchParams(
    'name=John&tags=js&tags=react&age=30'
  );
  const result = parseQueryParams(searchParams);

  assert.equal(result.name, 'John');
  assert.equal(result.age, '30');
  assert.deepEqual(result.tags, ['js', 'react']);
});

test('parseQueryParams handles empty search params', () => {
  const searchParams = new URLSearchParams('');
  const result = parseQueryParams(searchParams);

  assert.deepEqual(result, {});
});

test('buildQueryString creates query strings from objects', () => {
  const params = { name: 'John', age: 30, active: true };
  const result = buildQueryString(params);

  assert.ok(result.includes('name=John'), 'Should include name parameter');
  assert.ok(result.includes('age=30'), 'Should include age parameter');
  assert.ok(result.includes('active=true'), 'Should include active parameter');
});

test('buildQueryString handles arrays', () => {
  const params = { tags: ['javascript', 'react'], name: 'John' };
  const result = buildQueryString(params);

  assert.ok(result.includes('tags=javascript'), 'Should include first tag');
  assert.ok(result.includes('tags=react'), 'Should include second tag');
  assert.ok(result.includes('name=John'), 'Should include name');
});

test('buildQueryString filters out null and undefined values', () => {
  const params = {
    name: 'John',
    age: null,
    active: undefined,
    company: 'TechCorp',
  };
  const result = buildQueryString(params);

  assert.ok(result.includes('name=John'), 'Should include non-null values');
  assert.ok(
    result.includes('company=TechCorp'),
    'Should include non-null values'
  );
  assert.ok(!result.includes('age='), 'Should exclude null values');
  assert.ok(!result.includes('active='), 'Should exclude undefined values');
});

test('buildQueryString handles empty objects', () => {
  const result = buildQueryString({});
  assert.equal(result, '');
});

test('buildQueryString handles special characters', () => {
  const params = { message: 'Hello & welcome!', email: '<EMAIL>' };
  const result = buildQueryString(params);

  // URLSearchParams automatically encodes special characters
  assert.ok(
    result.includes('message=Hello'),
    'Should handle special characters'
  );
  assert.ok(result.includes('email=test'), 'Should handle @ symbol');
});

// Pagination Utilities
function createPaginationInfo(
  page: number,
  limit: number,
  total: number
): {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
  offset: number;
} {
  const totalPages = Math.ceil(total / limit);
  const hasNext = page < totalPages;
  const hasPrev = page > 1;
  const offset = (page - 1) * limit;

  return {
    page,
    limit,
    total,
    totalPages,
    hasNext,
    hasPrev,
    offset,
  };
}

function validatePaginationParams(
  page?: number,
  limit?: number
): { page: number; limit: number; errors: string[] } {
  const errors: string[] = [];
  let validPage = page || 1;
  let validLimit = limit || 10;

  if (validPage < 1) {
    errors.push('Page must be greater than 0');
    validPage = 1;
  }

  if (validLimit < 1) {
    errors.push('Limit must be greater than 0');
    validLimit = 10;
  }

  if (validLimit > 100) {
    errors.push('Limit cannot exceed 100');
    validLimit = 100;
  }

  return { page: validPage, limit: validLimit, errors };
}

// Rate Limiting Simulation
type RateLimitInfo = {
  allowed: boolean;
  remaining: number;
  resetTime: number;
  retryAfter?: number;
};

function simulateRateLimit(
  requestCount: number,
  windowSize: number,
  maxRequests: number
): RateLimitInfo {
  const remaining = Math.max(0, maxRequests - requestCount);
  const allowed = requestCount < maxRequests;
  const resetTime = Date.now() + windowSize * 1000;
  const retryAfter = allowed ? undefined : windowSize;

  return {
    allowed,
    remaining,
    resetTime,
    retryAfter,
  };
}

// Webhook Validation
function validateWebhookSignature(
  payload: string,
  signature: string,
  secret: string
): boolean {
  // Simplified signature validation (in real implementation, use crypto)
  const expectedSignature = `sha256=${Buffer.from(payload + secret).toString('base64')}`;
  return signature === expectedSignature;
}

function parseWebhookPayload<T>(
  body: string,
  expectedFields: string[]
): { isValid: boolean; data?: T; errors: string[] } {
  const errors: string[] = [];

  try {
    const data = JSON.parse(body) as T;

    // Check for required fields
    for (const field of expectedFields) {
      if (!(field in (data as Record<string, unknown>))) {
        errors.push(`Missing required field: ${field}`);
      }
    }

    return {
      isValid: errors.length === 0,
      data: errors.length === 0 ? data : undefined,
      errors,
    };
  } catch {
    errors.push('Invalid JSON payload');
    return { isValid: false, errors };
  }
}

// Pagination Tests
test('createPaginationInfo calculates pagination correctly', () => {
  const pagination = createPaginationInfo(2, 10, 25);

  assert.equal(pagination.page, 2);
  assert.equal(pagination.limit, 10);
  assert.equal(pagination.total, 25);
  assert.equal(pagination.totalPages, 3);
  assert.equal(pagination.hasNext, true);
  assert.equal(pagination.hasPrev, true);
  assert.equal(pagination.offset, 10);
});

test('createPaginationInfo handles edge cases', () => {
  // First page
  const firstPage = createPaginationInfo(1, 10, 25);
  assert.equal(firstPage.hasPrev, false);
  assert.equal(firstPage.hasNext, true);
  assert.equal(firstPage.offset, 0);

  // Last page
  const lastPage = createPaginationInfo(3, 10, 25);
  assert.equal(lastPage.hasPrev, true);
  assert.equal(lastPage.hasNext, false);

  // Exact division
  const exactPage = createPaginationInfo(1, 10, 20);
  assert.equal(exactPage.totalPages, 2);
});

test('validatePaginationParams validates and corrects parameters', () => {
  const result = validatePaginationParams(2, 20);
  assert.equal(result.page, 2);
  assert.equal(result.limit, 20);
  assert.equal(result.errors.length, 0);
});

test('validatePaginationParams handles invalid parameters', () => {
  const result = validatePaginationParams(-1, 150);
  assert.equal(result.page, 1);
  assert.equal(result.limit, 100);
  assert.equal(result.errors.length, 2);
  assert.ok(
    result.errors.some((e) => e.includes('Page must be greater than 0'))
  );
  assert.ok(result.errors.some((e) => e.includes('Limit cannot exceed 100')));
});

test('validatePaginationParams uses defaults for undefined values', () => {
  const result = validatePaginationParams();
  assert.equal(result.page, 1);
  assert.equal(result.limit, 10);
  assert.equal(result.errors.length, 0);
});

// Rate Limiting Tests
test('simulateRateLimit allows requests within limit', () => {
  const rateLimit = simulateRateLimit(5, 60, 10);

  assert.equal(rateLimit.allowed, true);
  assert.equal(rateLimit.remaining, 5);
  assert.equal(rateLimit.retryAfter, undefined);
  assert.ok(
    rateLimit.resetTime > Date.now(),
    'Reset time should be in the future'
  );
});

test('simulateRateLimit blocks requests over limit', () => {
  const rateLimit = simulateRateLimit(15, 60, 10);

  assert.equal(rateLimit.allowed, false);
  assert.equal(rateLimit.remaining, 0);
  assert.equal(rateLimit.retryAfter, 60);
  assert.ok(
    rateLimit.resetTime > Date.now(),
    'Reset time should be in the future'
  );
});

test('simulateRateLimit handles edge case at exact limit', () => {
  const rateLimit = simulateRateLimit(10, 60, 10);

  assert.equal(rateLimit.allowed, false);
  assert.equal(rateLimit.remaining, 0);
  assert.equal(rateLimit.retryAfter, 60);
});

test('simulateRateLimit calculates remaining correctly', () => {
  const rateLimit = simulateRateLimit(3, 60, 10);

  assert.equal(rateLimit.remaining, 7);
  assert.equal(rateLimit.allowed, true);
});

// Webhook Validation Tests
test('validateWebhookSignature validates correct signatures', () => {
  const payload = '{"event":"job.created","data":{"id":123}}';
  const secret = 'webhook-secret-key';
  const signature = `sha256=${Buffer.from(payload + secret).toString('base64')}`;

  const isValid = validateWebhookSignature(payload, signature, secret);
  assert.equal(isValid, true);
});

test('validateWebhookSignature rejects incorrect signatures', () => {
  const payload = '{"event":"job.created","data":{"id":123}}';
  const secret = 'webhook-secret-key';
  const wrongSignature = 'sha256=invalid-signature';

  const isValid = validateWebhookSignature(payload, wrongSignature, secret);
  assert.equal(isValid, false);
});

test('parseWebhookPayload parses valid JSON with required fields', () => {
  const payload =
    '{"event":"job.created","data":{"id":123},"timestamp":"2024-01-15T10:30:00Z"}';
  const requiredFields = ['event', 'data', 'timestamp'];

  const result = parseWebhookPayload(payload, requiredFields);

  assert.equal(result.isValid, true);
  assert.ok(result.data, 'Should include parsed data');
  assert.equal(result.errors.length, 0);
  assert.equal((result.data as any).event, 'job.created');
});

test('parseWebhookPayload identifies missing required fields', () => {
  const payload = '{"event":"job.created","data":{"id":123}}';
  const requiredFields = ['event', 'data', 'timestamp'];

  const result = parseWebhookPayload(payload, requiredFields);

  assert.equal(result.isValid, false);
  assert.equal(result.data, undefined);
  assert.equal(result.errors.length, 1);
  assert.ok(result.errors[0].includes('Missing required field: timestamp'));
});

test('parseWebhookPayload handles invalid JSON', () => {
  const invalidPayload = '{"event":"job.created","data":{"id":123}'; // Missing closing brace
  const requiredFields = ['event', 'data'];

  const result = parseWebhookPayload(invalidPayload, requiredFields);

  assert.equal(result.isValid, false);
  assert.equal(result.data, undefined);
  assert.equal(result.errors.length, 1);
  assert.equal(result.errors[0], 'Invalid JSON payload');
});

test('parseWebhookPayload handles empty required fields array', () => {
  const payload = '{"event":"job.created"}';
  const requiredFields: string[] = [];

  const result = parseWebhookPayload(payload, requiredFields);

  assert.equal(result.isValid, true);
  assert.ok(result.data, 'Should include parsed data');
  assert.equal(result.errors.length, 0);
});

// Integration Tests - combining multiple utilities
test('complete API request flow simulation', () => {
  // 1. Validate pagination
  const pagination = validatePaginationParams(2, 20);
  assert.equal(pagination.errors.length, 0);

  // 2. Check rate limiting
  const rateLimit = simulateRateLimit(5, 60, 100);
  assert.equal(rateLimit.allowed, true);

  // 3. Generate metadata
  const metadata = generateMetadata('list-jobs');
  assert.ok(metadata.requestId);

  // 4. Create success response
  const mockData = { jobs: [], pagination: createPaginationInfo(2, 20, 100) };
  const response = createSuccessResponse(
    mockData,
    'Jobs retrieved successfully'
  );

  assert.equal(response.success, true);
  assert.ok(response.data);
  assert.equal(response.message, 'Jobs retrieved successfully');
});

test('error handling flow simulation', () => {
  // 1. Create API error
  const error = createApiError('Validation failed', 400, 'VALIDATION_ERROR', {
    field: 'title',
    message: 'Title is required',
  });

  // 2. Format for response
  const formatted = formatApiError(error);

  assert.equal(formatted.error, 'Validation failed');
  assert.equal(formatted.status, 400);
  assert.equal(formatted.code, 'VALIDATION_ERROR');
  assert.ok(formatted.details);
  assert.ok(formatted.timestamp);
});
