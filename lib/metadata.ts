import { AI_CONFIG } from './config';
import { calculateCostBreakdown, type TokenUsage } from './cost-calculator';

/**
 * Generate standard API metadata with cost tracking
 */
export function generateMetadata(
  startTime: number,
  usage: TokenUsage | undefined,
  additionalData?: Record<string, unknown>
) {
  const duration = Date.now() - startTime;
  const model = additionalData?.model as string | undefined;
  const cost = calculateCostBreakdown(
    usage?.inputTokens || 0,
    usage?.outputTokens || 0,
    model || AI_CONFIG.MODEL
  );

  return {
    duration,
    timestamp: new Date().toISOString(),
    usage: usage || {
      inputTokens: 0,
      outputTokens: 0,
      totalTokens: 0,
    },
    cost,
    ...additionalData,
  };
}
