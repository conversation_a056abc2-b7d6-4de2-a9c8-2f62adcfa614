/*
 * Monitoring utilities for job status checks.
 * Task 2: HTTP helper – fetchHead(url) with timeout & retries.
 */

import { openai } from '@ai-sdk/openai';
import { generateObject } from 'ai';
import { replaceTemplateVariables } from './api-utils';
import { AI_CONFIG } from './config';
import {
  JobStatusClassifierSchema,
  type MonitorStatus,
} from './monitor-schema';
import { JOB_STATUS_CLASSIFIER_PROMPT } from './prompts';
import { generateMetadata, logger } from './utils';

/**
 * Validate if a URL is a valid HTTP/HTTPS URL for monitoring
 */
function isValidHttpUrl(url: string | null | undefined): boolean {
  if (!url) {
    return false;
  }
  return url.startsWith('http://') || url.startsWith('https://');
}

/**
 * Select the optimal URL for monitoring based on job data
 * Priority: apply_url > source_url, with validation
 */
export function getMonitoringUrl(job: {
  apply_url?: string | null;
  source_url?: string | null;
  apply_email?: string | null;
}): string | null {
  // Skip email-only applications that have no valid URLs
  if (job.apply_email && !job.apply_url && !isValidHttpUrl(job.source_url)) {
    return null; // Skip monitoring for email-only jobs
  }

  // Check apply_url first, but skip if it's a mailto link
  if (job.apply_url && isValidHttpUrl(job.apply_url)) {
    return job.apply_url;
  }

  // Fallback to source_url if apply_url is invalid/mailto/null
  if (job.source_url && isValidHttpUrl(job.source_url)) {
    return job.source_url;
  }

  // No valid URLs found
  return null;
}

export interface FetchHeadOptions {
  /** Request timeout in milliseconds (default 10 000 ms) */
  timeout?: number;
  /** Maximum number of attempts (default 3) */
  maxRetries?: number;
  /** Custom User-Agent header */
  userAgent?: string;
}

export interface HeadResult {
  ok: boolean;
  status: number;
  redirected: boolean;
  finalUrl: string;
  headers: Record<string, string>;
}

/**
 * Perform an HTTP HEAD request with built-in timeout and exponential-back-off retries.
 * Implemented recursively to avoid `await` inside loops (Ultracite rule).
 */
export function fetchHead(
  url: string,
  {
    timeout = 10_000,
    maxRetries = 3,
    userAgent = 'Bordfeed-Monitor/0.1',
  }: FetchHeadOptions = {}
): Promise<HeadResult> {
  async function attempt(tryCount: number): Promise<HeadResult> {
    const controller = new AbortController();
    const timer = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(url, {
        method: 'HEAD',
        redirect: 'follow',
        signal: controller.signal,
        headers: {
          'User-Agent': userAgent,
        },
      });

      clearTimeout(timer);

      return {
        ok: response.ok,
        status: response.status,
        redirected: response.redirected,
        finalUrl: response.url,
        headers: Object.fromEntries(response.headers.entries()),
      } satisfies HeadResult;
    } catch (error) {
      clearTimeout(timer);

      // Retry with exponential back-off until maxRetries is reached
      if (tryCount < maxRetries) {
        const delay = 1000 * 2 ** (tryCount - 1); // Start at 1s, then 2s, 4s
        await new Promise((resolve) => setTimeout(resolve, delay));
        return attempt(tryCount + 1);
      }

      logger.error('fetchHead failed', { url, error });
      throw error;
    }
  }

  return attempt(1);
}

export interface DownloadSnippetOptions {
  /** Request timeout (ms) */
  timeout?: number;
  /** Max bytes to read (default 100 kB) */
  maxBytes?: number;
  /** Custom User-Agent header */
  userAgent?: string;
}

export interface SnippetResult {
  ok: boolean;
  status: number;
  finalUrl: string;
  content: string;
  truncated: boolean;
}

/**
 * Download at most `maxBytes` from the given URL (GET request). Returns the snippet string.
 * Uses Range header for efficiency when supported, otherwise truncates response text.
 */
export async function downloadSnippet(
  url: string,
  {
    timeout = 15_000,
    maxBytes = 100 * 1024,
    userAgent = 'Bordfeed-Monitor/0.1',
  }: DownloadSnippetOptions = {}
): Promise<SnippetResult> {
  const controller = new AbortController();
  const timer = setTimeout(() => controller.abort(), timeout);

  try {
    const response = await fetch(url, {
      method: 'GET',
      redirect: 'follow',
      signal: controller.signal,
      headers: {
        'User-Agent': userAgent,
        Range: `bytes=0-${maxBytes - 1}`,
      },
    });

    clearTimeout(timer);

    const buffer = await response.arrayBuffer();
    const truncated = buffer.byteLength >= maxBytes;
    const slice = truncated ? buffer.slice(0, maxBytes) : buffer;
    const decoder = new TextDecoder('utf-8', { fatal: false });
    const content = decoder.decode(slice);

    return {
      ok: response.ok,
      status: response.status,
      finalUrl: response.url,
      content,
      truncated,
    } satisfies SnippetResult;
  } catch (error) {
    clearTimeout(timer);
    logger.error('downloadSnippet failed', { url, error });
    throw error;
  }
}

// -----------------------------------------------------
// Task 4: Phrase matcher – detect "job closed" phrases
// -----------------------------------------------------

export interface PhraseMatchResult {
  matched: boolean;
  phrase: string | null;
}

/**
 * Check HTML/text snippet for any of the supplied phrases.
 * Matching is case-insensitive and ignores extraneous whitespace.
 */
export function matchClosedPhrase(
  snippet: string,
  phrases: readonly string[]
): PhraseMatchResult {
  if (!(snippet && phrases.length)) {
    return { matched: false, phrase: null };
  }

  // Helper to escape special regex characters from a string
  const escapeRegex = (str: string) =>
    str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

  for (const phrase of phrases) {
    const pattern = phrase.trim();
    if (!pattern) {
      continue;
    }

    try {
      // Treat phrase as full regex pattern (case-insensitive, multiline)
      const regex = new RegExp(escapeRegex(pattern), 'i');
      if (regex.test(snippet)) {
        return { matched: true, phrase };
      }
    } catch {
      // Fallback to simple includes if regex is invalid
      if (snippet.toLowerCase().includes(pattern.toLowerCase())) {
        return { matched: true, phrase };
      }
    }
  }

  return { matched: false, phrase: null };
}

// -----------------------------------------------------
// Task 7: AI wrapper util – classifyStatus(htmlSnippet)
// -----------------------------------------------------

export interface ClassificationResult {
  status: MonitorStatus;
  confidence: number;
  metadata: ReturnType<typeof generateMetadata>;
}

/**
 * Classify job status using OpenAI structured output.
 */
export async function classifyStatus(
  snippet: string
): Promise<ClassificationResult> {
  const startTime = Date.now();

  const prompt = replaceTemplateVariables(JOB_STATUS_CLASSIFIER_PROMPT, {
    snippet,
  });

  const result = await generateObject({
    model: openai(AI_CONFIG.MODEL),
    prompt,
    schema: JobStatusClassifierSchema,
  });

  const metadata = generateMetadata(startTime, result.usage, {
    model: AI_CONFIG.MODEL,
  });

  // Type assertion to handle Zod v4 compatibility with AI SDK
  const typedResult = result.object as {
    status: MonitorStatus;
    confidence: number;
  };

  return {
    status: typedResult.status,
    confidence: typedResult.confidence,
    metadata,
  };
}
