import { z } from 'zod';
import { APPLY_METHODS } from './apply-methods';
import { CAREER_LEVELS } from './career-levels';
import { SCHEMA_LIMITS } from './constants';
import { countries } from './data/countries';
import { CURRENCY_CODES } from './data/currencies';
import { LANGUAGE_CODES } from './data/languages';
import { JOB_STATUSES, VISA_SPONSORSHIP_OPTIONS } from './job-status';
import { JOB_TYPES } from './job-types';
import { SALARY_UNITS } from './salary-units';
import { REMOTE_REGIONS, WORKPLACE_TYPES } from './workplace';

// Enhanced validation messages for development
const VALIDATION_MESSAGES = {
  TITLE_EMPTY:
    '🚫 Job title is required and cannot be empty. AI must extract a meaningful job title.',
  DESCRIPTION_EMPTY:
    '🚫 Job description is required and cannot be empty. AI must extract job description content.',
  SALARY_INVALID:
    '💰 Salary must be a positive integer (no decimals, no negative values)',
  URL_INVALID: '🔗 Apply URL must be a valid HTTP/HTTPS URL format',
  DATE_INVALID:
    '📅 Date must be in ISO format (YYYY-MM-DD or YYYY-MM-DDTHH:mm:ss)',
  TEXT_TOO_LONG: (field: string, limit: number) =>
    `📝 ${field} exceeds maximum length of ${limit} characters. Consider summarizing.`,
  ENUM_INVALID: (field: string, validValues: readonly string[]) =>
    `🎯 ${field} must be one of: ${validValues.join(', ')}. Check spelling and case sensitivity.`,
  ARRAY_INVALID: (field: string) =>
    `📋 ${field} must be an array of valid values. Ensure proper formatting.`,
} as const;

// Accept ISO date or datetime strings
const _IsoDateOrDateTimeSchema = z.string();

// Enhanced schema for AI extraction with verbose validation messages
export const JobExtractionSchema = z.object({
  // Core fields (required) - Enhanced with detailed error messages
  title: z
    .string()
    .min(1, VALIDATION_MESSAGES.TITLE_EMPTY)
    .max(
      200,
      '🚫 Job title too long (max 200 chars). AI should extract concise title.'
    ),

  description: z
    .string()
    .min(1, VALIDATION_MESSAGES.DESCRIPTION_EMPTY)
    .max(
      10_000,
      '🚫 Description too long (max 10k chars). AI should summarize key points.'
    ),

  status: z
    .enum(JOB_STATUSES, {
      message: VALIDATION_MESSAGES.ENUM_INVALID('Job status', JOB_STATUSES),
    })
    .default('active'),

  // Core fields (nullable and optional for AI extraction flexibility)
  company: z
    .string()
    .max(100, '🏢 Company name too long (max 100 chars)')
    .nullable()
    .optional(),

  type: z
    .enum(JOB_TYPES, {
      message: VALIDATION_MESSAGES.ENUM_INVALID('Job type', JOB_TYPES),
    })
    .nullable()
    .optional(),

  apply_url: z
    .string()
    .url(VALIDATION_MESSAGES.URL_INVALID)
    .nullable()
    .optional(),

  apply_method: z
    .enum(APPLY_METHODS, {
      message: VALIDATION_MESSAGES.ENUM_INVALID('Apply method', APPLY_METHODS),
    })
    .nullable()
    .optional(),

  posted_date: z
    .string()
    .regex(
      /^\d{4}-\d{2}-\d{2}(T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?)?$/,
      VALIDATION_MESSAGES.DATE_INVALID
    )
    .nullable()
    .optional(),

  // Salary information with enhanced validation
  salary_min: z
    .number()
    .int(VALIDATION_MESSAGES.SALARY_INVALID)
    .positive(VALIDATION_MESSAGES.SALARY_INVALID)
    .max(10_000_000, '💰 Salary minimum seems unrealistic (max 10M)')
    .nullable()
    .optional(),

  salary_max: z
    .number()
    .int(VALIDATION_MESSAGES.SALARY_INVALID)
    .positive(VALIDATION_MESSAGES.SALARY_INVALID)
    .max(10_000_000, '💰 Salary maximum seems unrealistic (max 10M)')
    .nullable()
    .optional(),

  salary_currency: z
    .enum(CURRENCY_CODES, {
      message: VALIDATION_MESSAGES.ENUM_INVALID(
        'Currency',
        CURRENCY_CODES.slice(0, 10)
      ),
    })
    .nullable()
    .optional(),

  salary_unit: z
    .enum(SALARY_UNITS, {
      message: VALIDATION_MESSAGES.ENUM_INVALID('Salary unit', SALARY_UNITS),
    })
    .nullable()
    .optional(),

  // Location & Remote work with enhanced validation
  workplace_type: z
    .enum(WORKPLACE_TYPES, {
      message: VALIDATION_MESSAGES.ENUM_INVALID(
        'Workplace type',
        WORKPLACE_TYPES
      ),
    })
    .nullable()
    .optional(),

  remote_region: z
    .enum(REMOTE_REGIONS, {
      message: VALIDATION_MESSAGES.ENUM_INVALID(
        'Remote region',
        REMOTE_REGIONS
      ),
    })
    .nullable()
    .optional(),

  timezone_requirements: z
    .string()
    .max(200, '🌍 Timezone requirements too long (max 200 chars)')
    .nullable()
    .optional(),

  workplace_city: z
    .string()
    .max(100, '🏙️ City name too long (max 100 chars)')
    .nullable()
    .optional(),

  workplace_country: z
    .enum(countries, {
      message: VALIDATION_MESSAGES.ENUM_INVALID(
        'Country',
        countries.slice(0, 10)
      ),
    })
    .nullable()
    .optional(),

  // Additional details with enhanced validation
  benefits: z
    .string()
    .max(
      SCHEMA_LIMITS.benefits,
      VALIDATION_MESSAGES.TEXT_TOO_LONG('Benefits', SCHEMA_LIMITS.benefits)
    )
    .nullable()
    .optional(),

  application_requirements: z
    .string()
    .max(
      SCHEMA_LIMITS.application_requirements,
      VALIDATION_MESSAGES.TEXT_TOO_LONG(
        'Application requirements',
        SCHEMA_LIMITS.application_requirements
      )
    )
    .nullable()
    .optional(),

  valid_through: z
    .string()
    .regex(
      /^\d{4}-\d{2}-\d{2}(T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?)?$/,
      VALIDATION_MESSAGES.DATE_INVALID
    )
    .nullable()
    .optional(),

  job_identifier: z
    .string()
    .max(100, '🆔 Job identifier too long (max 100 chars)')
    .nullable()
    .optional(),

  job_source_name: z
    .string()
    .max(100, '📋 Job source name too long (max 100 chars)')
    .nullable()
    .optional(),

  department: z
    .string()
    .max(100, '🏢 Department name too long (max 100 chars)')
    .nullable()
    .optional(),

  travel_required: z
    .boolean({
      message: '✈️ Travel required must be true/false boolean value',
    })
    .nullable()
    .optional(),

  // Career & Skills with enhanced validation
  career_level: z
    .array(
      z.enum(CAREER_LEVELS, {
        message: VALIDATION_MESSAGES.ENUM_INVALID(
          'Career level',
          CAREER_LEVELS
        ),
      })
    )
    .max(5, '📈 Too many career levels (max 5)')
    .nullable()
    .optional(),

  visa_sponsorship: z
    .enum(VISA_SPONSORSHIP_OPTIONS, {
      message: VALIDATION_MESSAGES.ENUM_INVALID(
        'Visa sponsorship',
        VISA_SPONSORSHIP_OPTIONS
      ),
    })
    .nullable()
    .optional(),

  languages: z
    .array(
      z.enum(LANGUAGE_CODES, {
        message: VALIDATION_MESSAGES.ENUM_INVALID(
          'Language',
          LANGUAGE_CODES.slice(0, 10)
        ),
      })
    )
    .max(10, '🗣️ Too many languages (max 10)')
    .nullable()
    .optional(),

  skills: z
    .string()
    .max(
      SCHEMA_LIMITS.skills,
      VALIDATION_MESSAGES.TEXT_TOO_LONG('Skills', SCHEMA_LIMITS.skills)
    )
    .nullable()
    .optional(),

  qualifications: z
    .string()
    .max(
      SCHEMA_LIMITS.qualifications,
      VALIDATION_MESSAGES.TEXT_TOO_LONG(
        'Qualifications',
        SCHEMA_LIMITS.qualifications
      )
    )
    .nullable()
    .optional(),

  education_requirements: z
    .string()
    .max(
      SCHEMA_LIMITS.education_requirements,
      VALIDATION_MESSAGES.TEXT_TOO_LONG(
        'Education requirements',
        SCHEMA_LIMITS.education_requirements
      )
    )
    .nullable()
    .optional(),

  experience_requirements: z
    .string()
    .max(
      SCHEMA_LIMITS.experience_requirements,
      VALIDATION_MESSAGES.TEXT_TOO_LONG(
        'Experience requirements',
        SCHEMA_LIMITS.experience_requirements
      )
    )
    .nullable()
    .optional(),

  responsibilities: z
    .string()
    .max(
      SCHEMA_LIMITS.responsibilities,
      VALIDATION_MESSAGES.TEXT_TOO_LONG(
        'Responsibilities',
        SCHEMA_LIMITS.responsibilities
      )
    )
    .nullable()
    .optional(),

  // SEO & Classification with enhanced validation
  featured: z
    .boolean({
      message: '⭐ Featured must be true/false boolean value',
    })
    .nullable()
    .optional(),

  industry: z
    .string()
    .max(100, '🏭 Industry name too long (max 100 chars)')
    .nullable()
    .optional(),

  occupational_category: z
    .string()
    .max(100, '👔 Occupational category too long (max 100 chars)')
    .nullable()
    .optional(),
});

// Full schema including metadata
export const JobSchema = z.object({
  sourcedAt: z.string().datetime(),
  sourceUrl: z.string().url(),
  ...JobExtractionSchema.shape,
});

export type Job = z.infer<typeof JobSchema>;
export type JobExtraction = z.infer<typeof JobExtractionSchema>;
