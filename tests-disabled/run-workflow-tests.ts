#!/usr/bin/env tsx

/**
 * Workflow Test Runner - Day 4 Testing Strategy
 *
 * Runs all workflow-related tests from the rapid build plan
 * Completely detached from production - safe to delete
 */

import { spawn } from 'node:child_process';
import { performance } from 'node:perf_hooks';

type TestResult = {
  suite: string;
  passed: boolean;
  duration: number;
  error?: string;
};

const results: TestResult[] = [];

function runTestSuite(
  name: string,
  command: string,
  args: string[]
): Promise<TestResult> {
  const start = performance.now();

  return new Promise((resolve) => {
    const child = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
    });

    child.on('close', (code) => {
      const duration = performance.now() - start;
      const passed = code === 0;

      const result: TestResult = {
        suite: name,
        passed,
        duration,
        error: passed ? undefined : `Process exited with code ${code}`,
      };

      results.push(result);

      if (passed) {
      } else {
      }

      resolve(result);
    });

    child.on('error', (error) => {
      const duration = performance.now() - start;
      const result: TestResult = {
        suite: name,
        passed: false,
        duration,
        error: error.message,
      };

      results.push(result);
      resolve(result);
    });
  });
}

async function runWorkflowTests() {
  const testSuites = [
    {
      name: 'Unit Tests - Workflow Processing',
      command: 'npx',
      args: [
        'vitest',
        'run',
        'tests/unit/workflow-processing.test.ts',
        '--reporter=verbose',
      ],
    },
    {
      name: 'Integration Tests - Webhook to Workflow',
      command: 'npx',
      args: [
        'vitest',
        'run',
        'tests/integration/webhook-to-workflow.test.ts',
        '--reporter=verbose',
      ],
    },
    {
      name: 'Integration Tests - Workflow Validation',
      command: 'npx',
      args: [
        'vitest',
        'run',
        'tests/integration/workflow-validation.test.ts',
        '--reporter=verbose',
      ],
    },
  ];

  // Run all test suites
  for (const suite of testSuites) {
    await runTestSuite(suite.name, suite.command, suite.args);
  }

  // Print summary
  printSummary();

  // Exit with appropriate code
  const allPassed = results.every((result) => result.passed);
  process.exit(allPassed ? 0 : 1);
}

function printSummary() {
  const totalTests = results.length;
  const passedTests = results.filter((r) => r.passed).length;
  const failedTests = totalTests - passedTests;
  const _totalDuration = results.reduce((sum, r) => sum + r.duration, 0);
  results.forEach((result) => {
    const _status = result.passed ? '✅' : '❌';
    const _duration = (result.duration / 1000).toFixed(2);

    if (result.error) {
    }
  });

  if (failedTests > 0) {
  } else {
  }
}

// Load testing function
async function runLoadTests() {
  const loadTestTypes = ['light', 'moderate'];

  for (const testType of loadTestTypes) {
    try {
      const result = await runTestSuite(`Load Test - ${testType}`, 'npx', [
        'tsx',
        'tests/scripts/load-test-workflows.ts',
        testType,
        'http://localhost:3000',
      ]);

      if (!result.passed) {
      }
    } catch (_error) {}
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  const includeLoadTests = args.includes('--load');
  const loadTestOnly = args.includes('--load-only');

  try {
    if (loadTestOnly) {
      await runLoadTests();
    } else {
      await runWorkflowTests();

      if (includeLoadTests) {
        await runLoadTests();
      }
    }
  } catch (_error) {
    process.exit(1);
  }
}

// Help text
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  process.exit(0);
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}
