'use client';

import { MultiSelectUrlFilter } from '@/components/common/MultiSelectUrlFilter';
import { countries } from '@/lib/data/countries';

interface CountriesFilterUrlProps {
  disabled?: boolean;
}

/**
 * Countries filter component with URL state
 * Now uses the generic MultiSelectUrlFilter component
 */
export function CountriesFilterUrl({
  disabled = false,
}: CountriesFilterUrlProps) {
  return (
    <MultiSelectUrlFilter
      label="Countries"
      paramName="countries"
      options={countries}
      disabled={disabled}
      searchable={true}
      maxHeight="max-h-48"
      clearable={true}
      showCount={true}
    />
  );
}
