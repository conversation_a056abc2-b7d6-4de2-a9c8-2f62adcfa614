/**
 * Configuration Validation Utilities
 *
 * Consolidated validation functions for various configuration objects
 * including Airtable, environment variables, and service configurations.
 */

import {
  combineValidationResults,
  createValidationError,
  createValidationSuccess,
  URL_PATTERNS,
  type ValidationErrorResult,
  validateRequiredFields,
  validateStringLength,
} from './validation-utils';

// =============================================================================
// AIRTABLE VALIDATION
// =============================================================================

export interface AirtableConfig extends Record<string, unknown> {
  baseId?: string;
  tableName?: string;
  pat?: string;
}

/**
 * Validates Airtable base ID format
 * Valid format: app + 14 alphanumeric characters (e.g., apprhCjWTxfG3JX5p)
 */
export function validateAirtableBaseId(baseId: string): boolean {
  return URL_PATTERNS.AIRTABLE_BASE_ID.test(baseId);
}

/**
 * Validates Airtable PAT token format
 * Valid format: pat + 40+ characters
 */
export function validateAirtablePat(pat: string): boolean {
  return URL_PATTERNS.AIRTABLE_PAT.test(pat);
}

/**
 * Validates Airtable table name
 * Must be non-empty and reasonable length
 */
export function validateAirtableTableName(tableName: string): boolean {
  return tableName.length > 0 && tableName.length <= 100;
}

/**
 * Validates PAT and returns detailed error if invalid
 */
export function validatePatWithError(
  pat: string
): ValidationErrorResult & { error?: string } {
  if (!pat?.trim()) {
    const error = 'PAT token is required';
    return {
      valid: false,
      errors: [error],
      error, // Legacy compatibility
    };
  }

  if (!validateAirtablePat(pat)) {
    const error =
      'Invalid PAT format. PATs should start with "pat" and be at least 20 characters';
    return {
      valid: false,
      errors: [error],
      error, // Legacy compatibility
    };
  }

  return { valid: true, errors: [] };
}

/**
 * Validates complete Airtable configuration
 */
export function validateAirtableConfig(
  config: AirtableConfig
): ValidationErrorResult {
  const results: ValidationErrorResult[] = [];

  // Validate required fields
  results.push(validateRequiredFields(config, ['baseId', 'tableName', 'pat']));

  // Validate base ID format
  if (config.baseId && !validateAirtableBaseId(config.baseId)) {
    results.push(
      createValidationError(
        'Base ID format is invalid (must be "app" + 14 alphanumeric characters)'
      )
    );
  }

  // Validate table name
  if (config.tableName && !validateAirtableTableName(config.tableName)) {
    results.push(
      createValidationError('Table name is invalid (must be 1-100 characters)')
    );
  }

  // Validate PAT
  if (config.pat) {
    results.push(validatePatWithError(config.pat));
  }

  return combineValidationResults(...results);
}

// =============================================================================
// ENVIRONMENT VALIDATION
// =============================================================================

export interface EnvironmentConfig extends Record<string, unknown> {
  nodeEnv?: string;
  databaseUrl?: string;
  openaiApiKey?: string;
  upstashRedisUrl?: string;
  upstashToken?: string;
  slackWebhookUrl?: string;
}

/**
 * Validate environment configuration
 */
export function validateEnvironmentConfig(
  config: EnvironmentConfig
): ValidationErrorResult {
  const results: ValidationErrorResult[] = [];
  const requiredFields: (keyof EnvironmentConfig)[] = [];

  // Different requirements based on environment
  if (config.nodeEnv === 'production') {
    requiredFields.push('databaseUrl', 'openaiApiKey');
  }

  results.push(validateRequiredFields(config, requiredFields));

  // Validate URL formats
  if (config.databaseUrl && !config.databaseUrl.startsWith('postgresql://')) {
    results.push(
      createValidationError(
        'Database URL must be a valid PostgreSQL connection string'
      )
    );
  }

  if (
    config.slackWebhookUrl &&
    !config.slackWebhookUrl.startsWith('https://hooks.slack.com/')
  ) {
    results.push(
      createValidationError('Slack webhook URL must be a valid Slack webhook')
    );
  }

  return combineValidationResults(...results);
}

// =============================================================================
// API KEY VALIDATION
// =============================================================================

export type ApiKeyValidation = {
  service: string;
  key: string;
  pattern?: RegExp;
  description?: string;
};

/**
 * Validate API key format
 */
export function validateApiKey(
  validation: ApiKeyValidation
): ValidationErrorResult {
  const { service, key, pattern, description } = validation;

  if (!key?.trim()) {
    return createValidationError(`${service} API key is required`);
  }

  if (pattern && !pattern.test(key)) {
    const message = description
      ? `${service} API key format is invalid: ${description}`
      : `${service} API key format is invalid`;
    return createValidationError(message);
  }

  return createValidationSuccess();
}

/**
 * Common API key validations
 */
export const API_KEY_VALIDATIONS = {
  OPENAI: {
    service: 'OpenAI',
    pattern: /^sk-[a-zA-Z0-9]{48}$/,
    description: 'Must start with "sk-" followed by 48 characters',
  },
  UPSTASH: {
    service: 'Upstash',
    pattern: /^[A-Za-z0-9_-]+$/,
    description:
      'Must contain only alphanumeric characters, hyphens, and underscores',
  },
} as const;

// =============================================================================
// WEBHOOK VALIDATION
// =============================================================================

export interface WebhookConfig extends Record<string, unknown> {
  url: string;
  secret?: string;
  events?: string[];
}

/**
 * Validate webhook configuration
 */
export function validateWebhookConfig(
  config: WebhookConfig
): ValidationErrorResult {
  const results: ValidationErrorResult[] = [];

  // Validate required fields
  results.push(validateRequiredFields(config, ['url']));

  // Validate URL format
  if (config.url && !config.url.startsWith('https://')) {
    results.push(createValidationError('Webhook URL must use HTTPS'));
  }

  // Validate secret if provided
  if (config.secret) {
    results.push(
      validateStringLength(config.secret, {
        min: 8,
        max: 128,
        fieldName: 'Webhook secret',
      })
    );
  }

  // Validate events if provided
  if (config.events && config.events.length === 0) {
    results.push(
      createValidationError('At least one webhook event must be specified')
    );
  }

  return combineValidationResults(...results);
}
