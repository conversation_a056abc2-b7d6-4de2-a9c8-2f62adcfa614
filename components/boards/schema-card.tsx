'use client';

import { Code, Database } from 'lucide-react';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '../ui/collapsible';

// Airtable schema types for board-specific usage
type AirtableFieldChoice = {
  id: string;
  name: string;
  color?: string;
};

type AirtableField = {
  id: string;
  name: string;
  type: string;
  options?: {
    choices?: AirtableFieldChoice[];
    [key: string]: unknown;
  };
};

export type BoardSchemaData = {
  success: boolean;
  tableName: string;
  tableId: string;
  fields: AirtableField[];
  allTables: string[];
  error?: string;
};

type SchemaCardProps = {
  schemaData: BoardSchemaData;
  boardName?: string;
};

export function SchemaCard({ schemaData, boardName }: SchemaCardProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 font-medium text-sm">
          <Database className="h-5 w-5" />
          Airtable Schema
          {boardName && (
            <Badge className="ml-auto" variant="outline">
              {boardName}
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 gap-4">
          <div className="flex justify-between">
            <span className="font-medium text-xs">Table:</span>
            <code className="text-xs">{schemaData.tableName}</code>
          </div>
          <div className="flex justify-between">
            <span className="font-medium text-xs">Fields:</span>
            <span className="text-xs">
              {schemaData.fields?.length || 0} total
            </span>
          </div>
          {schemaData.allTables.length > 1 && (
            <div className="flex justify-between">
              <span className="font-medium text-xs">Available Tables:</span>
              <span className="text-xs">
                {schemaData.allTables.length} in base
              </span>
            </div>
          )}
        </div>

        <Collapsible>
          <CollapsibleTrigger asChild>
            <Button className="w-full justify-between" variant="outline">
              Field Details ({schemaData.fields?.length || 0})
              <Code className="h-4 w-4" />
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <Card className="mt-4">
              <CardContent className="max-h-60 overflow-y-auto p-4">
                <div className="space-y-2">
                  {schemaData.fields?.map((field) => (
                    <div className="text-xs" key={field.id}>
                      <div className="flex items-center justify-between">
                        <code className="font-mono">{field.name}</code>
                        <Badge variant="outline">{field.type}</Badge>
                      </div>
                      {(field.type === 'singleSelect' ||
                        field.type === 'multipleSelects') &&
                        field.options?.choices && (
                          <div className="ml-4 text-muted-foreground text-xs">
                            Options:{' '}
                            {field.options.choices
                              .slice(0, 3)
                              .map((choice) => choice.name)
                              .join(', ')}
                            {field.options.choices.length > 3 &&
                              ` +${field.options.choices.length - 3} more`}
                          </div>
                        )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </CollapsibleContent>
        </Collapsible>

        {schemaData.allTables.length > 1 && (
          <Collapsible>
            <CollapsibleTrigger asChild>
              <Button className="w-full justify-between" variant="outline">
                All Tables ({schemaData.allTables.length})
                <Code className="h-4 w-4" />
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <Card className="mt-4">
                <CardContent className="max-h-40 overflow-y-auto p-4">
                  <div className="space-y-1">
                    {schemaData.allTables.map((tableName) => (
                      <div
                        className={`text-xs ${
                          tableName === schemaData.tableName
                            ? 'font-medium text-primary'
                            : 'text-muted-foreground'
                        }`}
                        key={tableName}
                      >
                        <code className="font-mono">{tableName}</code>
                        {tableName === schemaData.tableName && (
                          <Badge className="ml-2" variant="default">
                            Current
                          </Badge>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </CollapsibleContent>
          </Collapsible>
        )}
      </CardContent>
    </Card>
  );
}
