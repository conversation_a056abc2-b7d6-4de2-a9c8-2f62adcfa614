import { AI_CONFIG } from '../config/ai-config';
import { estimateReadingTime, generateMetadata } from '../utils';
import type { AIExtractionResult, ExtractJobInput } from './ai-extraction';

/**
 * Extraction metadata creation utilities
 *
 * Handles the creation of performance and processing metadata
 * for extraction operations, including timing, token usage,
 * and memory tracking.
 */

export type ExtractionMetadata = {
  model: string;
  finishReason: string;
  response: {
    id: string;
    model: string;
    timestamp: Date;
  };
  contentLength: number;
  estimatedReadingTime: number;
  promptUsed: string;
  promptLength: number;
  duration?: number;
  tokenUsage?: {
    inputTokens: number;
    outputTokens: number;
    totalTokens: number;
  };
  memoryUsage?: {
    heapUsed: string;
    heapTotal: string;
    external: string;
    deltaHeapUsed: string;
  };
  [key: string]: unknown;
};

/**
 * Create comprehensive metadata object for extraction result
 */
export function createExtractionMetadata(
  startTime: number,
  usage: {
    inputTokens?: number;
    outputTokens?: number;
    totalTokens?: number;
  },
  result: AIExtractionResult,
  post: ExtractJobInput,
  finalPrompt: string,
  memoryInfo?: {
    startMemory: NodeJS.MemoryUsage;
    endMemory: NodeJS.MemoryUsage;
  }
): ExtractionMetadata {
  const duration = Date.now() - startTime;

  const baseMetadata = generateMetadata(startTime, usage, {
    // Model details
    model: AI_CONFIG.MODEL,
    finishReason: result.finishReason || 'unknown',

    // Response metadata
    response: {
      id: result.response?.id || 'unknown',
      model: result.response?.modelId || AI_CONFIG.MODEL,
      timestamp: result.response?.timestamp || new Date(),
    },

    // Additional info
    contentLength: post.content?.length || 0,
    estimatedReadingTime: estimateReadingTime(post.content || ''),

    // Prompt info
    promptUsed: 'default',
    promptLength: finalPrompt.length,
  });

  // Add extraction-specific metadata
  const extractionMetadata: ExtractionMetadata = {
    ...baseMetadata,
    model: AI_CONFIG.MODEL,
    finishReason: result.finishReason || 'unknown',
    response: {
      id: result.response?.id || 'unknown',
      model: result.response?.modelId || AI_CONFIG.MODEL,
      timestamp: result.response?.timestamp || new Date(),
    },
    contentLength: post.content?.length || 0,
    estimatedReadingTime: estimateReadingTime(post.content || ''),
    promptUsed: 'default',
    promptLength: finalPrompt.length,
    duration,
    tokenUsage: {
      inputTokens: usage.inputTokens || 0,
      outputTokens: usage.outputTokens || 0,
      totalTokens: usage.totalTokens || 0,
    },
  };

  // Add memory usage if provided
  if (memoryInfo) {
    extractionMetadata.memoryUsage = {
      heapUsed: `${memoryInfo.endMemory.heapUsed / 1024 / 1024}MB`,
      heapTotal: `${memoryInfo.endMemory.heapTotal / 1024 / 1024}MB`,
      external: `${memoryInfo.endMemory.external / 1024 / 1024}MB`,
      deltaHeapUsed: `${
        (memoryInfo.endMemory.heapUsed - memoryInfo.startMemory.heapUsed) /
        1024 /
        1024
      }MB`,
    };
  }

  return extractionMetadata;
}

/**
 * Create metadata for failed extractions
 */
export function createFailureMetadata(
  startTime: number,
  error: Error,
  post: ExtractJobInput,
  finalPrompt: string,
  memoryInfo?: {
    startMemory: NodeJS.MemoryUsage;
    endMemory: NodeJS.MemoryUsage;
  }
): Record<string, unknown> {
  const duration = Date.now() - startTime;

  const failureMetadata = {
    error: error.message,
    cause: error.cause,
    stack: error.stack?.split('\n').slice(0, 10),
    duration: `${duration}ms`,
    inputSize: post.content.length,
    model: AI_CONFIG.MODEL,
    environment: process.env.NODE_ENV,
    nodeVersion: process.version,
    openaiKeyExists: !!process.env.OPENAI_API_KEY,
    promptLength: finalPrompt.length,
    sourceUrl: post.sourceUrl,
    contentPreview: post.content.substring(0, 200),
  };

  // Add memory usage if provided
  if (memoryInfo) {
    Object.assign(failureMetadata, {
      memoryUsed: `${
        (memoryInfo.endMemory.heapUsed - memoryInfo.startMemory.heapUsed) /
        1024 /
        1024
      }MB`,
      memoryEnd: {
        heapUsed: `${memoryInfo.endMemory.heapUsed / 1024 / 1024}MB`,
        heapTotal: `${memoryInfo.endMemory.heapTotal / 1024 / 1024}MB`,
      },
    });
  }

  return failureMetadata;
}

/**
 * Create debug metadata for memory tracking
 */
export function createMemoryDebugInfo(memory: NodeJS.MemoryUsage) {
  return {
    heapUsed: `${memory.heapUsed / 1024 / 1024}MB`,
    heapTotal: `${memory.heapTotal / 1024 / 1024}MB`,
    external: `${memory.external / 1024 / 1024}MB`,
    rss: `${memory.rss / 1024 / 1024}MB`,
  };
}
