import { Client } from "@upstash/qstash";
import { logger } from "./utils";

// Initialize QStash client for schedules (different from workflow client)
const qstashClient = new Client({
  token: process.env.QSTASH_TOKEN!,
});

const SCHEDULE_ID = "automatic-job-processing";
const CRON_EXPRESSION = "*/2 * * * *"; // PRODUCTION TEST: Every 2 minutes for fast testing

/**
 * Get the destination URL for the job scheduler
 */
function getJobSchedulerUrl(): string {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://bordfeed.com";
  return `${baseUrl}/api/job-scheduler`;
}

/**
 * Create automatic job processing schedule
 */
export async function createAutomaticSchedule(): Promise<{
  success: boolean;
  scheduleId?: string;
  error?: string;
}> {
  try {
    const destination = getJobSchedulerUrl();

    logger.info("Creating automatic job processing schedule", {
      destination,
      cron: CRON_EXPRESSION,
      scheduleId: SCHEDULE_ID,
    });

    const result = await qstashClient.schedules.create({
      destination,
      cron: CRON_EXPRESSION,
      scheduleId: SCHEDULE_ID,
      headers: {
        Authorization: `Bearer ${process.env.SCHEDULER_SECRET}`,
        "Content-Type": "application/json",
      },
    });

    logger.info("✅ Automatic schedule created successfully", {
      scheduleId: result.scheduleId,
    });

    return {
      success: true,
      scheduleId: result.scheduleId,
    };
  } catch (error) {
    logger.error("❌ Failed to create automatic schedule", { error });
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

/**
 * Delete automatic job processing schedule
 */
export async function deleteAutomaticSchedule(): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    logger.info("Deleting automatic job processing schedule", {
      scheduleId: SCHEDULE_ID,
    });

    await qstashClient.schedules.delete(SCHEDULE_ID);

    logger.info("✅ Automatic schedule deleted successfully");

    return { success: true };
  } catch (error) {
    logger.error("❌ Failed to delete automatic schedule", { error });
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

/**
 * Get automatic schedule status
 */
export async function getScheduleStatus(): Promise<{
  exists: boolean;
  schedule?: any;
  error?: string;
}> {
  try {
    logger.info("Checking automatic schedule status", {
      scheduleId: SCHEDULE_ID,
    });

    const schedule = await qstashClient.schedules.get(SCHEDULE_ID);

    logger.info("✅ Schedule status retrieved", {
      scheduleId: schedule.scheduleId,
      cron: schedule.cron,
      destination: schedule.destination,
    });

    return {
      exists: true,
      schedule,
    };
  } catch (error) {
    // Schedule doesn't exist or other error
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";

    if (errorMessage.includes("404") || errorMessage.includes("not found")) {
      logger.info("Schedule does not exist");
      return { exists: false };
    }

    logger.error("❌ Failed to get schedule status", { error });
    return {
      exists: false,
      error: errorMessage,
    };
  }
}

/**
 * Toggle automatic schedule (enable if disabled, disable if enabled)
 */
export async function toggleAutomaticSchedule(): Promise<{
  success: boolean;
  action: "created" | "deleted";
  error?: string;
}> {
  try {
    const status = await getScheduleStatus();

    if (status.error) {
      return {
        success: false,
        action: "created",
        error: status.error,
      };
    }

    if (status.exists) {
      // Schedule exists, delete it
      const result = await deleteAutomaticSchedule();
      return {
        success: result.success,
        action: "deleted",
        error: result.error,
      };
    } else {
      // Schedule doesn't exist, create it
      const result = await createAutomaticSchedule();
      return {
        success: result.success,
        action: "created",
        error: result.error,
      };
    }
  } catch (error) {
    logger.error("❌ Failed to toggle automatic schedule", { error });
    return {
      success: false,
      action: "created",
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}
