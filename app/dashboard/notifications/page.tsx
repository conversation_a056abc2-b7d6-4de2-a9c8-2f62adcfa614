'use client';

import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

type FlagsResponse = {
  configured: boolean;
  flags: {
    enableCritical: boolean;
    enableAlert: boolean;
    enableInfo: boolean;
    enablePipeline: boolean;
    pipelineOnlyFailures: boolean;
    pipelineSteps: string[];
  };
};

export default function NotificationsSettingsPage() {
  const [loading, setLoading] = useState(true);
  const [configured, setConfigured] = useState(false);
  const [flags, setFlags] = useState<FlagsResponse['flags'] | null>(null);
  const [stepsText, setStepsText] = useState('');
  const [testLoading, setTestLoading] = useState<string | null>(null);
  // Local-only frequency settings (persisted locally; backend wiring optional)
  const [healthCadence, setHealthCadence] = useState<string>('1h');
  const [jobsScrapedOnComplete, setJobsScrapedOnComplete] =
    useState<boolean>(true);
  const [jobsProcessedOnComplete, setJobsProcessedOnComplete] =
    useState<boolean>(true);
  const [jobsProcessedDaily, setJobsProcessedDaily] = useState<boolean>(true);
  const [jobsProcessedDailyTime, setJobsProcessedDailyTime] =
    useState<string>('09:00');

  useEffect(() => {
    async function load() {
      try {
        const res = await fetch('/api/notifications/settings', {
          cache: 'no-store',
        });
        const data = (await res.json()) as any;
        setConfigured(Boolean(data.configured));
        setFlags(data.flags as FlagsResponse['flags']);
        setStepsText((data.flags?.pipelineSteps ?? []).join(', '));
        // Frequency from backend if present, else fallback to local
        if (data.frequency) {
          if (typeof data.frequency.healthCadence === 'string') {
            setHealthCadence(data.frequency.healthCadence);
          }
          if (typeof data.frequency.jobsScrapedOnComplete === 'boolean') {
            setJobsScrapedOnComplete(data.frequency.jobsScrapedOnComplete);
          }
          if (typeof data.frequency.jobsProcessedOnComplete === 'boolean') {
            setJobsProcessedOnComplete(data.frequency.jobsProcessedOnComplete);
          }
          if (typeof data.frequency.jobsProcessedDaily === 'boolean') {
            setJobsProcessedDaily(data.frequency.jobsProcessedDaily);
          }
          if (typeof data.frequency.jobsProcessedDailyTime === 'string') {
            setJobsProcessedDailyTime(data.frequency.jobsProcessedDailyTime);
          }
        }
        const persisted = localStorage.getItem('bf_slack_freq');
        if (persisted) {
          const parsed = JSON.parse(persisted) as Record<string, unknown>;
          if (typeof parsed.healthCadence === 'string') {
            setHealthCadence(parsed.healthCadence);
          }
          if (typeof parsed.jobsScrapedOnComplete === 'boolean') {
            setJobsScrapedOnComplete(parsed.jobsScrapedOnComplete);
          }
          if (typeof parsed.jobsProcessedOnComplete === 'boolean') {
            setJobsProcessedOnComplete(parsed.jobsProcessedOnComplete);
          }
          if (typeof parsed.jobsProcessedDaily === 'boolean') {
            setJobsProcessedDaily(parsed.jobsProcessedDaily);
          }
          if (typeof parsed.jobsProcessedDailyTime === 'string') {
            setJobsProcessedDailyTime(parsed.jobsProcessedDailyTime);
          }
        }
      } finally {
        setLoading(false);
      }
    }
    void load();
  }, []);

  const disabledHint = configured
    ? undefined
    : 'Slack webhook not configured (SLACK_WEBHOOK_URL).';

  const sendTestNotification = async (type: string, options?: any) => {
    if (!configured) {
      toast.error('Slack notifications not configured', {
        description:
          'Set SLACK_WEBHOOK_URL environment variable to enable testing',
      });
      return;
    }

    // Handle special pipeline loading states
    const loadingKey =
      type === 'pipeline'
        ? `pipeline-${options?.success ? 'success' : 'failure'}`
        : type;

    // Create a loading toast
    const loadingToast = toast.loading(`Sending test ${type} notification...`, {
      description: 'This may take a few seconds',
    });

    setTestLoading(loadingKey);
    try {
      const response = await fetch('/api/notifications/test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ type, ...options }),
      });

      const result = await response.json();

      // Dismiss loading toast
      toast.dismiss(loadingToast);

      if (result.success) {
        // Show success toast with different messages for each type
        const successMessages = {
          critical: {
            title: '🚨 Critical test notification sent!',
            description: 'Check your Slack channel for the critical alert',
          },
          alert: {
            title: '⚠️ Warning test notification sent!',
            description: 'Check your Slack channel for the warning message',
          },
          info: {
            title: '📢 Info test notification sent!',
            description: 'Check your Slack channel for the info message',
          },
          pipeline: {
            title: `🔄 Pipeline ${options?.success ? 'success' : 'failure'} test sent!`,
            description: `Check your Slack channel for the pipeline ${options?.success ? 'success' : 'failure'} notification`,
          },
        };

        const message = successMessages[type as keyof typeof successMessages];
        toast.success(message.title, {
          description: message.description,
          duration: 4000,
        });
      } else {
        toast.error('Test notification failed', {
          description: result.error || 'Unknown error occurred',
          duration: 5000,
        });
      }
    } catch (error) {
      // Dismiss loading toast
      toast.dismiss(loadingToast);

      toast.error('Failed to send test notification', {
        description:
          error instanceof Error
            ? error.message
            : 'Network error or server unavailable',
        duration: 5000,
      });
    } finally {
      setTestLoading(null);
    }
  };

  return (
    <div className="mx-auto w-full max-w-4xl space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Slack Notifications</CardTitle>
        </CardHeader>
        <CardContent className="space-y-5">
          {loading && (
            <div className="text-muted-foreground text-sm">Loading…</div>
          )}
          {!loading && flags && (
            <>
              <Table>
                <TableCaption>
                  Configure which Slack notifications are sent. Changes are
                  saved locally for now.
                </TableCaption>
                <TableHeader>
                  <TableRow>
                    <TableHead>Category</TableHead>
                    <TableHead className="min-w-[360px]">Description</TableHead>
                    <TableHead className="w-[100px]">Enabled</TableHead>
                    <TableHead>Options</TableHead>
                    <TableHead className="w-[100px]">Test</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell className="font-medium">Critical</TableCell>
                    <TableCell>
                      System failures, outages, or data corruption.
                    </TableCell>
                    <TableCell>
                      <Checkbox
                        checked={flags.enableCritical}
                        disabled={!configured}
                        onCheckedChange={(val) =>
                          setFlags({ ...flags, enableCritical: Boolean(val) })
                        }
                      />
                    </TableCell>
                    <TableCell />
                    <TableCell>
                      <Button
                        disabled={!configured || testLoading === 'critical'}
                        onClick={() => sendTestNotification('critical')}
                        size="sm"
                        variant="outline"
                      >
                        {testLoading === 'critical' ? '...' : 'Test'}
                      </Button>
                    </TableCell>
                  </TableRow>

                  <TableRow>
                    <TableCell className="font-medium">Warning</TableCell>
                    <TableCell>
                      Degraded performance, rate limits, recoverable issues.
                    </TableCell>
                    <TableCell>
                      <Checkbox
                        checked={flags.enableAlert}
                        disabled={!configured}
                        onCheckedChange={(val) =>
                          setFlags({ ...flags, enableAlert: Boolean(val) })
                        }
                      />
                    </TableCell>
                    <TableCell />
                    <TableCell>
                      <Button
                        disabled={!configured || testLoading === 'alert'}
                        onClick={() => sendTestNotification('alert')}
                        size="sm"
                        variant="outline"
                      >
                        {testLoading === 'alert' ? '...' : 'Test'}
                      </Button>
                    </TableCell>
                  </TableRow>

                  <TableRow>
                    <TableCell className="font-medium">Info</TableCell>
                    <TableCell>
                      Milestones, metrics, and general informational events.
                    </TableCell>
                    <TableCell>
                      <Checkbox
                        checked={flags.enableInfo}
                        disabled={!configured}
                        onCheckedChange={(val) =>
                          setFlags({ ...flags, enableInfo: Boolean(val) })
                        }
                      />
                    </TableCell>
                    <TableCell />
                    <TableCell>
                      <Button
                        disabled={!configured || testLoading === 'info'}
                        onClick={() => sendTestNotification('info')}
                        size="sm"
                        variant="outline"
                      >
                        {testLoading === 'info' ? '...' : 'Test'}
                      </Button>
                    </TableCell>
                  </TableRow>

                  <TableRow>
                    <TableCell className="font-medium">Pipeline</TableCell>
                    <TableCell>End-to-end pipeline step updates.</TableCell>
                    <TableCell>
                      <Checkbox
                        checked={flags.enablePipeline}
                        disabled={!configured}
                        onCheckedChange={(val) =>
                          setFlags({ ...flags, enablePipeline: Boolean(val) })
                        }
                      />
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col gap-2">
                        <div className="flex items-center gap-2">
                          <Checkbox
                            checked={flags.pipelineOnlyFailures}
                            disabled={!(configured && flags.enablePipeline)}
                            onCheckedChange={(val) =>
                              setFlags({
                                ...flags,
                                pipelineOnlyFailures: Boolean(val),
                              })
                            }
                          />
                          <span className="text-muted-foreground text-sm">
                            Only failures
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Input
                            className="w-full max-w-[360px]"
                            disabled={!(configured && flags.enablePipeline)}
                            onChange={(e) => setStepsText(e.target.value)}
                            placeholder="SOURCED, DEDUPED, PROCESSED"
                            value={stepsText}
                          />
                          <span className="text-muted-foreground text-xs">
                            Steps allowlist
                          </span>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col gap-1">
                        <Button
                          className="text-xs"
                          disabled={
                            !configured || testLoading === 'pipeline-success'
                          }
                          onClick={() =>
                            sendTestNotification('pipeline', {
                              success: true,
                              step: 'TEST_SUCCESS',
                            })
                          }
                          size="sm"
                          variant="outline"
                        >
                          {testLoading === 'pipeline-success'
                            ? '...'
                            : '✓ Success'}
                        </Button>
                        <Button
                          className="text-xs"
                          disabled={
                            !configured || testLoading === 'pipeline-failure'
                          }
                          onClick={() =>
                            sendTestNotification('pipeline', {
                              success: false,
                              step: 'TEST_FAILURE',
                            })
                          }
                          size="sm"
                          variant="outline"
                        >
                          {testLoading === 'pipeline-failure'
                            ? '...'
                            : '✗ Failure'}
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">Health</TableCell>
                    <TableCell>
                      Periodic health summary notifications.
                    </TableCell>
                    <TableCell>
                      <Select
                        onValueChange={setHealthCadence}
                        value={healthCadence}
                      >
                        <SelectTrigger className="w-full max-w-[240px]">
                          <SelectValue placeholder="Off" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="off">Off</SelectItem>
                          <SelectItem value="15m">Every 15m</SelectItem>
                          <SelectItem value="30m">Every 30m</SelectItem>
                          <SelectItem value="1h">Every 1h</SelectItem>
                          <SelectItem value="6h">Every 6h</SelectItem>
                          <SelectItem value="24h">Daily</SelectItem>
                        </SelectContent>
                      </Select>
                    </TableCell>
                    <TableCell>
                      <span className="text-muted-foreground text-xs">
                        Scheduled
                      </span>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">Jobs scraped</TableCell>
                    <TableCell>
                      Send when scraping completes for a source run.
                    </TableCell>
                    <TableCell>
                      <Checkbox
                        checked={jobsScrapedOnComplete}
                        disabled={!configured}
                        onCheckedChange={(v) =>
                          setJobsScrapedOnComplete(Boolean(v))
                        }
                      />
                    </TableCell>
                    <TableCell />
                    <TableCell>
                      <span className="text-muted-foreground text-xs">
                        On events
                      </span>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">
                      Jobs processed
                    </TableCell>
                    <TableCell>
                      Send when processing completes; optionally a daily
                      summary.
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col gap-2">
                        <div className="flex items-center gap-2">
                          <Checkbox
                            checked={jobsProcessedOnComplete}
                            disabled={!configured}
                            onCheckedChange={(v) =>
                              setJobsProcessedOnComplete(Boolean(v))
                            }
                          />
                          <span className="text-muted-foreground text-sm">
                            On complete
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Checkbox
                            checked={jobsProcessedDaily}
                            disabled={!configured}
                            onCheckedChange={(v) =>
                              setJobsProcessedDaily(Boolean(v))
                            }
                          />
                          <span className="text-muted-foreground text-sm">
                            Daily summary at
                          </span>
                          <Input
                            className="w-full max-w-[160px]"
                            disabled={!(configured && jobsProcessedDaily)}
                            onChange={(e) =>
                              setJobsProcessedDailyTime(e.target.value)
                            }
                            type="time"
                            value={jobsProcessedDailyTime}
                          />
                        </div>
                      </div>
                    </TableCell>
                    <TableCell />
                    <TableCell>
                      <span className="text-muted-foreground text-xs">
                        On events
                      </span>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>

              <div className="flex items-center gap-2 pt-2">
                <Button
                  disabled={!configured}
                  onClick={async () => {
                    if (!configured) {
                      toast.error('Cannot save settings', {
                        description: 'Slack webhook not configured',
                      });
                      return;
                    }

                    const loadingToast = toast.loading(
                      'Saving notification settings...',
                      {
                        description: 'Updating your preferences',
                      }
                    );

                    try {
                      const payload = {
                        enableCritical: flags.enableCritical,
                        enableAlert: flags.enableAlert,
                        enableInfo: flags.enableInfo,
                        enablePipeline: flags.enablePipeline,
                        pipelineOnlyFailures: flags.pipelineOnlyFailures,
                        pipelineSteps: stepsText
                          .split(',')
                          .map((s) => s.trim().toUpperCase())
                          .filter(Boolean),
                        healthCadence,
                        jobsScrapedOnComplete,
                        jobsProcessedOnComplete,
                        jobsProcessedDaily,
                        jobsProcessedDailyTime,
                      };

                      const response = await fetch(
                        '/api/notifications/settings',
                        {
                          method: 'PUT',
                          headers: { 'Content-Type': 'application/json' },
                          body: JSON.stringify(payload),
                        }
                      );

                      const result = await response.json();

                      toast.dismiss(loadingToast);

                      if (response.ok && result.success) {
                        toast.success('Settings saved successfully!', {
                          description:
                            'Your notification preferences have been updated',
                          duration: 3000,
                        });
                      } else {
                        toast.error('Failed to save settings', {
                          description: result.error || 'Unknown error occurred',
                          duration: 5000,
                        });
                      }
                    } catch (error) {
                      toast.dismiss(loadingToast);
                      toast.error('Failed to save settings', {
                        description:
                          error instanceof Error
                            ? error.message
                            : 'Network error or server unavailable',
                        duration: 5000,
                      });
                    }
                  }}
                  type="button"
                >
                  Save
                </Button>
                {disabledHint && (
                  <span className="text-muted-foreground text-xs">
                    {disabledHint}
                  </span>
                )}
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
