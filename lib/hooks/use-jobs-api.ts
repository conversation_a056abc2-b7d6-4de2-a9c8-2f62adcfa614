import type { SortingState } from '@tanstack/react-table';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import type { DatabaseJob } from '@/lib/storage';

// Import the shared JobFilters type
import type { JobFilters } from '@/lib/types/job-filters';

type PaginationInfo = {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
};

type UseJobsApiProps = {
  filters: JobFilters;
  sorting: SortingState;
  pageSize?: number;
};

// Helper functions for URL building (pure functions)
function addPaginationParams(
  params: URLSearchParams,
  page: number,
  limit: number
) {
  params.set('page', String(page));
  params.set('limit', String(limit));
}

function addSortingParams(params: URLSearchParams, sorting: SortingState) {
  if (sorting.length > 0) {
    params.set('sortField', sorting[0].id);
    params.set('sortDirection', sorting[0].desc ? 'desc' : 'asc');
  }
}

function addBasicFilterParams(params: URLSearchParams, filters: JobFilters) {
  if (filters.search) {
    params.set('search', filters.search);
  }
  if (filters.status && filters.status !== 'all') {
    params.set('status', filters.status);
  }
  if (filters.processingStatus && filters.processingStatus !== 'all') {
    params.set('processingStatus', filters.processingStatus);
  }
  if (filters.sourceType && filters.sourceType !== 'all') {
    params.set('sourceType', filters.sourceType);
  }
}

function addArrayFilterParams(params: URLSearchParams, filters: JobFilters) {
  if (filters.jobTypes?.length) {
    params.set('jobTypes', filters.jobTypes.join(','));
  }
  if (filters.workplaceTypes?.length) {
    params.set('workplaceTypes', filters.workplaceTypes.join(','));
  }
  if (filters.careerLevels?.length) {
    params.set('careerLevels', filters.careerLevels.join(','));
  }
  if (filters.countries?.length) {
    params.set('countries', filters.countries.join(','));
  }
  if (filters.languages?.length) {
    params.set('languages', filters.languages.join(','));
  }
}

function addSalaryFilterParams(params: URLSearchParams, filters: JobFilters) {
  if (filters.salaryMin) {
    params.set('salaryMin', filters.salaryMin.toString());
  }
  if (filters.salaryMax) {
    params.set('salaryMax', filters.salaryMax.toString());
  }
  if (filters.salaryCurrencies?.length) {
    params.set('salaryCurrencies', filters.salaryCurrencies.join(','));
  }
}

function addKeywordFilterParams(params: URLSearchParams, filters: JobFilters) {
  if (filters.includeKeywords?.length) {
    params.set('includeKeywords', filters.includeKeywords.join(','));
  }
  if (filters.excludeKeywords?.length) {
    params.set('excludeKeywords', filters.excludeKeywords.join(','));
  }
}

export function useJobsApi({
  filters,
  sorting,
  pageSize = 20,
}: UseJobsApiProps) {
  const [jobs, setJobs] = useState<DatabaseJob[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState<PaginationInfo>({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: pageSize,
    hasNextPage: false,
    hasPreviousPage: false,
  });
  const [sqlQuery, setSqlQuery] = useState<string>('');
  const [executionTime, setExecutionTime] = useState<number | undefined>();

  // Use refs to track state and avoid dependency issues
  const currentPageRef = useRef(1);
  const isInitialLoadRef = useRef(false);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Simplified, stable URL builder that doesn't depend on component state
  const buildApiUrl = useCallback(
    (
      page: number,
      limit: number,
      currentFilters: JobFilters,
      currentSorting: SortingState
    ) => {
      const params = new URLSearchParams();

      addPaginationParams(params, page, limit);
      addSortingParams(params, currentSorting);
      addBasicFilterParams(params, currentFilters);
      addArrayFilterParams(params, currentFilters);
      addSalaryFilterParams(params, currentFilters);
      addKeywordFilterParams(params, currentFilters);

      return `/api/jobs?${params.toString()}`;
    },
    []
  ); // No dependencies - pure function

  const loadJobs = useCallback(
    async (
      page: number,
      limit: number,
      currentFilters: JobFilters,
      currentSorting: SortingState
    ) => {
      // Cancel any in-flight request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      abortControllerRef.current = new AbortController();

      try {
        setLoading(true);

        const url = buildApiUrl(page, limit, currentFilters, currentSorting);
        const response = await fetch(url, {
          signal: abortControllerRef.current.signal,
        });

        if (!response.ok) {
          throw new Error('Failed to fetch jobs');
        }

        const data = await response.json();

        // Update current page ref
        currentPageRef.current = page;

        setJobs(data.jobs || []);
        setPagination(
          data.pagination || {
            currentPage: page,
            totalPages: 1,
            totalItems: 0,
            itemsPerPage: limit,
            hasNextPage: false,
            hasPreviousPage: false,
          }
        );
        setSqlQuery(data.sqlInfo?.query || '');
        setExecutionTime(data.sqlInfo?.executionTime);
      } catch (error) {
        // Only handle non-abort errors
        if ((error as Error).name !== 'AbortError') {
          setJobs([]);
          setSqlQuery('');
          setExecutionTime(undefined);
        }
      } finally {
        setLoading(false);
        abortControllerRef.current = null;
      }
    },
    [buildApiUrl]
  );

  // Create stable dependencies to prevent infinite loops with URL-based filters
  // This is crucial when using nuqs filters that recreate objects on every render
  const filtersKey = useMemo(() => JSON.stringify(filters), [filters]);
  const sortingKey = useMemo(() => JSON.stringify(sorting), [sorting]);

  // Single useEffect for all data loading with debounce to prevent infinite loops
  // biome-ignore lint/correctness/useExhaustiveDependencies: Intentionally exclude loadJobs to prevent circular dependency
  useEffect(() => {
    // Debounce API calls to prevent infinite loops
    const timeoutId = setTimeout(() => {
      // Reset to page 1 when filters, sorting, or page size change (except for initial load)
      const targetPage = isInitialLoadRef.current ? 1 : 1;

      loadJobs(targetPage, pageSize, filters, sorting);
      isInitialLoadRef.current = true;
    }, 100); // 100ms debounce

    // Cleanup function to cancel timeout and in-flight requests
    return () => {
      clearTimeout(timeoutId);
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [filtersKey, sortingKey, pageSize]); // Use stable string keys instead of objects

  const handlePageChange = useCallback(
    (newPage: number) => {
      if (newPage !== currentPageRef.current && !loading) {
        loadJobs(newPage, pageSize, filters, sorting);
      }
    },
    [filters, sorting, loading, pageSize, loadJobs] // Removed loadJobs to prevent circular dependency
  );

  const handlePageSizeChange = useCallback(
    (newPageSize: number) => {
      // Reset to page 1 when changing page size
      currentPageRef.current = 1;
      loadJobs(1, newPageSize, filters, sorting);
    },
    [filters, sorting, loadJobs] // Removed loadJobs to prevent circular dependency
  );

  return {
    jobs,
    loading,
    pagination,
    sqlQuery,
    executionTime,
    loadJobs: useCallback(
      () => loadJobs(currentPageRef.current, pageSize, filters, sorting),
      [filters, sorting, pageSize, loadJobs] // Removed loadJobs to prevent circular dependency
    ),
    handlePageChange,
    handlePageSizeChange,
    buildApiUrl: useCallback(
      (page?: number) =>
        buildApiUrl(page || currentPageRef.current, pageSize, filters, sorting),
      [filters, sorting, pageSize, buildApiUrl] // Removed buildApiUrl to prevent circular dependency
    ),
  };
}
