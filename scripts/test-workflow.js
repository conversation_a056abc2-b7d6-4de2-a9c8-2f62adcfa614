#!/usr/bin/env node

/**
 * Test script to trigger Upstash Workflow in production
 * This bypasses the Vercel auth by using the workflow client directly
 */

import { Client } from '@upstash/workflow';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const workflowClient = new Client({
  token: process.env.QSTASH_TOKEN,
});

async function testWorkflow() {
  const response = await workflowClient.trigger({
    url: 'https://bordfeed-j6k8bm6zu-growthlog.vercel.app/api/workflows/process-jobs',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      batchSize: 2,
      source: 'production_test_script',
    }),
  });

  return response.workflowRunId;
}

// Run the test
testWorkflow()
  .then((_runId) => {
    process.exit(0);
  })
  .catch((_error) => {
    process.exit(1);
  });
