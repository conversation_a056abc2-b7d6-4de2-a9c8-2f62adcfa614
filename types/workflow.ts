/**
 * Workflow Types for Upstash Workflow Integration
 *
 * Database-aligned interfaces for workflow processing
 */

// =============================================================================
// WORKFLOW PAYLOAD TYPES
// =============================================================================

export interface JobProcessingPayload {
  batchSize?: number;
  source?: string;
  priority?: "low" | "normal" | "high";
}

export interface JobPostingPayload {
  boardId?: string;
  maxJobs?: number;
  dryRun?: boolean;
}

export interface JobMonitoringPayload {
  maxJobs?: number;
  source?: string;
  olderThanHours?: number;
}

// =============================================================================
// DATABASE TYPES
// =============================================================================

export interface DatabaseJob {
  id: string; // UUID
  processing_status: "pending" | "processing" | "completed" | "failed";
  external_id: string;
  raw_sourced_job_data: any;
  ai_metadata?: any;

  // Job fields
  title?: string;
  company?: string;
  source_type?: string;
  source_name?: string;

  // Workflow tracking fields
  workflow_run_id?: string | null;
  workflow_step?: string | null;
  locked_at?: string | null;
  locked_by?: string | null;

  // Timestamps
  created_at: string;
  updated_at: string;
  processed_at?: string | null;
  description?: string;
  apply_url?: string;
  source_url?: string;

  // Monitoring fields
  monitor_status?: "active" | "closed" | "filled" | "unknown";
  monitor_attempts?: number;
  last_checked_at?: string | null;
  next_try_at?: string | null;
}

export interface WorkflowRun {
  id: string;
  workflow_type: string;
  status: "running" | "completed" | "failed" | "partial_failure";
  started_at: string;
  completed_at?: string | null;
  job_count: number;
  error_message?: string | null;
}

// =============================================================================
// WORKFLOW STEP TYPES
// =============================================================================

export interface JobLockResult {
  lockedJobs: DatabaseJob[];
  lockCount: number;
  workflowRunId: string;
}

export interface JobProcessingResult {
  id: string;
  success: boolean;
  ai_metadata?: any;
  usage?: any;
  error?: string;
  errorType?: string;
  processingTime?: number;
  finishReason?: string;
  validationErrors?: any[];
  validationTime?: number;
  fieldsValidated?: number;
  warnings?: string[];
  debugInfo?: {
    jobTitle?: string;
    company?: string;
    sourceType?: string;
    hasRawData?: boolean;
    suggestions?: string[];
    cause?: any;
    aiResponse?: string;
  };
}

export interface WorkflowStepResult {
  success: boolean;
  message: string;
  data?: any;
  error?: string;
  duration?: number;
}

// =============================================================================
// WORKFLOW CONTEXT TYPES
// =============================================================================

export interface WorkflowContext {
  workflowRunId: string;
  workflowType: string;
  startedAt: string;
  stepCount: number;
  currentStep?: string;
}

export interface WorkflowStats {
  active: number;
  completed: number;
  failed: number;
  partial_failure: number;
  total_today: number;
  jobs_processed_today: number;
}

export interface WorkflowQueue {
  pending: number;
  processing: number;
}

export interface WorkflowStatusResponse {
  stats: WorkflowStats;
  queue: WorkflowQueue;
  recent_runs: WorkflowRun[];
  timestamp: string;
}

// =============================================================================
// ERROR TYPES
// =============================================================================

export class WorkflowError extends Error {
  constructor(
    message: string,
    public readonly step: string,
    public readonly workflowRunId: string,
    public readonly cause?: Error
  ) {
    super(message);
    this.name = "WorkflowError";
  }
}

export class JobLockError extends WorkflowError {
  constructor(message: string, workflowRunId: string, cause?: Error) {
    super(message, "job-locking", workflowRunId, cause);
    this.name = "JobLockError";
  }
}

export class AIProcessingError extends WorkflowError {
  constructor(
    message: string,
    workflowRunId: string,
    public readonly jobId: string,
    cause?: Error
  ) {
    super(message, "ai-processing", workflowRunId, cause);
    this.name = "AIProcessingError";
  }
}

// =============================================================================
// UTILITY TYPES
// =============================================================================

export type WorkflowStep =
  | "lock-jobs"
  | "ai-processing"
  | "save-results"
  | "cleanup"
  | "fetch-jobs"
  | "post-to-airtable"
  | "monitor-urls"
  | "update-status";

export type WorkflowType =
  | "process-jobs"
  | "post-jobs"
  | "monitor-jobs"
  | "cleanup-stuck-jobs";

// =============================================================================
// CONFIGURATION TYPES
// =============================================================================

export interface WorkflowConfig {
  maxConcurrency: number;
  timeoutMs: number;
  retryAttempts: number;
  retryDelayMs: number;
  batchSize: number;
}

export const DEFAULT_WORKFLOW_CONFIG: WorkflowConfig = {
  maxConcurrency: 5,
  timeoutMs: 300_000, // 5 minutes
  retryAttempts: 3,
  retryDelayMs: 1000,
  batchSize: 10,
};
