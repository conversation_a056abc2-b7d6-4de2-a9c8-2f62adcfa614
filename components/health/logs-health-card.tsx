'use client';

import { FileText, TrendingUp } from 'lucide-react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';

type LogsHealthCardProps = {
  data?: Record<string, number>; // Decision layer counts from verify-logs endpoint
};

export function LogsHealthCard({ data }: LogsHealthCardProps) {
  if (!data) {
    return (
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="font-medium text-sm">Log Activity</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="font-bold text-2xl">Loading...</div>
        </CardContent>
      </Card>
    );
  }

  const totalLogs = Object.values(data).reduce((sum, count) => sum + count, 0);
  const logEntries = Object.entries(data).sort(([, a], [, b]) => b - a);

  const getLayerColor = (layer: string) => {
    switch (layer) {
      case 'ai':
        return 'text-blue-600';
      case 'phrase':
        return 'text-green-600';
      case 'head':
        return 'text-orange-600';
      case 'heuristic':
        return 'text-purple-600';
      default:
        return 'text-gray-600';
    }
  };

  const getLayerDescription = (layer: string) => {
    switch (layer) {
      case 'ai':
        return 'AI Classification';
      case 'phrase':
        return 'Phrase Matching';
      case 'head':
        return 'HTTP Head Check';
      case 'heuristic':
        return 'Rule-based Logic';
      default:
        return layer;
    }
  };

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center gap-2 font-medium text-sm">
          <FileText className="h-4 w-4" />
          Log Activity
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="font-bold text-2xl">{totalLogs}</span>
            <TrendingUp className="h-4 w-4 text-green-500" />
          </div>
          <p className="text-muted-foreground text-xs">Logs (Last 5 min)</p>
        </div>

        <div className="space-y-3">
          {logEntries.length > 0 ? (
            <div className="space-y-2">
              <div className="font-medium text-xs">Decision Layers</div>
              {logEntries.map(([layer, count]) => (
                <div
                  className="flex items-center justify-between text-xs"
                  key={layer}
                >
                  <div className="flex items-center gap-2">
                    <div
                      className={`h-2 w-2 rounded-full ${getLayerColor(
                        layer
                      ).replace('text-', 'bg-')}`}
                    />
                    <span>{getLayerDescription(layer)}</span>
                  </div>
                  <span className={`font-medium ${getLayerColor(layer)}`}>
                    {count}
                  </span>
                </div>
              ))}
            </div>
          ) : (
            <div className="py-4 text-center">
              <div className="text-muted-foreground text-sm">
                No recent log activity
              </div>
              <div className="mt-1 text-muted-foreground text-xs">
                Monitor logs appear here when jobs are being processed
              </div>
            </div>
          )}

          <div className="border-t pt-2">
            <div className="text-muted-foreground text-xs">
              Shows monitoring decision layers from the last 5 minutes. Each
              layer represents a different job status detection method.
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
