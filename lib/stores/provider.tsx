/**
 * Store Provider for Zustand stores in Next.js
 * This provider ensures stores are properly initialized for SSR
 */

'use client';

import { createContext, type ReactNode, useContext, useRef } from 'react';
import type { StoreApi } from 'zustand';

/**
 * Context for accessing store instances
 * This will be expanded as we add more stores
 */
type StoreContextValue = {
  // Placeholder - will be populated with actual stores
  // sourcesStore?: StoreApi<SourcesState>;
  // boardsStore?: StoreApi<BoardsState>;
  // jobsStore?: StoreApi<JobsState>;
  // healthStore?: StoreApi<HealthState>;
  // uiStore?: StoreApi<UIState>;
  initialized: boolean;
};

const StoreContext = createContext<StoreContextValue | null>(null);

/**
 * Props for the StoreProvider component
 */
type StoreProviderProps = {
  children: ReactNode;
  /**
   * Initial state for stores (useful for SSR)
   * Can be populated from server-side data fetching
   */
  initialState?: Record<string, unknown>;
};

/**
 * StoreProvider component that initializes and provides access to all Zustand stores
 *
 * @example
 * ```tsx
 * // In app/layout.tsx
 * import { StoreProvider } from '@/lib/stores/provider';
 *
 * export default function RootLayout({ children }) {
 *   return (
 *     <html>
 *       <body>
 *         <StoreProvider>
 *           {children}
 *         </StoreProvider>
 *       </body>
 *     </html>
 *   );
 * }
 * ```
 */
export function StoreProvider({ children }: StoreProviderProps) {
  // Use ref to ensure stores are only created once
  const storeRef = useRef<StoreContextValue | undefined>(undefined);

  if (!storeRef.current) {
    // Initialize stores here
    // This will be expanded as we create actual stores
    storeRef.current = {
      initialized: true,
      // sourcesStore: createSourcesStore(initialState?.sources),
      // boardsStore: createBoardsStore(initialState?.boards),
      // jobsStore: createJobsStore(initialState?.jobs),
      // healthStore: createHealthStore(initialState?.health),
      // uiStore: createUIStore(),
    };
  }

  return (
    <StoreContext.Provider value={storeRef.current}>
      {children}
    </StoreContext.Provider>
  );
}

/**
 * Hook to access the store context
 * Throws an error if used outside of StoreProvider
 */
export function useStoreContext() {
  const context = useContext(StoreContext);

  if (!context) {
    throw new Error('useStoreContext must be used within a StoreProvider');
  }

  return context;
}

/**
 * Factory function for creating store initialization functions
 * This will be used when we create actual stores
 *
 * @example
 * ```ts
 * const createSourcesStore = (initialState?: Partial<SourcesState>) => {
 *   return createStore<SourcesState>()(
 *     devtools(
 *       persist(
 *         (set, get) => ({
 *           ...defaultSourcesState,
 *           ...initialState,
 *           actions: {
 *             // actions here
 *           }
 *         })
 *       )
 *     )
 *   );
 * };
 * ```
 */
export function createStoreInitializer<T>(
  createStoreFn: (initialState?: Partial<T>) => StoreApi<T>
) {
  return (initialState?: Partial<T>) => {
    // This ensures the store is only created once per instance
    const storeRef = useRef<StoreApi<T> | undefined>(undefined);

    if (!storeRef.current) {
      storeRef.current = createStoreFn(initialState);
    }

    return storeRef.current;
  };
}

/**
 * Utility to hydrate stores with server-side data
 * Can be called in Server Components to prepare initial state
 *
 * @example
 * ```tsx
 * // In a Server Component
 * const initialState = await hydrateStores({
 *   sources: await fetchSources(),
 *   boards: await fetchBoards(),
 * });
 *
 * return (
 *   <StoreProvider initialState={initialState}>
 *     {children}
 *   </StoreProvider>
 * );
 * ```
 */
export function hydrateStores(data: {
  sources?: unknown;
  boards?: unknown;
  jobs?: unknown;
  health?: unknown;
}) {
  // This will be implemented when we have actual stores
  // For now, it's a placeholder that returns the data as-is
  return data as StoreProviderProps['initialState'];
}
