'use client';

import type { ColumnDef } from '@tanstack/react-table';
import {
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { ArrowUpDown } from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { DataTableBase } from '@/components/ui/data-table-base';

export type MonitorLogRow = {
  id: string;
  job_id: string | null;
  previous_status: string | null;
  new_status: string | null;
  checked_at: string | null;
  duration_millis: number | null;
  total_tokens: number | null;
  model: string | null;
  head_status: number | null;
  head_ok: boolean | null;
  decision_layer: string | null;
};

const DEFAULT_PAGE_SIZE = 20;

export function MonitorLogsTable({ logs }: { logs: MonitorLogRow[] }) {
  const columns: ColumnDef<MonitorLogRow>[] = [
    {
      accessorKey: 'checked_at',
      header: ({ column }) => (
        <Button
          className="h-8 px-2"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          variant="ghost"
        >
          Checked
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => (
        <div className="text-sm">
          {row.original.checked_at?.slice(0, 19).replace('T', ' ')}
        </div>
      ),
      meta: { className: 'min-w-[180px]' },
    },
    {
      accessorKey: 'job_id',
      header: ({ column }) => (
        <Button
          className="h-8 px-2"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          variant="ghost"
        >
          Job ID
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const jobId = row.original.job_id;
        return jobId ? (
          <Link
            className="font-mono text-blue-600 hover:text-blue-800 hover:underline"
            href={`/dashboard/jobs/${jobId}`}
          >
            {jobId.slice(0, 8)}
          </Link>
        ) : (
          <span className="text-muted-foreground">-</span>
        );
      },
      meta: { className: 'min-w-[120px]' },
    },
    {
      accessorKey: 'previous_status',
      header: ({ column }) => (
        <Button
          className="h-8 px-2"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          variant="ghost"
        >
          Prev
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
    },
    {
      accessorKey: 'new_status',
      header: ({ column }) => (
        <Button
          className="h-8 px-2"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          variant="ghost"
        >
          New
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
    },
    {
      accessorKey: 'duration_millis',
      header: ({ column }) => (
        <Button
          className="h-8 px-2"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          variant="ghost"
        >
          Duration (ms)
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
    },
    {
      accessorKey: 'total_tokens',
      header: ({ column }) => (
        <Button
          className="h-8 px-2"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          variant="ghost"
        >
          Tokens
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
    },
    {
      accessorKey: 'model',
      header: ({ column }) => (
        <Button
          className="h-8 px-2"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          variant="ghost"
        >
          Model
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
    },
    {
      accessorKey: 'decision_layer',
      header: ({ column }) => (
        <Button
          className="h-8 px-2"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          variant="ghost"
        >
          Decision
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
    },
    {
      id: 'http',
      header: ({ column }) => (
        <Button
          className="h-8 px-2"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          variant="ghost"
        >
          HTTP
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => (
        <div className="text-sm">
          {row.original.head_ok ? '✅' : '❌'} {row.original.head_status}
        </div>
      ),
      meta: { className: 'min-w-[100px]' },
    },
  ];

  const table = useReactTable({
    data: logs,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    initialState: {
      pagination: { pageIndex: 0, pageSize: DEFAULT_PAGE_SIZE },
      sorting: [{ id: 'checked_at', desc: true }],
    },
  });

  return (
    <div className="rounded-md border p-4">
      <DataTableBase
        filterColumn="model"
        filterPlaceholder="Filter logs..."
        showPagination
        showSelection={false}
        showViewOptions
        table={table}
      />
    </div>
  );
}
