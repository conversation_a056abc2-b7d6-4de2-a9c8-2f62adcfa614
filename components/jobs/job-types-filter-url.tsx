'use client';

import { MultiSelectUrlFilter } from '@/components/common/MultiSelectUrlFilter';
import { JOB_TYPES } from '@/lib/job-types';

type JobTypesFilterUrlProps = {
  disabled?: boolean;
};

/**
 * Job Types filter component with URL state
 * Now uses the generic MultiSelectUrlFilter component
 */
export function JobTypesFilterUrl({
  disabled = false,
}: JobTypesFilterUrlProps) {
  return (
    <MultiSelectUrlFilter
      clearable={false}
      disabled={disabled}
      label="Job Types"
      options={JOB_TYPES}
      paramName="types"
      searchable={false}
      showCount={true}
    />
  );
}
