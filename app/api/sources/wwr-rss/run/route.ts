import { type NextRequest, NextResponse } from 'next/server';
import { handleApiError } from '@/lib/api-utils';
import { getApifyService } from '@/lib/services/ApifyService';

export async function POST(_request: NextRequest): Promise<NextResponse> {
  try {
    const actorId = process.env.WWR_ACTOR_ID;

    if (!actorId) {
      return NextResponse.json(
        { error: 'WeWorkRemotely actor ID not configured' },
        { status: 500 }
      );
    }

    const apifyService = getApifyService();

    // Trigger the run using schedule input
    const runData = await apifyService.triggerRun(actorId);

    return NextResponse.json({
      success: true,
      message: 'WeWorkRemotely RSS run triggered successfully',
      data: {
        runId: runData.id,
        status: runData.status,
        apifyUrl: `https://console.apify.com/actors/${actorId}/runs/${runData.id}`,
      },
    });
  } catch (error) {
    return handleApiError(error, 'WeWorkRemotely RSS run trigger');
  }
}
