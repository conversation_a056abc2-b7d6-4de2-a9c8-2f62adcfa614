/**
 * Boards Store - Manages job board configurations and operations
 */

import { useEffect, useState } from 'react';
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import type { JobBoard, LoadingState } from './types';

/**
 * Extended JobBoard interface with additional properties
 */
export interface ExtendedJobBoard extends JobBoard {
  enabled?: boolean;
  posting?: {
    dailyLimit: number;
    strategy: string;
    avoidRepostingDays: number;
    postingTimes?: string[];
    timezone?: string;
  };
  filters?: {
    types?: string[];
    workplaceTypes?: string[];
    remoteRegions?: string[];
    countries?: string[];
    careerLevels?: string[];
    salaryMin?: number;
    salaryMax?: number;
    salaryCurrencies?: string[];
    includeKeywords?: string[];
    excludeKeywords?: string[];
    visaSponsorship?: string;
    languages?: string[];
    sourcePriority?: string[];
  };
  totalPosted?: number;
  lastPostedAt?: string;
  airtableRecordCount?: number;
  airtableLastFetchedAt?: string;
}

/**
 * PAT Configuration Result
 */
type PATConfigResult = {
  success: boolean;
  error?: string;
  boardId?: string;
};

/**
 * Airtable Send Result
 */
type AirtableSendResult = {
  success: boolean;
  error?: string;
  recordId?: string;
  recordUrl?: string;
};

/**
 * Boards State Interface
 */
export interface BoardsState extends LoadingState {
  // Data
  boards: ExtendedJobBoard[];
  availablePats: string[];
  lastFetch: Date | null;

  // Actions
  actions: {
    // Fetch all boards
    fetch: () => Promise<void>;

    // Create a new board
    createBoard: (
      board: Partial<ExtendedJobBoard>
    ) => Promise<{ success: boolean; error?: string }>;

    // Update a board
    updateBoard: (
      id: string,
      updates: Partial<ExtendedJobBoard>
    ) => Promise<{ success: boolean; error?: string }>;

    // Delete a board
    deleteBoard: (id: string) => Promise<{ success: boolean; error?: string }>;

    // Configure PAT for a board
    configurePAT: (boardId: string, pat: string) => Promise<PATConfigResult>;

    // Send job to Airtable
    sendToAirtable: (
      boardId: string,
      jobData: Record<string, unknown>
    ) => Promise<AirtableSendResult>;

    // Test Airtable connection
    testConnection: (
      boardId: string
    ) => Promise<{ success: boolean; error?: string }>;

    // Clear error
    clearError: () => void;

    // Reset store
    reset: () => void;
  };
}

/**
 * Initial state
 */
const initialState: Omit<BoardsState, 'actions'> = {
  boards: [],
  availablePats: [],
  loading: false,
  error: null,
  lastFetch: null,
};

/**
 * Boards Store with Immer for easier state updates
 */
export const useBoardsStore = create<BoardsState>()(
  devtools(
    immer((set, get) => ({
      ...initialState,

      actions: {
        // Fetch all boards
        fetch: async () => {
          set((state) => {
            state.loading = true;
            state.error = null;
          });

          try {
            // Fetch boards
            const boardsResponse = await fetch('/api/job-boards');
            const boardsData = await boardsResponse.json();

            if (!(boardsResponse.ok && boardsData.success)) {
              throw new Error(boardsData.error || 'Failed to fetch boards');
            }

            // Fetch available PATs
            const patsResponse = await fetch('/api/airtable-secrets');
            const patsData = await patsResponse.json();

            const availablePats = patsData.success
              ? patsData.availablePats || []
              : [];

            set((state) => {
              state.boards = boardsData.boards || [];
              state.availablePats = availablePats;
              state.loading = false;
              state.lastFetch = new Date();
            });
          } catch (error) {
            set((state) => {
              state.loading = false;
              state.error =
                error instanceof Error ? error.message : 'Unknown error';
            });
          }
        },

        // Create a new board
        createBoard: async (board) => {
          set((state) => {
            state.loading = true;
            state.error = null;
          });

          try {
            const response = await fetch('/api/job-boards', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(board),
            });

            const data = await response.json();

            if (!(response.ok && data.success)) {
              throw new Error(data.error || 'Failed to create board');
            }

            // Refresh boards list
            await get().actions.fetch();

            return { success: true };
          } catch (error) {
            const errorMessage =
              error instanceof Error ? error.message : 'Unknown error';
            set((state) => {
              state.loading = false;
              state.error = errorMessage;
            });
            return { success: false, error: errorMessage };
          }
        },

        // Update a board
        updateBoard: async (id, updates) => {
          // Optimistic update
          set((state) => {
            const index = state.boards.findIndex((b) => b.id === id);
            if (index !== -1) {
              state.boards[index] = { ...state.boards[index], ...updates };
            }
          });

          try {
            const response = await fetch(`/api/job-boards?id=${id}`, {
              method: 'PATCH',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(updates),
            });

            const data = await response.json();

            if (!(response.ok && data.success)) {
              // Revert on error
              await get().actions.fetch();
              throw new Error(data.error || 'Failed to update board');
            }

            return { success: true };
          } catch (error) {
            const errorMessage =
              error instanceof Error ? error.message : 'Unknown error';
            set((state) => {
              state.error = errorMessage;
            });
            return { success: false, error: errorMessage };
          }
        },

        // Delete a board
        deleteBoard: async (id) => {
          // Optimistic update
          const originalBoards = get().boards;
          set((state) => {
            state.boards = state.boards.filter((b) => b.id !== id);
          });

          try {
            const response = await fetch(`/api/job-boards?id=${id}`, {
              method: 'DELETE',
            });

            const data = await response.json();

            if (!(response.ok && data.success)) {
              // Revert on error
              set((state) => {
                state.boards = originalBoards;
              });
              throw new Error(data.error || 'Failed to delete board');
            }

            return { success: true };
          } catch (error) {
            const errorMessage =
              error instanceof Error ? error.message : 'Unknown error';
            set((state) => {
              state.error = errorMessage;
            });
            return { success: false, error: errorMessage };
          }
        },

        // Configure PAT for a board
        configurePAT: async (boardId, pat) => {
          try {
            const response = await fetch('/api/airtable-secrets', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ boardId, pat }),
            });

            const data = await response.json();

            if (!(response.ok && data.success)) {
              throw new Error(data.error || 'Failed to configure PAT');
            }

            // Update board PAT status
            set((state) => {
              const board = state.boards.find((b) => b.id === boardId);
              if (board) {
                board.patStatus = 'configured';
              }
              if (!state.availablePats.includes(boardId)) {
                state.availablePats.push(boardId);
              }
            });

            return { success: true, boardId };
          } catch (error) {
            const errorMessage =
              error instanceof Error ? error.message : 'Unknown error';
            return { success: false, error: errorMessage };
          }
        },

        // Send job to Airtable
        sendToAirtable: async (boardId, jobData) => {
          const board = get().boards.find((b) => b.id === boardId);
          if (!board) {
            return { success: false, error: 'Board not found' };
          }

          // Optimistic update
          set((state) => {
            const boardToUpdate = state.boards.find((b) => b.id === boardId);
            if (boardToUpdate) {
              boardToUpdate.lastSync = new Date().toISOString();
            }
          });

          try {
            const response = await fetch('/api/airtable-send', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                boardId,
                jobData,
                airtableConfig: board.airtable,
              }),
            });

            const data = await response.json();

            if (!(response.ok && data.success)) {
              // Revert optimistic update
              set((state) => {
                const boardToUpdate = state.boards.find(
                  (b) => b.id === boardId
                );
                if (boardToUpdate) {
                  boardToUpdate.lastSync = board.lastSync;
                }
              });
              throw new Error(data.error || 'Failed to send to Airtable');
            }

            // Update posted count
            set((state) => {
              const boardToUpdate = state.boards.find((b) => b.id === boardId);
              if (boardToUpdate) {
                boardToUpdate.totalPosted =
                  (boardToUpdate.totalPosted || 0) + 1;
                boardToUpdate.lastPostedAt = new Date().toISOString();
              }
            });

            return {
              success: true,
              recordId: data.recordId,
              recordUrl: data.recordUrl,
            };
          } catch (error) {
            const errorMessage =
              error instanceof Error ? error.message : 'Unknown error';
            return { success: false, error: errorMessage };
          }
        },

        // Test Airtable connection
        testConnection: async (boardId) => {
          const board = get().boards.find((b) => b.id === boardId);
          if (!board) {
            return { success: false, error: 'Board not found' };
          }

          try {
            const response = await fetch('/api/airtable-test', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                boardId,
                baseId: board.airtable.baseId,
                tableName: board.airtable.tableName,
              }),
            });

            const data = await response.json();

            // Update PAT status based on result
            set((state) => {
              const boardToUpdate = state.boards.find((b) => b.id === boardId);
              if (boardToUpdate) {
                boardToUpdate.patStatus = data.success
                  ? 'configured'
                  : 'invalid';
              }
            });

            return {
              success: data.success,
              error: data.error,
            };
          } catch (error) {
            const errorMessage =
              error instanceof Error ? error.message : 'Unknown error';
            return { success: false, error: errorMessage };
          }
        },

        // Clear error
        clearError: () => {
          set((state) => {
            state.error = null;
          });
        },

        // Reset store
        reset: () => {
          set(initialState);
        },
      },
    })),
    { name: 'BoardsStore' }
  )
);

/**
 * Hydrated hook for SSR-safe usage
 */
export const useBoardsStoreHydrated = () => {
  const store = useBoardsStore();
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    setIsHydrated(true);
  }, []);

  if (!isHydrated) {
    return {
      boards: [],
      availablePats: [],
      loading: false,
      error: null,
      lastFetch: null,
      actions: {
        fetch: async () => {
          // No-op during hydration
        },
        createBoard: async () => ({ success: false, error: 'Not hydrated' }),
        updateBoard: async () => ({ success: false, error: 'Not hydrated' }),
        deleteBoard: async () => ({ success: false, error: 'Not hydrated' }),
        configurePAT: async () => ({ success: false, error: 'Not hydrated' }),
        sendToAirtable: async () => ({ success: false, error: 'Not hydrated' }),
        testConnection: async () => ({ success: false, error: 'Not hydrated' }),
        clearError: () => {
          // No-op during hydration
        },
        reset: () => {
          // No-op during hydration
        },
      },
    } as BoardsState;
  }

  return store;
};
