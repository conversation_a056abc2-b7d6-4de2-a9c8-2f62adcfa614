/**
 * Database Client Factory
 * 
 * Provides a centralized, consistent way to create and manage database clients
 * with proper error handling, validation, and connection pooling.
 */

import {
  createBrowserClient,
  createServerClient as createSupabaseServerClient,
} from '@supabase/ssr';
import { createClient as createSupabaseClient, type SupabaseClient } from '@supabase/supabase-js';
import { logger } from '@/lib/logger';

// Database client types
export type DatabaseClient = SupabaseClient;
export type AdminDatabaseClient = SupabaseClient;

// Configuration interface
export interface DatabaseConfig {
  url: string;
  anonKey: string;
  serviceRoleKey?: string;
}

// Client creation options
export interface ClientOptions {
  timeout?: number;
  retries?: number;
  cache?: boolean;
  debug?: boolean;
}

// Environment validation
function validateEnvironment(): DatabaseConfig {
  const url = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const anonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!url) {
    throw new Error('NEXT_PUBLIC_SUPABASE_URL is required');
  }

  if (!anonKey) {
    throw new Error('NEXT_PUBLIC_SUPABASE_ANON_KEY is required');
  }

  // Validate URL format
  try {
    new URL(url);
  } catch {
    throw new Error('NEXT_PUBLIC_SUPABASE_URL must be a valid URL');
  }

  return {
    url,
    anonKey,
    serviceRoleKey,
  };
}

// Client cache for reuse
const clientCache = new Map<string, DatabaseClient>();

/**
 * Create a browser-side Supabase client (for Client Components)
 */
export function createClient(options: ClientOptions = {}): DatabaseClient {
  const { debug = false, cache = true } = options;
  const cacheKey = 'browser-client';

  // Return cached client if available
  if (cache && clientCache.has(cacheKey)) {
    const cachedClient = clientCache.get(cacheKey)!;
    if (debug) {
      logger.debug('Using cached browser client');
    }
    return cachedClient;
  }

  try {
    const config = validateEnvironment();
    
    if (debug) {
      logger.debug('Creating browser client', {
        url: config.url,
        hasAnonKey: !!config.anonKey,
      });
    }

    const client = createBrowserClient(config.url, config.anonKey);

    // Cache the client
    if (cache) {
      clientCache.set(cacheKey, client);
    }

    return client;
  } catch (error) {
    logger.error('Failed to create browser client', { error });
    throw new Error(`Database client creation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Create a server-side Supabase client (for Server Components and API routes)
 */
export async function createServerClient(options: ClientOptions = {}): Promise<DatabaseClient> {
  const { debug = false } = options;

  try {
    const { cookies } = await import('next/headers');
    const config = validateEnvironment();

    if (debug) {
      logger.debug('Creating server client', {
        url: config.url,
        hasAnonKey: !!config.anonKey,
      });
    }

    const cookieStore = await cookies();

    const client = createSupabaseServerClient(config.url, config.anonKey, {
      cookies: {
        getAll() {
          return cookieStore.getAll();
        },
        setAll(cookiesToSet) {
          try {
            for (const { name, value, options } of cookiesToSet) {
              cookieStore.set(name, value, options);
            }
          } catch {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing user sessions.
            if (debug) {
              logger.debug('Cookie setting ignored in Server Component context');
            }
          }
        },
      },
    });

    return client;
  } catch (error) {
    logger.error('Failed to create server client', { error });
    throw new Error(`Server database client creation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Create a service role client (for admin operations that bypass RLS)
 */
export function createServiceRoleClient(options: ClientOptions = {}): AdminDatabaseClient {
  const { debug = false, cache = true } = options;
  const cacheKey = 'service-role-client';

  // Return cached client if available
  if (cache && clientCache.has(cacheKey)) {
    const cachedClient = clientCache.get(cacheKey)!;
    if (debug) {
      logger.debug('Using cached service role client');
    }
    return cachedClient;
  }

  try {
    const config = validateEnvironment();

    if (!config.serviceRoleKey) {
      throw new Error('SUPABASE_SERVICE_ROLE_KEY is required for admin operations');
    }

    if (debug) {
      logger.debug('Creating service role client', {
        url: config.url,
        hasServiceKey: !!config.serviceRoleKey,
      });
    }

    const client = createSupabaseClient(config.url, config.serviceRoleKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
    });

    // Cache the client
    if (cache) {
      clientCache.set(cacheKey, client);
    }

    return client;
  } catch (error) {
    logger.error('Failed to create service role client', { error });
    throw new Error(`Admin database client creation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Create an admin client with enhanced configuration
 */
export function createAdminClient(options: ClientOptions = {}): AdminDatabaseClient {
  return createServiceRoleClient(options);
}

/**
 * Test database connection
 */
export async function testConnection(client: DatabaseClient): Promise<{
  success: boolean;
  error?: string;
  latency?: number;
}> {
  const startTime = Date.now();
  
  try {
    // Simple query to test connection
    const { error } = await client
      .from('jobs')
      .select('id')
      .limit(1)
      .single();

    const latency = Date.now() - startTime;

    if (error && error.code !== 'PGRST116') { // PGRST116 is "no rows returned" which is fine
      return {
        success: false,
        error: error.message,
        latency,
      };
    }

    return {
      success: true,
      latency,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      latency: Date.now() - startTime,
    };
  }
}

/**
 * Health check for database connectivity
 */
export async function checkDatabaseHealth(): Promise<{
  status: 'healthy' | 'degraded' | 'unhealthy';
  details: {
    client: { success: boolean; latency?: number; error?: string };
    admin: { success: boolean; latency?: number; error?: string };
  };
}> {
  try {
    // Test regular client
    const client = createClient({ cache: false });
    const clientTest = await testConnection(client);

    // Test admin client
    const adminClient = createServiceRoleClient({ cache: false });
    const adminTest = await testConnection(adminClient);

    // Determine overall status
    let status: 'healthy' | 'degraded' | 'unhealthy';
    
    if (clientTest.success && adminTest.success) {
      status = 'healthy';
    } else if (clientTest.success || adminTest.success) {
      status = 'degraded';
    } else {
      status = 'unhealthy';
    }

    return {
      status,
      details: {
        client: clientTest,
        admin: adminTest,
      },
    };
  } catch (error) {
    logger.error('Database health check failed', { error });
    return {
      status: 'unhealthy',
      details: {
        client: { success: false, error: 'Health check failed' },
        admin: { success: false, error: 'Health check failed' },
      },
    };
  }
}

/**
 * Clear client cache (useful for testing or resetting connections)
 */
export function clearClientCache(): void {
  clientCache.clear();
  logger.debug('Database client cache cleared');
}

/**
 * Get cache statistics
 */
export function getCacheStats(): {
  size: number;
  keys: string[];
} {
  return {
    size: clientCache.size,
    keys: Array.from(clientCache.keys()),
  };
}

// Legacy compatibility - re-export for backwards compatibility
export { createClient as createBrowserClient };
export { createServerClient as createSupabaseServerClient };
export { createServiceRoleClient as createSupabaseServiceRoleClient };

// Default client instance for convenience (but prefer factory functions)
let defaultClient: DatabaseClient | null = null;

export function getDefaultClient(): DatabaseClient {
  if (!defaultClient) {
    defaultClient = createClient();
  }
  return defaultClient;
}