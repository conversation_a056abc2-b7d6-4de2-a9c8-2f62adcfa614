/**
 * Legacy Supabase Client Module
 *
 * This file is maintained for backwards compatibility.
 * New code should use the database client factory: lib/database/
 *
 * @deprecated Use lib/database/ module instead
 */

// Re-export everything from the new database client factory
export {
  type AdminDatabaseClient,
  checkDatabaseHealth,
  createAdminClient,
  createClient,
  createServerClient,
  createServiceRoleClient,
  type DatabaseClient,
  getDefaultClient,
  testConnection,
} from './database/client-factory';

// Legacy convenience export for the browser client
import { getDefaultClient } from './database/client-factory';
export const supabase = getDefaultClient();
