import { type NextRequest, NextResponse } from 'next/server';
import { handleApiError } from '@/lib/api-utils';
import {
  monitorBatch,
  persistMonitorResults,
  selectJobsForMonitoring,
} from '@/lib/monitor-worker';
import { logger } from '@/lib/utils';

export const dynamic = 'force-dynamic';

const SECRET = process.env.MONITOR_SECRET;

export async function POST(req: NextRequest) {
  const url = new URL(req.url);
  const key = url.searchParams.get('key');

  if (!SECRET || key !== SECRET) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const limit = Number(url.searchParams.get('limit') || '20');

  try {
    const jobs = await selectJobsForMonitoring({ limit });
    if (jobs.length === 0) {
      logger.pipeline({
        step: 'MONITORED',
        source: 'monitor',
        jobCount: 0,
        success: true,
        metadata: { message: 'No jobs ready for monitoring' },
      });
      return NextResponse.json({ processed: 0, message: 'No jobs ready' });
    }

    const results = await monitorBatch(jobs);
    await persistMonitorResults(results);

    // Track successful monitoring
    logger.pipeline({
      step: 'MONITORED',
      source: 'monitor',
      jobCount: results.length,
      success: true,
      metadata: {
        jobsMonitored: results.length,
        jobsSelected: jobs.length,
      },
    });

    return NextResponse.json({ processed: results.length });
  } catch (error) {
    logger.error('Monitor API error', error);

    // Track monitoring failure
    logger.pipeline({
      step: 'MONITORED',
      source: 'monitor',
      jobCount: 0,
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      metadata: { limit },
    });

    return handleApiError(error, 'Monitor processing');
  }
}
