{"name": "bordfeed", "version": "0.1.7", "description": "AI-powered job board automation platform with intelligent extraction, monitoring, and multi-platform publishing", "private": true, "packageManager": "bun@1.1.0", "engines": {"bun": ">=1.1.0", "node": ">=18"}, "scripts": {"dev": "next dev --turbopack", "prebuild": "bun run typecheck", "build": "next build", "start": "next start", "lint": "bunx ultracite lint", "format": "bunx ultracite format", "typecheck": "tsc -p tsconfig.json --noEmit", "test": "bunx tsx tests/scripts/run-all-tests.ts", "test:unit": "bunx tsx tests/scripts/run-unit-tests.ts", "test:api": "bunx tsx tests/scripts/run-api-tests.ts", "test:integration": "bunx tsx tests/scripts/run-integration-tests.ts", "test:e2e": "bunx playwright test", "test:schema": "bunx tsx tests/utils/validate-openapi.ts", "test:watch": "bunx tsx --watch tests/scripts/run-unit-tests.ts", "env:pull": "bunx vercel env pull", "env:push": "bunx vercel env push", "deploy": "bunx vercel --prod", "postinstall": "echo 'skip fumadocs-mdx (disabled for local dev)'"}, "dependencies": {"@ai-sdk/openai": "^2.0.19", "@fastify/deepmerge": "^3.1.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-tooltip": "^1.2.8", "@scalar/api-client-react": "^1.3.27", "@scalar/api-reference-react": "^0.7.38", "@scalar/nextjs-api-reference": "^0.8.14", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.55.0", "@tanstack/react-table": "^8.21.3", "@types/mdx": "^2.0.13", "@upstash/workflow": "^0.2.17", "ai": "^5.0.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^17.2.1", "fumadocs-core": "^15.7.0", "fumadocs-mdx": "^11.8.0", "fumadocs-openapi": "^9.2.1", "fumadocs-ui": "^15.7.0", "lucide-react": "^0.540.0", "next": "^15.5.0", "next-themes": "0.4.6", "nuqs": "^2.4.3", "openai": "^5.13.1", "openapi-fetch": "^0.14.0", "react": "^19.1.1", "react-dom": "^19.1.1", "recharts": "^3.1.2", "shiki": "^3.11.0", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "use-debounce": "^10.0.5", "vercel": "^46.0.2", "zod": "^4.0.17", "zustand": "^5.0.8"}, "devDependencies": {"@biomejs/biome": "^2.2.0", "@faker-js/faker": "^9.9.0", "@playwright/test": "^1.55.0", "@redux-devtools/extension": "^3.3.0", "@tailwindcss/postcss": "^4.1.12", "@types/node": "^24.3.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "eslint-config-next": "^15.5.0", "openapi-typescript": "^7.9.1", "tailwindcss": "^4.1.12", "tsx": "^4.20.4", "tw-animate-css": "^1.3.7", "typescript": "^5.9.2", "ultracite": "^5.2.5", "vitest": "^3.2.4"}}