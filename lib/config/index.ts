/**
 * Centralized Configuration Index
 * 
 * Single entry point for all configuration settings across the application.
 * This provides a clean API for accessing all configuration values.
 */

// Import functions for internal use
import { getAIServiceStatus } from './ai-config';
import { getEnvironmentInfo } from './environment-config';
import { getNotificationStatus } from './notification-config';

// AI Configuration
export {
  AI_CONFIG,
  INPUT_LIMITS,
  SCHEMA_LIMITS,
  validateAIConfig,
  getAIServiceStatus,
} from './ai-config';

// Environment Configuration
export {
  DEBUG_MODE,
  DEFAULTS,
  ENV_STATUS,
  UPSTASH_CONFIG,
  envFlag,
  getEnvironmentInfo,
} from './environment-config';

// Notification Configuration
export {
  SLACK_CONFIG,
  SLACK_FLAGS,
  NOTIFICATION_LEVELS,
  type NotificationLevel,
  isNotificationEnabled,
  validateNotificationConfig,
  getNotificationStatus,
} from './notification-config';

// Job Constants
export {
  // Job types and categories
  CAREER_LEVELS,
  JOB_TYPES,
  WORK<PERSON>ACE_TYPES,
  REMOTE_REGIONS,
  VISA_SPONSORSHIP_OPTIONS,
  CURRENCY_CODES,
  
  // Job sources and posting
  POSTING_STRATEGIES,
  type PostingStrategy,
  POSTING_STRATEGY_DESCRIPTIONS,
  JOB_SOURCES,
  type JobSource,
  JOB_SOURCE_DESCRIPTIONS,
  getSourceDisplayName,
  
  // Job board configuration
  type JobBoardConfig,
  DEFAULT_JOB_BOARD_CONFIG,
  
  // Validation functions
  isValidJobSource,
  isValidPostingStrategy,
  getJobFilterOptions,
} from './job-constants';

// Configuration validation
export interface SystemConfigStatus {
  ai: ReturnType<typeof getAIServiceStatus>;
  environment: ReturnType<typeof getEnvironmentInfo>;
  notifications: ReturnType<typeof getNotificationStatus>;
  overall: {
    healthy: boolean;
    errors: string[];
    warnings: string[];
  };
}

/**
 * Get comprehensive system configuration status
 */
export function getSystemConfigStatus(): SystemConfigStatus {
  const ai = getAIServiceStatus();
  const environment = getEnvironmentInfo();
  const notifications = getNotificationStatus();
  
  // Collect all errors and warnings
  const errors: string[] = [
    ...ai.errors,
    ...notifications.errors,
  ];
  
  const warnings: string[] = [
    ...notifications.warnings,
  ];

  // Add environment warnings
  if (environment.missingVars.length > 0) {
    warnings.push(`Missing environment variables: ${environment.missingVars.join(', ')}`);
  }

  const overall = {
    healthy: errors.length === 0 && environment.isConfigured,
    errors,
    warnings,
  };

  return {
    ai,
    environment,
    notifications,
    overall,
  };
}

/**
 * Validate that all critical systems are configured
 */
export function validateSystemConfiguration(): {
  isValid: boolean;
  criticalIssues: string[];
  recommendations: string[];
} {
  const status = getSystemConfigStatus();
  const criticalIssues: string[] = [];
  const recommendations: string[] = [];

  // Check AI configuration
  if (!status.ai.configured) {
    criticalIssues.push('AI service not configured properly');
  }

  // Check environment
  if (!status.environment.isConfigured) {
    criticalIssues.push('Required environment variables missing');
  }

  // Check notifications for production
  if (status.environment.nodeEnv === 'production' && !status.notifications.configured) {
    criticalIssues.push('Notification system not configured for production');
  }

  // Add recommendations based on warnings
  if (status.overall.warnings.length > 0) {
    recommendations.push(...status.overall.warnings);
  }

  return {
    isValid: criticalIssues.length === 0,
    criticalIssues,
    recommendations,
  };
}