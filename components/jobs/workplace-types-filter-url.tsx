'use client';

import { MultiSelectUrlFilter } from '@/components/common/MultiSelectUrlFilter';
import { WORKPLACE_TYPES } from '@/lib/workplace';

type WorkplaceTypesFilterUrlProps = {
  disabled?: boolean;
};

/**
 * Workplace Types filter component with URL state
 * Now uses the generic MultiSelectUrlFilter component
 */
export function WorkplaceTypesFilterUrl({
  disabled = false,
}: WorkplaceTypesFilterUrlProps) {
  return (
    <MultiSelectUrlFilter
      clearable={false}
      disabled={disabled}
      label="Workplace Types"
      options={WORKPLACE_TYPES}
      paramName="workplace"
      searchable={false}
      showCount={true}
    />
  );
}
