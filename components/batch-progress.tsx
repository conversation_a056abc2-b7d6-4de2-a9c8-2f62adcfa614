'use client';

import type React from 'react';
import { useMemo } from 'react';
import { Progress } from './ui/progress';

export type BatchProgressProps = {
  /** Total number of items to process */
  total: number;
  /** Number of items completed */
  completed: number;
  /** Number of items that failed */
  failed?: number;
  /** Number of duplicates skipped */
  duplicated?: number;
  /** Current status */
  status: 'idle' | 'running' | 'completed' | 'error';
  /** Start time of the batch operation */
  startTime?: Date;
  /** Title of the batch operation */
  title?: string;
  /** Additional CSS classes */
  className?: string;
  /** Whether to show detailed breakdown */
  showDetails?: boolean;
};

const BatchProgress: React.FC<BatchProgressProps> = ({
  total,
  completed,
  failed = 0,
  duplicated = 0,
  status,
  startTime,
  title = 'Batch Processing',
  className = '',
  showDetails = true,
}) => {
  // Calculate ETA based on current progress rate
  const etaMs = useMemo(() => {
    if (!startTime || completed === 0 || status !== 'running') {
      return;
    }

    const elapsedMs = Date.now() - startTime.getTime();
    const avgTimePerItem = elapsedMs / completed;
    const remaining = total - completed;

    return Math.round(avgTimePerItem * remaining);
  }, [startTime, completed, total, status]);

  // Calculate processing rate
  const processingRate = useMemo(() => {
    if (!startTime || completed === 0) {
      return null;
    }

    const elapsedSeconds = (Date.now() - startTime.getTime()) / 1000;
    if (elapsedSeconds === 0) {
      return null;
    }

    const rate = completed / elapsedSeconds;
    return rate < 1
      ? `${(rate * 60).toFixed(1)} items/min`
      : `${rate.toFixed(1)} items/sec`;
  }, [startTime, completed]);

  // Get status-specific messaging
  const getStatusMessage = () => {
    switch (status) {
      case 'completed':
        return failed > 0
          ? 'Completed with errors'
          : 'All items processed successfully';
      case 'error':
        return 'Batch processing failed';
      case 'running':
        return 'Processing...';
      default:
        return 'Ready to start';
    }
  };

  // Determine overall status for progress bar
  const _progressStatus = useMemo(() => {
    if (status === 'error') {
      return 'error';
    }
    if (status === 'completed') {
      return failed > 0 ? 'error' : 'completed';
    }
    return status;
  }, [status, failed]);

  const successful = completed - failed - duplicated;

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="font-semibold text-gray-900 text-lg">{title}</h3>
        {processingRate && status === 'running' && (
          <span className="text-gray-500 text-sm">{processingRate}</span>
        )}
      </div>

      {/* Progress Bar */}
      <div className="space-y-2">
        <div className="flex justify-between text-sm">
          <span>{getStatusMessage()}</span>
          <span>
            {completed} / {total}
          </span>
        </div>
        <Progress value={(completed / total) * 100} />
        {etaMs && (
          <div className="text-right text-gray-500 text-xs">
            ETA: {Math.round(etaMs / 1000)}s
          </div>
        )}
      </div>

      {/* Detailed Breakdown */}
      {showDetails && (completed > 0 || status === 'completed') && (
        <div className="grid grid-cols-2 gap-4 rounded-lg bg-gray-50 p-4 text-sm sm:grid-cols-4">
          <div>
            <p className="text-gray-500">Total Items</p>
            <p className="font-semibold text-gray-900 text-lg">
              {total.toLocaleString()}
            </p>
          </div>

          <div>
            <p className="text-gray-500">Successful</p>
            <p className="font-semibold text-green-600 text-lg">
              {successful.toLocaleString()}
            </p>
          </div>

          {duplicated > 0 && (
            <div>
              <p className="text-gray-500">Duplicates</p>
              <p className="font-semibold text-lg text-yellow-600">
                {duplicated.toLocaleString()}
              </p>
            </div>
          )}

          {failed > 0 && (
            <div>
              <p className="text-gray-500">Failed</p>
              <p className="font-semibold text-lg text-red-600">
                {failed.toLocaleString()}
              </p>
            </div>
          )}
        </div>
      )}

      {/* Status Message */}
      <div className="flex items-center gap-2 text-sm">
        {status === 'running' && (
          <div className="flex items-center gap-2">
            <div className="h-2 w-2 animate-pulse rounded-full bg-blue-600" />
            <span className="text-gray-700">{getStatusMessage()}</span>
          </div>
        )}

        {status === 'completed' && (
          <div className="flex items-center gap-2">
            {failed === 0 ? (
              <>
                <svg
                  className="h-4 w-4 text-green-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <title>Success</title>
                  <path
                    clipRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                    fillRule="evenodd"
                  />
                </svg>
                <span className="text-green-700">{getStatusMessage()}</span>
              </>
            ) : (
              <>
                <svg
                  className="h-4 w-4 text-yellow-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <title>Warning</title>
                  <path
                    clipRule="evenodd"
                    d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                    fillRule="evenodd"
                  />
                </svg>
                <span className="text-yellow-700">{getStatusMessage()}</span>
              </>
            )}
          </div>
        )}

        {status === 'error' && (
          <div className="flex items-center gap-2">
            <svg
              className="h-4 w-4 text-red-600"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <title>Error</title>
              <path
                clipRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                fillRule="evenodd"
              />
            </svg>
            <span className="text-red-700">{getStatusMessage()}</span>
          </div>
        )}
      </div>

      {/* Elapsed Time */}
      {startTime && (status === 'completed' || status === 'error') && (
        <p className="text-gray-500 text-xs">
          Completed in {Math.round((Date.now() - startTime.getTime()) / 1000)}s
        </p>
      )}
    </div>
  );
};

export default BatchProgress;
