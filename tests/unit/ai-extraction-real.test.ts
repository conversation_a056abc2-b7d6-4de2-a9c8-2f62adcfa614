import assert from 'node:assert/strict';
import test from 'node:test';

// Import actual production code
import { JobExtractionSchema, JobSchema } from '../../lib/job-schema.js';
import {
  JOB_EXTRACTION_PROMPT,
  JOB_STATUS_CLASSIFIER_PROMPT,
} from '../../lib/prompts.js';

// Test implementation of replaceTemplateVariables to avoid dependencies
function testReplaceTemplateVariables(
  template: string,
  variables: Record<string, string>
): string {
  let result = template;
  for (const [key, value] of Object.entries(variables)) {
    result = result.replace(new RegExp(`\\{${key}\\}`, 'g'), value);
  }
  return result;
}

import { AI_CONFIG, SCHEMA_LIMITS } from '../../lib/constants.js';
import { estimateReadingTime } from '../../lib/text-utils.js';

// Test implementation of generateMetadata to avoid Supabase dependencies
function testGenerateMetadata(
  startTime: number,
  usage:
    | { inputTokens?: number; outputTokens?: number; totalTokens?: number }
    | undefined,
  additionalData?: Record<string, unknown>
) {
  const duration = Date.now() - startTime;
  return {
    duration,
    inputTokens: usage?.inputTokens || 0,
    outputTokens: usage?.outputTokens || 0,
    totalTokens: usage?.totalTokens || 0,
    model: additionalData?.model || 'gpt-4o-mini',
    ...additionalData,
  };
}

import { APPLY_METHODS } from '../../lib/apply-methods.js';
import { CAREER_LEVELS } from '../../lib/career-levels.js';
import {
  isJobExpired,
  JOB_STATUSES,
  type JobStatus,
} from '../../lib/job-status.js';
import { JOB_TYPES } from '../../lib/job-types.js';
import { WORKPLACE_TYPES } from '../../lib/workplace.js';

// Test implementations of internal functions (based on actual production logic)
function testApplyFallbackLogic(
  extractedJob: { apply_url?: string | null },
  sourceUrl: string
) {
  if (!extractedJob.apply_url || extractedJob.apply_url.trim() === '') {
    extractedJob.apply_url = sourceUrl;
  }
}

function testApplyStatusLogic(extractedJob: {
  status?: JobStatus | null;
  valid_through?: string | null;
}) {
  // Set default status to active if not provided
  if (!extractedJob.status) {
    extractedJob.status = 'active';
  }

  // Check if job is expired and update status
  if (extractedJob.valid_through && isJobExpired(extractedJob.valid_through)) {
    extractedJob.status = 'expired';
  }
}

// Test data using real job content examples
const realJobContent = `
Senior Software Engineer - Remote
TechCorp Inc.

We are seeking a Senior Software Engineer to join our growing engineering team. This is a full-time remote position with competitive compensation.

Responsibilities:
- Design and develop scalable web applications
- Collaborate with cross-functional teams
- Mentor junior developers
- Participate in code reviews

Requirements:
- 5+ years of software development experience
- Proficiency in JavaScript, TypeScript, React
- Experience with Node.js and PostgreSQL
- Bachelor's degree in Computer Science or equivalent

Benefits:
- Competitive salary: $120,000 - $180,000
- Health insurance
- 401k matching
- Unlimited PTO
- Remote work flexibility

To apply, please send your <NAME_EMAIL> or visit our careers page.

Posted: January 15, 2024
Application deadline: March 15, 2024
`;

const _expiredJobContent = `
Marketing Manager Position - FILLED
MarketingCorp

This position has been filled. Thank you to all applicants.

We are no longer accepting applications for this role.
Posted: December 1, 2023
Status: Position Filled
`;

const _minimalJobContent = `
Software Developer
StartupCorp

Looking for a developer to join our team.
Contact: <EMAIL>
`;

// Real Production Schema Tests
test('JobExtractionSchema validates with real production schema', () => {
  const validJobData = {
    title: 'Senior Software Engineer',
    description: 'We are seeking a Senior Software Engineer...',
    status: 'active' as const,
    company: 'TechCorp Inc.',
    type: 'Full-time' as const,
    apply_url: 'https://techcorp.com/careers',
    apply_method: 'link' as const,
    posted_date: '2024-01-15T08:00:00.000Z',
    salary_min: 120_000,
    salary_max: 180_000,
    salary_currency: 'USD' as const,
    salary_unit: 'year' as const,
    workplace_type: 'Remote' as const,
    remote_region: 'Worldwide' as const,
    timezone_requirements: null,
    workplace_city: null,
    workplace_country: null,
    benefits: 'Health insurance, 401k, unlimited PTO',
    application_requirements: "5+ years experience, Bachelor's degree",
    valid_through: '2024-03-15T23:59:59.000Z',
    job_identifier: 'TECH-2024-001',
    job_source_name: 'TechCorp Careers',
    department: 'Engineering',
    travel_required: false,
    career_level: ['Senior'],
    visa_sponsorship: 'No',
    languages: ['en'],
    skills: 'JavaScript, TypeScript, React, Node.js, PostgreSQL',
    qualifications: '5+ years of software development experience',
    education_requirements: "Bachelor's degree in Computer Science",
    experience_requirements: '5+ years in software development',
    responsibilities: 'Design and develop scalable web applications',
    featured: false,
    industry: 'Technology',
    occupational_category: 'Software Development',
  };

  try {
    const result = JobExtractionSchema.parse(validJobData);
    assert.ok(result, 'Should parse valid job data with real schema');
    assert.equal(result.title, 'Senior Software Engineer');
    assert.equal(result.salary_min, 120_000);
    assert.equal(result.career_level?.[0], 'Senior');
    assert.equal(result.languages?.[0], 'en');
  } catch (error) {
    assert.fail(`Real schema should parse valid data: ${error}`);
  }
});

test('JobExtractionSchema enforces real schema constraints', () => {
  const invalidJobData = {
    title: 'Test Job',
    description: 'A' * (SCHEMA_LIMITS.benefits + 100), // Exceeds character limit
    status: 'invalid_status', // Invalid enum value
    salary_min: -1000, // Negative salary
    career_level: 'Senior', // Should be array, not string
    languages: 'en', // Should be array, not string
  };

  try {
    JobExtractionSchema.parse(invalidJobData);
    assert.fail('Should reject invalid data');
  } catch (error) {
    assert.ok(error, 'Should throw validation error for invalid data');
  }
});

test('JobSchema includes metadata fields', () => {
  const jobWithMetadata = {
    sourcedAt: '2024-01-15T10:30:00.000Z',
    sourceUrl: 'https://techcorp.com/careers/senior-engineer',
    title: 'Senior Software Engineer',
    description: 'A great opportunity',
    status: 'active' as const,
    company: null,
    type: null,
    apply_url: null,
    apply_method: null,
    posted_date: null,
    salary_min: null,
    salary_max: null,
    salary_currency: null,
    salary_unit: null,
    workplace_type: null,
    remote_region: null,
    timezone_requirements: null,
    workplace_city: null,
    workplace_country: null,
    benefits: null,
    application_requirements: null,
    valid_through: null,
    job_identifier: null,
    job_source_name: null,
    department: null,
    travel_required: null,
    career_level: null,
    visa_sponsorship: null,
    languages: null,
    skills: null,
    qualifications: null,
    education_requirements: null,
    experience_requirements: null,
    responsibilities: null,
    featured: null,
    industry: null,
    occupational_category: null,
  };

  try {
    const result = JobSchema.parse(jobWithMetadata);
    assert.ok(result, 'Should parse job with metadata');
    assert.equal(result.sourcedAt, '2024-01-15T10:30:00.000Z');
    assert.equal(
      result.sourceUrl,
      'https://techcorp.com/careers/senior-engineer'
    );
  } catch (error) {
    assert.fail(`JobSchema should parse valid data: ${error}`);
  }
});

// Real Prompt Tests
test('JOB_EXTRACTION_PROMPT contains real production prompt elements', () => {
  assert.ok(JOB_EXTRACTION_PROMPT.includes('Extract job data as JSON'));
  assert.ok(JOB_EXTRACTION_PROMPT.includes('Use null for missing data'));
  assert.ok(JOB_EXTRACTION_PROMPT.includes('REQUIRED DATA TYPES & ENUMS'));
  assert.ok(JOB_EXTRACTION_PROMPT.includes('{content}'));

  // Check that it includes real enum values (not variable names)
  assert.ok(JOB_EXTRACTION_PROMPT.includes('Full-time'));
  assert.ok(JOB_EXTRACTION_PROMPT.includes('active'));
  assert.ok(JOB_EXTRACTION_PROMPT.includes('link'));
  assert.ok(JOB_EXTRACTION_PROMPT.includes('Remote'));
  assert.ok(JOB_EXTRACTION_PROMPT.includes('Senior'));

  // Check character limits are included
  assert.ok(JOB_EXTRACTION_PROMPT.includes('CHARACTER LIMITS'));
  assert.ok(JOB_EXTRACTION_PROMPT.includes('benefits: max'));
});

test('JOB_STATUS_CLASSIFIER_PROMPT contains monitoring elements', () => {
  assert.ok(JOB_STATUS_CLASSIFIER_PROMPT.includes('job status classifier'));
  assert.ok(
    JOB_STATUS_CLASSIFIER_PROMPT.includes('active | closed | filled | unknown')
  );
  assert.ok(JOB_STATUS_CLASSIFIER_PROMPT.includes('confidence'));
  assert.ok(JOB_STATUS_CLASSIFIER_PROMPT.includes('{snippet}'));
});

test('testReplaceTemplateVariables works with real job content', () => {
  const result = testReplaceTemplateVariables(JOB_EXTRACTION_PROMPT, {
    content: realJobContent,
  });

  assert.ok(
    !result.includes('{content}'),
    'Should replace content placeholder'
  );
  assert.ok(
    result.includes('Senior Software Engineer'),
    'Should include job content'
  );
  assert.ok(result.includes('TechCorp Inc.'), 'Should include company name');
  assert.ok(
    result.length > JOB_EXTRACTION_PROMPT.length,
    'Should be longer after replacement'
  );
});

test('testReplaceTemplateVariables works with status classifier prompt', () => {
  const snippet = 'This position has been filled. Applications are closed.';
  const result = testReplaceTemplateVariables(JOB_STATUS_CLASSIFIER_PROMPT, {
    snippet,
  });

  assert.ok(
    !result.includes('{snippet}'),
    'Should replace snippet placeholder'
  );
  assert.ok(
    result.includes('position has been filled'),
    'Should include snippet content'
  );
});

// Real Utility Function Tests
test('estimateReadingTime calculates time for real job content', () => {
  const readingTime = estimateReadingTime(realJobContent);

  assert.ok(typeof readingTime === 'number', 'Should return a number');
  assert.ok(readingTime > 0, 'Should return positive reading time');
  assert.ok(readingTime < 10, 'Should be reasonable for job posting length');
});

test('testGenerateMetadata creates real metadata structure', () => {
  const startTime = Date.now() - 1000; // 1 second ago
  const usage = {
    inputTokens: 500,
    outputTokens: 200,
    totalTokens: 700,
  };

  const metadata = testGenerateMetadata(startTime, usage, {
    model: AI_CONFIG.MODEL,
    contentLength: realJobContent.length,
  });

  assert.ok(typeof metadata === 'object', 'Should return metadata object');
  assert.ok(metadata.duration > 0, 'Should have positive duration');
  assert.ok(metadata.inputTokens === 500, 'Should include input tokens');
  assert.ok(metadata.outputTokens === 200, 'Should include output tokens');
  assert.ok(metadata.model === AI_CONFIG.MODEL, 'Should include model info');
});

test('isJobExpired correctly identifies expired jobs', () => {
  const expiredDate = '2023-12-01T00:00:00.000Z'; // Past date
  const futureDate = '2025-12-01T00:00:00.000Z'; // Future date

  assert.equal(isJobExpired(expiredDate), true, 'Should identify expired job');
  assert.equal(isJobExpired(futureDate), false, 'Should identify active job');
  assert.equal(isJobExpired(null), false, 'Should handle null dates');
});

// Real Fallback Logic Tests
test('testApplyFallbackLogic sets apply_url from sourceUrl when missing', () => {
  const extractedJob = {
    apply_url: null,
  };
  const sourceUrl = 'https://techcorp.com/careers/senior-engineer';

  testApplyFallbackLogic(extractedJob, sourceUrl);

  assert.equal(
    extractedJob.apply_url,
    sourceUrl,
    'Should set apply_url to sourceUrl'
  );
});

test('testApplyFallbackLogic preserves existing apply_url', () => {
  const originalApplyUrl = 'https://techcorp.com/apply/123';
  const extractedJob = {
    apply_url: originalApplyUrl,
  };
  const sourceUrl = 'https://techcorp.com/careers/senior-engineer';

  testApplyFallbackLogic(extractedJob, sourceUrl);

  assert.equal(
    extractedJob.apply_url,
    originalApplyUrl,
    'Should preserve existing apply_url'
  );
});

test('testApplyStatusLogic sets status to expired for expired jobs', () => {
  const extractedJob = {
    status: 'active' as const,
    valid_through: '2023-12-01T00:00:00.000Z', // Past date
  };

  testApplyStatusLogic(extractedJob);

  assert.equal(extractedJob.status, 'expired', 'Should set status to expired');
});

test('testApplyStatusLogic preserves status for active jobs', () => {
  const extractedJob = {
    status: 'active' as const,
    valid_through: '2025-12-01T00:00:00.000Z', // Future date
  };

  testApplyStatusLogic(extractedJob);

  assert.equal(extractedJob.status, 'active', 'Should preserve active status');
});

// Real Constants Tests
test('AI_CONFIG contains production configuration', () => {
  assert.ok(
    typeof AI_CONFIG.MODEL === 'string',
    'Should have model configuration'
  );
  assert.ok(AI_CONFIG.MODEL.length > 0, 'Should have non-empty model name');
});

test('SCHEMA_LIMITS contains real character limits', () => {
  assert.ok(
    typeof SCHEMA_LIMITS.benefits === 'number',
    'Should have benefits limit'
  );
  assert.ok(
    typeof SCHEMA_LIMITS.skills === 'number',
    'Should have skills limit'
  );
  assert.ok(
    typeof SCHEMA_LIMITS.responsibilities === 'number',
    'Should have responsibilities limit'
  );

  assert.ok(SCHEMA_LIMITS.benefits > 0, 'Should have positive limits');
  assert.ok(SCHEMA_LIMITS.skills > 0, 'Should have positive limits');
});

// Real Enum Validation Tests
test('Production enums contain expected values', () => {
  // Test that real enums are properly defined
  assert.ok(JOB_STATUSES.includes('active'), 'Should include active status');
  assert.ok(JOB_STATUSES.includes('expired'), 'Should include expired status');

  assert.ok(JOB_TYPES.includes('Full-time'), 'Should include full-time type');
  assert.ok(JOB_TYPES.includes('Contract'), 'Should include contract type');

  assert.ok(APPLY_METHODS.includes('link'), 'Should include link method');
  assert.ok(APPLY_METHODS.includes('email'), 'Should include email method');

  assert.ok(
    WORKPLACE_TYPES.includes('Remote'),
    'Should include remote workplace'
  );
  assert.ok(
    WORKPLACE_TYPES.includes('Hybrid'),
    'Should include hybrid workplace'
  );

  assert.ok(CAREER_LEVELS.includes('Senior'), 'Should include senior level');
  assert.ok(CAREER_LEVELS.includes('Junior'), 'Should include junior level');
});

// Integration Tests with Real Data
test('Real job content processing pipeline', () => {
  // 1. Template replacement
  const prompt = testReplaceTemplateVariables(JOB_EXTRACTION_PROMPT, {
    content: realJobContent,
  });

  assert.ok(
    prompt.includes('Senior Software Engineer'),
    'Should include job title'
  );
  assert.ok(prompt.includes('TechCorp Inc.'), 'Should include company');

  // 2. Reading time estimation
  const readingTime = estimateReadingTime(realJobContent);
  assert.ok(readingTime > 0, 'Should estimate reading time');

  // 3. Mock extracted job data (simulating AI response)
  const mockExtractedJob = {
    title: 'Senior Software Engineer',
    company: 'TechCorp Inc.',
    status: 'active' as const,
    apply_url: null,
    valid_through: '2025-12-15T23:59:59.000Z', // Future date
  };

  // 4. Apply fallback logic
  testApplyFallbackLogic(mockExtractedJob, 'https://techcorp.com/careers');
  assert.equal(mockExtractedJob.apply_url, 'https://techcorp.com/careers');

  // 5. Apply status logic
  testApplyStatusLogic(mockExtractedJob);
  assert.equal(mockExtractedJob.status, 'active'); // Should remain active (not expired)
});

test('Expired job content processing', () => {
  const mockExpiredJob = {
    status: 'active' as const,
    valid_through: '2023-12-01T00:00:00.000Z', // Past date
  };

  testApplyStatusLogic(mockExpiredJob);
  assert.equal(
    mockExpiredJob.status,
    'expired',
    'Should detect and mark as expired'
  );
});
