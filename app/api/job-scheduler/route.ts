import { type NextRequest, NextResponse } from "next/server";
import { runSimpleJobScheduler } from "@/lib/simple-job-scheduler";
// Temporarily remove logger import to test circular dependency
// import { logger } from "@/lib/utils";

// Protect this endpoint with a secret
const SCHEDULER_SECRET = process.env.SCHEDULER_SECRET;

export async function POST(req: NextRequest) {
  const startTime = Date.now();
  try {
    // VERBOSE DEBUG: Log all request details
    const authHeader = req.headers.get("authorization");
    const userAgent = req.headers.get("user-agent");
    const providedSecret = authHeader?.replace("Bearer ", "");

    console.log("🚀 JOB SCHEDULER TRIGGERED", {
      timestamp: new Date().toISOString(),
      userAgent,
      hasAuthHeader: !!authHeader,
      secretMatches: providedSecret === SCHEDULER_SECRET,
      requestHeaders: Object.fromEntries(req.headers.entries()),
    });

    // Check authorization
    if (!SCHEDULER_SECRET || providedSecret !== SCHEDULER_SECRET) {
      console.error("❌ UNAUTHORIZED REQUEST", {
        providedSecret: providedSecret ? "PROVIDED" : "MISSING",
        expectedSecret: SCHEDULER_SECRET ? "CONFIGURED" : "MISSING",
      });
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    console.log("✅ AUTHORIZATION SUCCESSFUL", {
      userAgent,
      schedulerSecret: "VALID",
    });

    // Run the scheduler
    console.log("🔄 STARTING JOB SCHEDULER EXECUTION");
    const results = await runSimpleJobScheduler();

    const executionTime = Date.now() - startTime;

    // Aggregate results
    const totalPosted = results.reduce((sum, r) => sum + r.jobsPosted, 0);
    const totalFailed = results.reduce((sum, r) => sum + r.errors.length, 0);
    const success = results.length > 0 && totalFailed === 0;

    console.log("🎉 JOB SCHEDULER COMPLETED SUCCESSFULLY", {
      executionTimeMs: executionTime,
      totalPosted,
      totalFailed,
      boardsProcessed: results.length,
      success,
      detailedResults: results,
      timestamp: new Date().toISOString(),
    });

    // Log individual board results
    results.forEach((result, index) => {
      console.log(`📊 BOARD ${index + 1} RESULTS`, {
        boardId: result.boardId,
        boardName: result.boardName,
        jobsPosted: result.jobsPosted,
        errorsCount: result.errors.length,
        errors: result.errors,
      });
    });

    return NextResponse.json({
      success,
      totalPosted,
      totalFailed,
      boardsProcessed: results.length,
      results,
      executionTimeMs: executionTime,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    const executionTime = Date.now() - startTime;

    console.error("💥 JOB SCHEDULER FAILED", {
      error: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined,
      executionTimeMs: executionTime,
      timestamp: new Date().toISOString(),
    });

    return NextResponse.json(
      {
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error",
        executionTimeMs: executionTime,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// GET endpoint for manual testing/status check
export function GET(req: NextRequest) {
  const authHeader = req.headers.get("authorization");
  const providedSecret = authHeader?.replace("Bearer ", "");

  if (!SCHEDULER_SECRET || providedSecret !== SCHEDULER_SECRET) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  return NextResponse.json({
    status: "ready",
    message: "Job scheduler is ready. Send POST request to run.",
    configuration: {
      secretConfigured: !!SCHEDULER_SECRET,
      endpoint: "/api/job-scheduler",
      method: "POST",
      headers: {
        Authorization: "Bearer YOUR_SCHEDULER_SECRET",
        "Content-Type": "application/json",
      },
    },
  });
}
