/**
 * Job Validation & External ID Generation
 *
 * Handles flexible job validation and robust external ID creation
 * for different job sources (API-provided vs scraped)
 */

export type JobValidationResult = {
  isValid: boolean;
  missingFields: string[];
  externalId: string;
};

// Regex patterns defined at top level for performance
const EMAIL_REGEX = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/;
const COMPANY_TITLE_REGEX = /^([^:]+):/;
const APPLY_URL_PATTERNS = [
  /To apply:\s*<a href="([^"]+)"/i,
  /Apply:\s*<a href="([^"]+)"/i,
  /To apply:\s*(https?:\/\/[^\s\n]+)/i,
  /Apply:\s*(https?:\/\/[^\s\n]+)/i,
];

// Types for external job data sources
type RawJobData = {
  title?: string;
  source_url?: string;
  apply_email?: string;
  apply_url?: string;
  external_id?: string;
  id?: string;
  [key: string]: unknown;
};

type WorkableJobData = {
  title?: string;
  company?: string;
  description?: string;
  url?: string;
  reference_number?: string;
  [key: string]: unknown;
};

type JobDataApiData = {
  title?: string;
  content?: string;
  raw_data?: {
    original_job?: Record<string, unknown>;
  };
  [key: string]: unknown;
};

type WWRJobData = {
  id?: string;
  title?: string;
  content?: string;
  raw_data?: {
    rss_item?: {
      link?: string;
    };
  };
  [key: string]: unknown;
};

/**
 * Validates minimum required fields for a job
 * Required: title + (source_url OR apply_email)
 * Optional: company (null allowed for stealth startups)
 */
export function validateJob(
  job: RawJobData,
  source: string
): JobValidationResult {
  const missingFields: string[] = [];

  // Check title
  if (!job.title?.trim()) {
    missingFields.push('title');
  }

  // Check apply method (at least one required)
  const hasSourceUrl = job.source_url?.trim();
  const hasApplyEmail = job.apply_email?.trim();
  const hasApplyUrl = job.apply_url?.trim();

  if (!(hasSourceUrl || hasApplyEmail || hasApplyUrl)) {
    missingFields.push('source_url OR apply_email OR apply_url');
  }

  const isValid = missingFields.length === 0;

  // Use existing external_id if already set, otherwise generate one
  const externalId = job.external_id || generateExternalId(job, source);

  return {
    isValid,
    missingFields,
    externalId,
  };
}

/**
 * Generates robust external ID with collision prevention
 *
 * Strategy:
 * 1. If source provides ID: use it directly
 * 2. If we scrape: generate from content + timestamp + random suffix
 */
export function generateExternalId(job: RawJobData, source: string): string {
  // If source provides an ID, use it directly
  if (job.id) {
    return `${source}_${job.id}`;
  }

  // Otherwise, generate from job content with collision prevention
  const titleSlug =
    job.title
      ?.toLowerCase()
      .replace(/[^a-z0-9]/g, '_')
      .replace(/_+/g, '_')
      .slice(0, 30)
      .replace(/^_|_$/g, '') || 'unknown';

  const timestamp = Date.now();
  const randomSuffix = Math.random().toString(36).substring(2, 8);

  return `${source}_${titleSlug}_${timestamp}_${randomSuffix}`;
}

/**
 * Extract email from various formats
 * Handles: mailto:<EMAIL>, plain <EMAIL>, etc.
 */
export function extractEmail(text: string): string | null {
  if (!text) {
    return null;
  }

  // Handle mailto: links
  if (text.startsWith('mailto:')) {
    return text.replace('mailto:', '');
  }

  // Basic email regex
  const match = text.match(EMAIL_REGEX);

  return match ? match[0] : null;
}

/**
 * Maps Workable specific data structure to our database fields
 */
export function mapWorkableFields(
  job: WorkableJobData
): Record<string, unknown> {
  // Extract apply method - check if URL contains email
  const applicationUrl = job.url || '';
  const extractedEmail = extractEmail(applicationUrl);

  // Use reference_number as the primary identifier (consistently present)
  const externalId = job.reference_number
    ? `workable_${job.reference_number}`
    : generateExternalId(job, 'workable');

  return {
    title: job.title || 'Untitled Job',
    company: job.company || null, // Allow null for stealth companies
    description: job.description || '', // Required field - set empty string as placeholder for AI processing
    source_url: extractedEmail ? null : applicationUrl, // URL if not email
    apply_email: extractedEmail, // Email if detected
    source_type: 'workable',
    source_name: 'Workable',
    processing_status: 'pending',
    sourced_at: new Date().toISOString(),
    raw_sourced_job_data: job, // Complete original data
    external_id: externalId, // Use Workable's reference_number
  };
}

/**
 * Maps JobDataAPI specific data structure to our database fields
 */
export function mapJobDataApiFields(
  job: JobDataApiData
): Record<string, unknown> {
  // Parse the content field (JSON string)
  let parsedContent: Record<string, unknown> = {};
  try {
    parsedContent =
      typeof job.content === 'string'
        ? JSON.parse(job.content)
        : job.content || {};
  } catch (_e) {
    // Failed to parse content JSON, use empty object as fallback
  }

  // Get original job data from nested structure
  const originalJob = job.raw_data?.original_job || parsedContent;

  // CRITICAL FIX: Use JobDataAPI's actual ID for external_id
  // JobDataAPI provides clean numeric IDs like 24291964
  // Check multiple possible locations for the ID
  const jobDataApiId =
    originalJob.id || // raw_data.original_job.id
    parsedContent.id || // parsed content.id
    job.raw_data?.original_job?.id || // alternate path
    job.id; // fallback to job.id

  const externalId = jobDataApiId
    ? `jobdataapi_${jobDataApiId}`
    : generateExternalId(job, 'jobdataapi');

  // Extract apply method
  const originalJobData = originalJob as Record<string, unknown>;
  const applicationUrl =
    (originalJobData.application_url as string) || (job.url as string) || '';
  const extractedEmail = extractEmail(applicationUrl);

  return {
    title: job.title || (originalJobData.title as string) || 'Untitled Job',
    company:
      ((originalJobData.company as Record<string, unknown>)?.name as string) ||
      null, // Allow null for stealth
    description: '', // Empty placeholder - will be filled by AI processing from raw data
    source_url: extractedEmail ? null : applicationUrl, // URL if not email
    apply_url: extractedEmail ? null : applicationUrl, // CRITICAL FIX: Set apply_url for validation
    apply_email: extractedEmail, // Email if detected
    source_type: 'jobdata_api',
    source_name: 'JobDataAPI',
    processing_status: 'pending',
    sourced_at: new Date().toISOString(),
    raw_sourced_job_data: job, // Complete original data with description for AI processing
    external_id: externalId, // Use JobDataAPI's actual ID
  };
}

/**
 * Maps WeWorkRemotely specific data structure to our database fields
 */
export function mapWWRFields(job: WWRJobData): Record<string, unknown> {
  // Extract company from title (format: "Company: Job Title")
  const extractCompanyFromTitle = (title: string): string | null => {
    const match = title.match(COMPANY_TITLE_REGEX);
    return match ? match[1].trim() : null;
  };

  // Extract apply URL from content (format: "To apply: https://...")
  const extractApplyUrlFromContent = (content: string): string | null => {
    // Try multiple patterns for apply URL extraction
    for (const pattern of APPLY_URL_PATTERNS) {
      const match = content.match(pattern);
      if (match?.[1]) {
        return match[1];
      }
    }

    return null;
  };

  // Use the job ID as external identifier (job.id already includes the wwr_ prefix)
  const externalId = job.id || generateExternalId(job, 'wwr');

  // Extract company and apply URL
  const company = extractCompanyFromTitle(job.title || '');
  const applicationUrl =
    extractApplyUrlFromContent(job.content || '') ||
    (job.raw_data?.rss_item?.link as string);
  const extractedEmail = extractEmail(applicationUrl || '');

  return {
    title: job.title || 'Untitled Job',
    company, // Allow null for anonymous companies
    description: job.content || '', // Required field - set content as placeholder for AI processing
    source_url: extractedEmail ? null : applicationUrl, // URL if not email
    apply_email: extractedEmail, // Email if detected
    source_type: 'wwr_rss',
    source_name: 'WeWorkRemotely',
    processing_status: 'pending',
    sourced_at: new Date().toISOString(),
    raw_sourced_job_data: job, // Complete original data
    external_id: externalId, // Use WWR's ID (already has wwr_ prefix)
  };
}
