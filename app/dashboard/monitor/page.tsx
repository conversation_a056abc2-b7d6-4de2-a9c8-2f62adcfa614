import { Activity, Brain, Clock, TrendingUp } from 'lucide-react';
import { headers } from 'next/headers';
import { MonitorLogsTable } from '@/components/monitoring/monitor-logs-table';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { createServerClient } from '@/lib/supabase';

export const dynamic = 'force-dynamic';

type LogRow = {
  id: string;
  job_id: string | null;
  previous_status: string | null;
  new_status: string | null;
  checked_at: string | null;
  duration_millis: number | null;
  total_tokens: number | null;
  model: string | null;
  head_status: number | null;
  head_ok: boolean | null;
  decision_layer: string | null;
};

type MonitoringStats = {
  totalLogs: number;
  last24Hours: number;
  avgDuration: number;
  totalTokens: number;
  successRate: number;
  topDecisionLayer: string;
};

const _PAGE_SIZE = 20;

async function fetchMonitoringStats(): Promise<MonitoringStats> {
  const supabase = await createServerClient();
  const twentyFourHoursAgo = new Date(
    Date.now() - 24 * 60 * 60 * 1000
  ).toISOString();

  // Get total logs count
  const { count: totalLogs } = await supabase
    .from('job_monitor_logs')
    .select('*', { count: 'exact', head: true });

  // Get logs from last 24 hours
  const { count: last24Hours } = await supabase
    .from('job_monitor_logs')
    .select('*', { count: 'exact', head: true })
    .gte('checked_at', twentyFourHoursAgo);

  // Get average duration and total tokens
  const { data: aggregateData } = await supabase
    .from('job_monitor_logs')
    .select('duration_millis, total_tokens, head_ok, decision_layer')
    .not('duration_millis', 'is', null);

  const avgDuration = aggregateData?.length
    ? Math.round(
        aggregateData.reduce(
          (sum, log) => sum + (log.duration_millis || 0),
          0
        ) / aggregateData.length
      )
    : 0;

  const totalTokens =
    aggregateData?.reduce((sum, log) => sum + (log.total_tokens || 0), 0) || 0;

  const successRate = aggregateData?.length
    ? Math.round(
        (aggregateData.filter((log) => log.head_ok).length /
          aggregateData.length) *
          100
      )
    : 0;

  // Get most common decision layer
  const decisionLayerCounts =
    aggregateData?.reduce(
      (acc, log) => {
        const layer = log.decision_layer || 'unknown';
        acc[layer] = (acc[layer] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>
    ) || {};

  const topDecisionLayer =
    Object.entries(decisionLayerCounts).sort(([, a], [, b]) => b - a)[0]?.[0] ||
    'unknown';

  return {
    totalLogs: totalLogs || 0,
    last24Hours: last24Hours || 0,
    avgDuration,
    totalTokens,
    successRate,
    topDecisionLayer,
  };
}

async function fetchLogs({ jobId }: { jobId?: string }) {
  const limit = 1000;
  const params = new URLSearchParams();
  params.set('limit', String(limit));
  if (jobId) {
    params.set('jobId', jobId);
  }
  const headerList = await headers();
  const host = headerList.get('x-forwarded-host') ?? headerList.get('host');
  const proto = headerList.get('x-forwarded-proto') ?? 'http';
  const baseUrl = `${proto}://${host}`;
  const res = await fetch(`${baseUrl}/api/monitor/logs?${params.toString()}`, {
    cache: 'no-store',
  });
  if (!res.ok) {
    throw new Error('Failed to fetch monitor logs');
  }
  const json = (await res.json()) as { logs: LogRow[] };
  return json.logs;
}

export default async function MonitorLogsPage({
  searchParams,
}: {
  searchParams: Promise<{ jobId?: string }>;
}) {
  const { jobId } = await searchParams;
  const [logs, stats] = await Promise.all([
    fetchLogs({ jobId }),
    fetchMonitoringStats(),
  ]);

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      <h1 className="font-semibold text-3xl tracking-tight">📊 Monitor Logs</h1>

      {/* Stats Cards */}
      <div className="mb-8 grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="font-medium text-sm">Total Logs</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="font-bold text-2xl">
              {stats.totalLogs.toLocaleString()}
            </div>
            <p className="text-muted-foreground text-xs">
              {stats.last24Hours} in last 24h
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="font-medium text-sm">Avg Duration</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="font-bold text-2xl">
              {stats.avgDuration < 1000
                ? `${stats.avgDuration}ms`
                : `${(stats.avgDuration / 1000).toFixed(1)}s`}
            </div>
            <p className="text-muted-foreground text-xs">Average check time</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="font-medium text-sm">Success Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="font-bold text-2xl">{stats.successRate}%</div>
            <p className="text-muted-foreground text-xs">
              HTTP requests successful
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="font-medium text-sm">AI Tokens</CardTitle>
            <Brain className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="font-bold text-2xl">
              {stats.totalTokens.toLocaleString()}
            </div>
            <p className="text-muted-foreground text-xs">
              Top: {stats.topDecisionLayer}
            </p>
          </CardContent>
        </Card>
      </div>
      <MonitorLogsTable logs={logs} />
    </div>
  );
}
