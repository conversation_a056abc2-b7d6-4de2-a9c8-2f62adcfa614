/**
 * Secrets Management for Airtable PATs
 * Supports multiple security approaches based on deployment environment
 */

import crypto from 'node:crypto';
import { createServerClient, createServiceRoleClient } from './supabase';
import { logger } from './utils';

// Encryption settings
const _ENCRYPTION_ALGORITHM = 'aes-256-gcm';
function getEncryptionKey(): string {
  const key = process.env.SECRETS_ENCRYPTION_KEY;
  if (!key) {
    throw new Error('SECRETS_ENCRYPTION_KEY is required in production');
  }
  return key;
}

export type SecretStorageMethod =
  | 'env_vars'
  | 'encrypted_db'
  | 'external_service';

export type AirtableSecret = {
  boardId: string;
  pat: string;
  method: SecretStorageMethod;
};

export type EncryptedSecret = {
  encrypted: string;
  iv: string;
  tag: string;
};

/**
 * Encrypt a secret string
 */
function encryptSecret(text: string): EncryptedSecret {
  const iv = crypto.randomBytes(16);
  const key = crypto.scryptSync(getEncryptionKey(), 'salt', 32);
  const cipher = crypto.createCipheriv('aes-256-gcm', key, iv);
  cipher.setAAD(Buffer.from('airtable-pat'));

  let encrypted = cipher.update(text, 'utf8', 'hex');
  encrypted += cipher.final('hex');

  const tag = cipher.getAuthTag();

  return {
    encrypted,
    iv: iv.toString('hex'),
    tag: tag.toString('hex'),
  };
}

/**
 * Decrypt a secret
 */
function decryptSecret(encryptedData: EncryptedSecret): string {
  const key = crypto.scryptSync(getEncryptionKey(), 'salt', 32);
  const iv = Buffer.from(encryptedData.iv, 'hex');
  const decipher = crypto.createDecipheriv('aes-256-gcm', key, iv);
  decipher.setAAD(Buffer.from('airtable-pat'));
  decipher.setAuthTag(Buffer.from(encryptedData.tag, 'hex'));

  let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
  decrypted += decipher.final('utf8');

  return decrypted;
}

/**
 * Legacy function - no longer used since we only support database storage
 * @deprecated Use encrypted database storage only
 */
export function getPatFromEnvVar(_boardId: string): string | null {
  logger.warn(
    'getPatFromEnvVar is deprecated - use encrypted database storage only'
  );
  return null;
}

/**
 * Approach 2: Encrypted Database Storage
 */
export async function storeEncryptedPat(
  boardId: string,
  pat: string
): Promise<void> {
  const supabase = createServiceRoleClient();
  const encrypted = encryptSecret(pat);

  const { error } = await supabase.from('airtable_secrets').upsert(
    {
      board_id: boardId,
      encrypted_pat: JSON.stringify(encrypted),
      storage_method: 'encrypted_db',
      updated_at: new Date().toISOString(),
    },
    {
      onConflict: 'board_id',
    }
  );

  if (error) {
    logger.error('Failed to store encrypted PAT:', error);
    throw new Error('Failed to store encrypted PAT');
  }
}

export async function getEncryptedPat(boardId: string): Promise<string | null> {
  const supabase = createServiceRoleClient();

  const { data, error } = await supabase
    .from('airtable_secrets')
    .select('encrypted_pat')
    .eq('board_id', boardId)
    .single();

  if (error || !data?.encrypted_pat) {
    return null;
  }

  try {
    const encryptedData = JSON.parse(data.encrypted_pat) as EncryptedSecret;
    return decryptSecret(encryptedData);
  } catch (err) {
    logger.error('Failed to decrypt PAT:', err);
    return null;
  }
}

/**
 * Approach 3: External Secret Management (AWS Secrets Manager, HashiCorp Vault, etc.)
 *
 * Placeholder function for external secret service integration.
 * Currently returns null - implement when external secret management is needed.
 */
export function getPatFromExternalService(
  _boardId: string
): Promise<string | null> {
  // Template examples for external secret service integration:
  //
  // AWS Secrets Manager:
  // const secretsClient = new SecretsManagerClient({ region: "us-east-1" });
  // const response = await secretsClient.send(
  //   new GetSecretValueCommand({
  //     SecretId: `bordfeed/boards/${boardId}/pat`,
  //   })
  // );
  // return response.SecretString || null;

  // HashiCorp Vault:
  // const vault = require("node-vault")({
  //   apiVersion: "v1",
  //   endpoint: process.env.VAULT_ENDPOINT,
  //   token: process.env.VAULT_TOKEN,
  // });
  // const result = await vault.read(`secret/bordfeed/boards/${boardId}`);
  // return result.data.pat || null;

  // Azure Key Vault:
  // const { SecretClient } = require("@azure/keyvault-secrets");
  // const { DefaultAzureCredential } = require("@azure/identity");
  // const credential = new DefaultAzureCredential();
  // const client = new SecretClient(process.env.AZURE_KEYVAULT_URL, credential);
  // const secret = await client.getSecret(`bordfeed-board-${boardId}-pat`);
  // return secret.value || null;

  return Promise.resolve(null);
}

/**
 * Main function: Get PAT for a job board using encrypted database storage only
 */
export async function getAirtablePat(boardId: string): Promise<string | null> {
  const storageMethod =
    (process.env.AIRTABLE_PAT_STORAGE_METHOD as SecretStorageMethod) ||
    'encrypted_db';

  if (storageMethod !== 'encrypted_db') {
    logger.error(
      'Only encrypted_db storage method is supported. Current method:',
      storageMethod
    );
    return null;
  }

  // Get PAT from encrypted database only - no fallbacks
  return await getEncryptedPat(boardId);
}

/**
 * Utility: List all available PATs for debugging
 */
export async function listAvailablePats(): Promise<
  Record<string, SecretStorageMethod[]>
> {
  const result: Record<string, SecretStorageMethod[]> = {};

  // Check encrypted database only
  try {
    const supabase = createServiceRoleClient();
    const { data } = await supabase.from('airtable_secrets').select('board_id');

    if (data) {
      for (const { board_id } of data) {
        result[board_id] = ['encrypted_db'];
      }
    }
  } catch (err) {
    logger.error('Failed to check encrypted PATs:', err);
  }

  return result;
}

/**
 * Security utility: Remove plain text PAT from database
 */
export async function migratePlainTextPats(): Promise<void> {
  const supabase = await createServerClient();

  // Get all boards with plain text PATs
  const { data: boards, error } = await supabase
    .from('job_board_configs')
    .select('id, airtable_pat')
    .not('airtable_pat', 'is', null);

  if (error || !boards) {
    logger.error('Failed to fetch boards for PAT migration:', error);
    return;
  }

  logger.info(
    `Migrating ${boards.length} plain text PATs to encrypted storage`
  );

  const migrationPromises = boards.map(async (board) => {
    if (board.airtable_pat) {
      try {
        // Store encrypted version
        await storeEncryptedPat(board.id, board.airtable_pat);

        // Remove plain text version
        const supabaseForUpdate = await createServerClient();
        await supabaseForUpdate
          .from('job_board_configs')
          .update({ airtable_pat: null })
          .eq('id', board.id);

        logger.info(`Migrated PAT for board: ${board.id}`);
      } catch (err) {
        logger.error(`Failed to migrate PAT for board ${board.id}:`, err);
      }
    }
  });

  await Promise.all(migrationPromises);

  logger.info('PAT migration completed');
}
