/**
 * Consolidated Validation Module
 * 
 * Centralized validation utilities, patterns, and schemas
 * to ensure consistency and reduce duplication across the application.
 */

// Re-export all validation utilities
export * from './validation-utils';
export * from './schema-validation';

// Re-export specific items to avoid conflicts
export {
  validateAirtableBaseId,
  validateAirtablePat,
  validateAirtableTableName,
  validatePatWithError,
  validateAirtableConfig,
  validateEnvironmentConfig,
  validateApiKey,
  validateWebhookConfig,
  API_KEY_VALIDATIONS,
} from './config-validation';

export {
  ExtractRequestSchema,
  ExtractJobRequestSchema,
  TimestampSchema,
  UUIDSchema,
  URLSchema,
  EmailSchema,
  HealthCheckSchema,
  ApifyWebhookSchema,
  PaginationQuerySchema,
  AirtableConfigSchema,
  JobCreateSchema,
  JobUpdateSchema,
  type ExtractJobRequest,
  type PaginationQuery,
  type SearchQuery,
  type JobCreate,
  type JobUpdate,
} from './api-validation';