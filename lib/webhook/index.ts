/**
 * Webhook Handler Module
 * 
 * Consolidated webhook handling utilities and base classes
 * for consistent webhook processing across different sources.
 */

export * from './WebhookHandler';
export * from './webhook-utils';

// Re-export specific items to avoid conflicts
export {
  ApifyWebhookSchema,
  WWRJobSchema,
  JobDataApiJobSchema,
  WorkableJobSchema,
  validateWebhookPayload,
  type WWRJob,
  type JobDataApiJob,
  type WorkableJob,
} from './webhook-schemas';