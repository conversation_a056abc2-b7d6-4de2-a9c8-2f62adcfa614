import { NextResponse } from 'next/server';
import { logger } from '@/lib/logger';

// Types for health check responses
type HealthCheckConfig = {
  source: string;
  actorIdEnvVar: string;
  actorDisplayName: string;
};

type HealthCheckResult = {
  status: 'healthy' | 'error';
  latency: number;
  message?: string;
  error?: string;
  source: string;
  details?: Record<string, unknown>;
  statusCode?: number;
  timestamp: string;
};

// Helper function to create error response
function createErrorResponse(
  config: HealthCheckConfig,
  latency: number,
  error: string,
  statusCode?: number
): NextResponse {
  const result: HealthCheckResult = {
    status: 'error',
    latency,
    error,
    source: config.source,
    timestamp: new Date().toISOString(),
  };

  if (statusCode) {
    result.statusCode = statusCode;
  }

  return NextResponse.json(result, { status: 500 });
}

// Helper function to validate environment variables
function validateEnvironmentVariables(
  config: HealthCheckConfig,
  startTime: number
): { apifyToken: string; actorId: string } | NextResponse {
  const apifyToken = process.env.APIFY_TOKEN;
  const actorId = process.env[config.actorIdEnvVar];

  if (!apifyToken) {
    return createErrorResponse(
      config,
      Date.now() - startTime,
      'Apify token not configured'
    );
  }

  if (!actorId) {
    return createErrorResponse(
      config,
      Date.now() - startTime,
      `${config.actorDisplayName} actor ID not configured`
    );
  }

  return { apifyToken, actorId };
}

// Helper function to fetch actor details
async function fetchActorDetails(
  actorId: string,
  apifyToken: string,
  config: HealthCheckConfig,
  startTime: number
): Promise<
  { actorData: Record<string, unknown>; latency: number } | NextResponse
> {
  try {
    const response = await fetch(`https://api.apify.com/v2/acts/${actorId}`, {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${apifyToken}`,
        'Content-Type': 'application/json',
      },
      signal: AbortSignal.timeout(10_000), // 10 second timeout
    });

    const latency = Date.now() - startTime;

    if (!response.ok) {
      if (response.status === 401) {
        return createErrorResponse(
          config,
          latency,
          'Invalid Apify token',
          response.status
        );
      }

      if (response.status === 404) {
        return createErrorResponse(
          config,
          latency,
          `${config.actorDisplayName} actor not found - check ${config.actorIdEnvVar}`,
          response.status
        );
      }

      return createErrorResponse(
        config,
        latency,
        `Apify API returned ${response.status}: ${response.statusText}`,
        response.status
      );
    }

    const actorData = await response.json();
    return { actorData, latency };
  } catch (error) {
    const latency = Date.now() - startTime;
    return createErrorResponse(
      config,
      latency,
      error instanceof Error ? error.message : 'Apify connection failed'
    );
  }
}

// Helper function to fetch recent runs information
async function fetchRecentRunsInfo(
  actorId: string,
  apifyToken: string
): Promise<{
  hasRuns: boolean;
  lastRunStatus: string | null;
  totalRuns: number;
}> {
  try {
    const runsResponse = await fetch(
      `https://api.apify.com/v2/acts/${actorId}/runs?limit=5`,
      {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${apifyToken}`,
          'Content-Type': 'application/json',
        },
        signal: AbortSignal.timeout(10_000),
      }
    );

    if (runsResponse.ok) {
      const runsData = await runsResponse.json();
      const runs = runsData.data.items || [];
      return {
        hasRuns: runs.length > 0,
        lastRunStatus: runs.length > 0 ? runs[0].status : null,
        totalRuns: runsData.data.total || 0,
      };
    }
  } catch {
    // Ignore errors for runs info - it's supplementary data
  }

  return { hasRuns: false, lastRunStatus: null, totalRuns: 0 };
}

// Main health check function
export async function performHealthCheck(
  config: HealthCheckConfig
): Promise<NextResponse> {
  const startTime = Date.now();

  try {
    // Validate environment variables
    const envResult = validateEnvironmentVariables(config, startTime);
    if (envResult instanceof NextResponse) {
      return envResult;
    }
    const { apifyToken, actorId } = envResult;

    // Fetch actor details
    const actorResult = await fetchActorDetails(
      actorId,
      apifyToken,
      config,
      startTime
    );
    if (actorResult instanceof NextResponse) {
      return actorResult;
    }
    const { actorData, latency } = actorResult;

    // Extract actor information
    const data = actorData.data as Record<string, unknown>;
    const isPublic = data.isPublic as boolean;
    const actorName = data.name as string;
    const username = data.username as string;

    // Fetch recent runs information
    const recentRunsInfo = await fetchRecentRunsInfo(actorId, apifyToken);

    // Log successful health check
    logger.info(`${config.actorDisplayName} health check successful`, {
      latency,
      actorName,
      username,
      isPublic,
      ...recentRunsInfo,
    });

    // Return successful response
    return NextResponse.json({
      status: 'healthy',
      latency,
      message: `${config.actorDisplayName} actor connection successful`,
      source: config.source,
      details: {
        actorName,
        username,
        isPublic,
        actorId,
        apifyUrl: `https://console.apify.com/actors/${actorId}`,
        ...recentRunsInfo,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    const latency = Date.now() - startTime;

    logger.error(`${config.actorDisplayName} health check failed`, {
      error: error instanceof Error ? error.message : 'Unknown error',
      latency,
    });

    return createErrorResponse(
      config,
      latency,
      error instanceof Error ? error.message : 'Apify connection failed'
    );
  }
}
