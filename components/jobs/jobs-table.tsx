'use client';

import {
  type ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  type SortingState,
  type Header as TableHeaderType,
  useReactTable,
  type VisibilityState,
} from '@tanstack/react-table';
import {
  ArrowDown,
  ArrowUp,
  ChevronDown,
  Code,
  Filter,
  Play,
  Plus,
  Trash2,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { type ReactElement, useState } from 'react';
import { CareerLevelsFilterUrl } from '@/components/jobs/career-levels-filter-url';
import { CountriesFilterUrl } from '@/components/jobs/countries-filter-url';
import { JobTypesFilterUrl } from '@/components/jobs/job-types-filter-url';
import { KeywordsFilterUrl } from '@/components/jobs/keywords-filter-url';
import { LanguagesFilterUrl } from '@/components/jobs/languages-filter-url';
import { ProcessingStatusFilterUrl } from '@/components/jobs/processing-status-filter-url';
import { SalaryRangeFilterUrl } from '@/components/jobs/salary-range-filter-url';
import { SearchInputUrl } from '@/components/jobs/search-input-url';
import { SourceTypeFilterUrl } from '@/components/jobs/source-type-filter-url';
import { StatusFilterUrl } from '@/components/jobs/status-filter-url';
import { WorkplaceTypesFilterUrl } from '@/components/jobs/workplace-types-filter-url';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Separator } from '@/components/ui/separator';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useBulkOperations } from '@/lib/hooks/use-bulk-operations';
import { useJobFiltersUrl } from '@/lib/hooks/use-job-filters-url';
import { useJobsApi } from '@/lib/hooks/use-jobs-api';
import type { DatabaseJob } from '@/lib/storage';

import { createColumns } from './columns';
import { DataTablePagination } from './data-table-pagination';
import { DataTableViewOptions } from './data-table-view-options';

// Removed JobsStats cards row in favor of compact info under Filters

function SortableHeaderCell({
  header,
}: {
  // Relaxed but typed to avoid 'any' while not over-constraining generics
  header: TableHeaderType<DatabaseJob, unknown>;
}) {
  const isSortable = header.column.getCanSort();
  const sorted = header.column.getIsSorted() as false | 'asc' | 'desc';

  let ariaSort: 'ascending' | 'descending' | 'none' = 'none';
  if (sorted === 'asc') {
    ariaSort = 'ascending';
  } else if (sorted === 'desc') {
    ariaSort = 'descending';
  }

  const onClick = isSortable
    ? header.column.getToggleSortingHandler()
    : undefined;

  const containerClass = isSortable
    ? 'group cursor-pointer select-none px-0.5 py-1.5'
    : 'px-0.5 py-1.5';
  const innerClass = isSortable
    ? 'flex items-center justify-between gap-2 rounded-md px-3 py-2.5 hover:bg-accent/60'
    : 'rounded-md px-3 py-2.5';

  let icon: ReactElement | null = null;
  if (isSortable) {
    if (sorted === 'asc') {
      icon = <ArrowUp className="h-4 w-4 text-foreground" />;
    } else if (sorted === 'desc') {
      icon = <ArrowDown className="h-4 w-4 text-foreground" />;
    } else {
      icon = <ArrowUp className="h-4 w-4 text-muted-foreground" />;
    }
  }

  return (
    <TableHead
      aria-sort={ariaSort}
      className={containerClass}
      onClick={onClick}
      onKeyDown={(event) => {
        if (!isSortable) {
          return;
        }
        if (event.key === 'Enter') {
          const handler = header.column.getToggleSortingHandler();
          if (typeof handler === 'function') {
            handler(event as unknown as React.MouseEvent);
          }
        }
      }}
      tabIndex={isSortable ? 0 : -1}
    >
      <div className={innerClass}>
        {header.isPlaceholder
          ? null
          : flexRender(header.column.columnDef.header, header.getContext())}
        {icon}
      </div>
    </TableHead>
  );
}

type JobsFilterSidebarProps = {
  loading: boolean;
  activeFilterCount: number;
  executionTime?: number;
  hasActiveFilters: boolean;
  onClearAll: () => void;
};

function JobsFilterSidebar({
  loading,
  activeFilterCount,
  executionTime,
  hasActiveFilters,
  onClearAll,
}: JobsFilterSidebarProps) {
  return (
    <div className="space-y-5 lg:col-span-1">
      <Card>
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Filter className="h-5 w-5" />
              Filters
            </CardTitle>
            <Button
              className="h-7 px-2 text-xs"
              disabled={!hasActiveFilters || loading}
              onClick={onClearAll}
              size="sm"
              variant="ghost"
            >
              Clear all
            </Button>
          </div>
          <CardDescription>
            <span className="text-xs">
              {activeFilterCount} active filters
              {typeof executionTime === 'number' && (
                <>
                  {' • '}
                  Query Time: {executionTime}ms
                </>
              )}
            </span>
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          {/* Search */}
          <SearchInputUrl disabled={loading} />

          {/* Status */}
          <div className="mt-2 font-medium text-[11px] text-muted-foreground uppercase tracking-wide">
            Status
          </div>
          <StatusFilterUrl disabled={loading} />

          {/* Processing Status */}
          <ProcessingStatusFilterUrl disabled={loading} />

          {/* Source Type */}
          <SourceTypeFilterUrl disabled={loading} />

          <Separator />

          {/* Job Basics */}
          <div className="font-medium text-[11px] text-muted-foreground uppercase tracking-wide">
            Job basics
          </div>
          {/* Job Types */}
          <JobTypesFilterUrl disabled={loading} />

          {/* Workplace Types */}
          <WorkplaceTypesFilterUrl disabled={loading} />

          {/* Career Levels */}
          <CareerLevelsFilterUrl disabled={loading} />

          <Separator />

          {/* Compensation */}
          <div className="font-medium text-[11px] text-muted-foreground uppercase tracking-wide">
            Compensation
          </div>
          {/* Salary Range */}
          <SalaryRangeFilterUrl disabled={loading} />

          <Separator />

          <div className="font-medium text-[11px] text-muted-foreground uppercase tracking-wide">
            Keywords
          </div>
          <KeywordsFilterUrl disabled={loading} />

          <Separator />

          {/* Location */}
          <div className="font-medium text-[11px] text-muted-foreground uppercase tracking-wide">
            Location
          </div>
          {/* Countries */}
          <CountriesFilterUrl disabled={loading} />

          {/* Languages */}
          <LanguagesFilterUrl disabled={loading} />
        </CardContent>
      </Card>
    </div>
  );
}

export function JobsTable() {
  const router = useRouter();
  const [sorting, setSorting] = useState<SortingState>([
    { id: 'created_at', desc: true },
  ]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({
    // Show most important columns by default
    title: true,
    company: true,
    status: true,
    processing_status: true,
    workplace_type: true,
    salary_min: true,
    created_at: true,
    // Hide detailed fields by default (can be toggled via View button)
    department: false,
    industry: false,
    occupational_category: false,
    monitor_status: false,
    featured: false,
    remote_region: false,
    workplace_city: false,
    workplace_country: false,
    timezone_requirements: false,
    travel_required: false,
    salary_currency: false,
    salary_unit: false,
    career_level: false,
    visa_sponsorship: false,
    languages: false,
    education_requirements: false,
    experience_requirements: false,
    skills: false,
    apply_method: false,
    posted_date: false,
    valid_through: false,
    job_source_name: false,
    job_identifier: false,
    updated_at: false,
    last_checked_at: false,
    monitor_attempts: false,
  });
  const [rowSelection, setRowSelection] = useState({});

  // Page size state
  const [pageSize, setPageSize] = useState(20);

  // URL-based filter state
  const { filters, activeFilterCount, hasActiveFilters, clearAllFilters } =
    useJobFiltersUrl();

  // API and data management from custom hook
  const {
    jobs,
    loading,
    pagination,
    sqlQuery,
    executionTime,
    loadJobs,
    handlePageChange,
    handlePageSizeChange,
  } = useJobsApi({ filters, sorting, pageSize });

  // Handle page size changes
  const onPageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    handlePageSizeChange(newPageSize);
  };

  // Create columns with refresh callback
  const columns = createColumns(() => loadJobs());

  // Server-side table configuration
  const table = useReactTable({
    data: jobs,
    columns,
    pageCount: pagination.totalPages,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      pagination: {
        pageIndex: pagination.currentPage - 1,
        pageSize: pagination.itemsPerPage,
      },
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    getCoreRowModel: getCoreRowModel(),
    manualPagination: true,
    manualSorting: true,
    manualFiltering: true,
  });

  const selectedRows = table.getFilteredSelectedRowModel().rows;

  // Bulk operations hook
  const {
    handleBulkDelete,
    handleBulkStatusUpdate,
    handleBulkRequeueAI,
    handleBulkAirtablePush,
    handleBulkResetMonitor,
    handleBulkProcessNow,
  } = useBulkOperations({
    selectedRows,
    onRefresh: () => loadJobs(),
    onClearSelection: () => setRowSelection({}),
  });

  if (loading && jobs.length === 0) {
    return (
      <div className="flex items-center justify-center py-10">
        <div className="text-muted-foreground">Loading jobs...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Main Interface */}
      <Tabs className="space-y-6" defaultValue="jobs">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="jobs">Jobs & Filters</TabsTrigger>
          <TabsTrigger value="sql">SQL Query</TabsTrigger>
        </TabsList>

        <TabsContent className="space-y-6" value="jobs">
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-4">
            <JobsFilterSidebar
              activeFilterCount={activeFilterCount}
              executionTime={executionTime}
              hasActiveFilters={hasActiveFilters}
              loading={loading}
              onClearAll={clearAllFilters}
            />

            {/* Jobs Table */}
            <div className="lg:col-span-3">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle>Jobs</CardTitle>
                      <CardDescription>
                        {loading
                          ? 'Loading jobs...'
                          : `${jobs.length} of ${pagination.totalItems} jobs`}
                      </CardDescription>
                    </div>
                    <div className="flex items-center space-x-2">
                      <DataTableViewOptions table={table} />
                      <Button
                        onClick={() => router.push('/dashboard/jobs/new')}
                        size="sm"
                      >
                        <Plus className="mr-2 h-4 w-4" />
                        Add Job
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  {/* Bulk Actions */}
                  {selectedRows.length > 0 && (
                    <div className="mb-4 flex items-center space-x-2">
                      <Badge variant="secondary">
                        {selectedRows.length} selected
                      </Badge>

                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button size="sm" variant="outline">
                            Update Status
                            <ChevronDown className="ml-2 h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                          <DropdownMenuLabel>Update Status</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuCheckboxItem
                            onClick={() => handleBulkStatusUpdate('active')}
                          >
                            Active
                          </DropdownMenuCheckboxItem>
                          <DropdownMenuCheckboxItem
                            onClick={() => handleBulkStatusUpdate('inactive')}
                          >
                            Inactive
                          </DropdownMenuCheckboxItem>
                          <DropdownMenuCheckboxItem
                            onClick={() => handleBulkStatusUpdate('filled')}
                          >
                            Filled
                          </DropdownMenuCheckboxItem>
                          <DropdownMenuCheckboxItem
                            onClick={() => handleBulkStatusUpdate('expired')}
                          >
                            Expired
                          </DropdownMenuCheckboxItem>
                          <DropdownMenuCheckboxItem
                            onClick={() => handleBulkStatusUpdate('cancelled')}
                          >
                            Cancelled
                          </DropdownMenuCheckboxItem>
                        </DropdownMenuContent>
                      </DropdownMenu>

                      <Button
                        onClick={handleBulkRequeueAI}
                        size="sm"
                        variant="outline"
                      >
                        Requeue AI
                      </Button>

                      <Button
                        onClick={handleBulkAirtablePush}
                        size="sm"
                        variant="outline"
                      >
                        Push to Airtable
                      </Button>

                      <Button
                        onClick={handleBulkProcessNow}
                        size="sm"
                        variant="outline"
                      >
                        <Play className="mr-2 h-4 w-4" />
                        Process Now
                      </Button>

                      <Button
                        onClick={handleBulkResetMonitor}
                        size="sm"
                        variant="outline"
                      >
                        Reset Monitor
                      </Button>

                      <Button
                        onClick={handleBulkDelete}
                        size="sm"
                        variant="destructive"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete
                      </Button>
                    </div>
                  )}

                  {/* Table */}
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        {table.getHeaderGroups().map((headerGroup) => (
                          <TableRow key={headerGroup.id}>
                            {headerGroup.headers.map((header) => (
                              <SortableHeaderCell
                                header={header}
                                key={header.id}
                              />
                            ))}
                          </TableRow>
                        ))}
                      </TableHeader>
                      <TableBody>
                        {table.getRowModel().rows?.length ? (
                          table.getRowModel().rows.map((row) => (
                            <TableRow
                              data-state={row.getIsSelected() && 'selected'}
                              key={row.id}
                            >
                              {row.getVisibleCells().map((cell) => (
                                <TableCell key={cell.id}>
                                  {flexRender(
                                    cell.column.columnDef.cell,
                                    cell.getContext()
                                  )}
                                </TableCell>
                              ))}
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell
                              className="h-24 text-center"
                              colSpan={columns.length}
                            >
                              No results.
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </div>

                  {/* Pagination */}
                  <DataTablePagination
                    canNextPage={pagination.hasNextPage}
                    canPreviousPage={pagination.hasPreviousPage}
                    currentPage={pagination.currentPage}
                    isServerSide={true}
                    onPageChange={handlePageChange}
                    onPageSizeChange={onPageSizeChange}
                    pageSize={pageSize}
                    showSelection={true}
                    table={table}
                    totalPages={pagination.totalPages}
                  />
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="sql">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Code className="h-5 w-5" />
                Generated SQL Query
              </CardTitle>
              <CardDescription>
                The SQL query generated by the filtering system
                {executionTime && (
                  <>
                    {' '}
                    • Executed in{' '}
                    <Badge variant="secondary">{executionTime}ms</Badge>
                  </>
                )}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <pre className="overflow-x-auto rounded-md bg-muted p-4 text-sm">
                <code>{sqlQuery || 'No query available'}</code>
              </pre>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
