/**
 * Performance utilities for Zustand stores
 *
 * Provides helper functions to analyze store performance metrics
 * collected by the performance middleware.
 */

type PerformanceLog = {
  storeName: string;
  duration: number;
  threshold: number;
  timestamp: number;
};

/**
 * Get all performance logs from localStorage
 */
export function getPerformanceLogs(): PerformanceLog[] {
  if (typeof window === 'undefined') {
    return [];
  }

  try {
    const logs = localStorage.getItem('bordfeed_perf_logs');
    return logs ? JSON.parse(logs) : [];
  } catch {
    // Silently handle parse errors
    return [];
  }
}

/**
 * Get performance summary by store
 */
export function getPerformanceSummary() {
  const logs = getPerformanceLogs();

  if (logs.length === 0) {
    return { message: 'No performance data available' };
  }

  const summary: Record<
    string,
    {
      count: number;
      avgDuration: number;
      maxDuration: number;
      minDuration: number;
    }
  > = {};

  for (const log of logs) {
    if (!summary[log.storeName]) {
      summary[log.storeName] = {
        count: 0,
        avgDuration: 0,
        maxDuration: 0,
        minDuration: Number.POSITIVE_INFINITY,
      };
    }

    const store = summary[log.storeName];
    store.count += 1;
    store.maxDuration = Math.max(store.maxDuration, log.duration);
    store.minDuration = Math.min(store.minDuration, log.duration);
    store.avgDuration =
      (store.avgDuration * (store.count - 1) + log.duration) / store.count;
  }

  return summary;
}

/**
 * Clear all performance logs
 */
export function clearPerformanceLogs(): void {
  if (typeof window === 'undefined') {
    return;
  }

  localStorage.removeItem('bordfeed_perf_logs');
}

/**
 * Log current performance summary - Only in development
 */
export function logPerformanceSummary(): void {
  // Only log in development environment
  if (process.env.NODE_ENV !== 'development') {
    return;
  }

  const summary = getPerformanceSummary();

  if ('message' in summary) {
    // Use non-console methods in production builds
    return;
  }

  // Development-only performance logging
  if (typeof window !== 'undefined') {
    const summaryData = Object.entries(summary).map(([storeName, stats]) => ({
      store: storeName,
      slowUpdates: stats.count,
      avgDuration: `${stats.avgDuration.toFixed(2)}ms`,
      maxDuration: `${stats.maxDuration}ms`,
      minDuration: `${stats.minDuration}ms`,
    }));

    // Store summary for development debugging
    (
      window as Window & { __ZUSTAND_PERF_SUMMARY__?: unknown }
    ).__ZUSTAND_PERF_SUMMARY__ = summaryData;
  }
}

/**
 * Check if any store is performing poorly
 */
export function checkPerformanceHealth(): {
  healthy: boolean;
  issues: string[];
} {
  const summary = getPerformanceSummary();

  if ('message' in summary) {
    return { healthy: true, issues: [] };
  }

  const issues: string[] = [];

  for (const [storeName, stats] of Object.entries(summary)) {
    if (stats.count > 10) {
      issues.push(`${storeName} has ${stats.count} slow updates`);
    }

    if (stats.avgDuration > 200) {
      issues.push(
        `${storeName} has high average duration (${stats.avgDuration.toFixed(2)}ms)`
      );
    }

    if (stats.maxDuration > 1000) {
      issues.push(
        `${storeName} has very slow updates (max: ${stats.maxDuration}ms)`
      );
    }
  }

  return {
    healthy: issues.length === 0,
    issues,
  };
}
