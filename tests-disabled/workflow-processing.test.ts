/**
 * Unit Tests for Workflow Processing
 *
 * Tests the core workflow processing logic in isolation
 * Completely detached from production - safe to delete
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

// Mock all external dependencies
vi.mock('@upstash/workflow/nextjs', () => ({
  serve: vi.fn((handler) => ({
    POST: vi.fn(async (_request) => {
      // Mock context for testing
      const mockContext = {
        workflowRunId: 'test-workflow-123',
        requestPayload: { batchSize: 5, source: 'test' },
        run: vi.fn(async (_stepName, fn) => {
          return await fn();
        }),
      };
      return await handler(mockContext);
    }),
  })),
}));

vi.mock('ai', () => ({
  generateObject: vi.fn(),
}));

vi.mock('@ai-sdk/openai', () => ({
  openai: vi.fn(() => 'mocked-model'),
}));

vi.mock('@/lib/supabase', () => ({
  createClient: vi.fn(() => ({
    from: vi.fn(() => ({
      update: vi.fn(() => ({
        eq: vi.fn(() => ({
          is: vi.fn(() => ({
            order: vi.fn(() => ({
              limit: vi.fn(() => ({
                select: vi.fn(() =>
                  Promise.resolve({
                    data: mockJobs,
                    error: null,
                  })
                ),
              })),
            })),
          })),
        })),
      })),
      insert: vi.fn(() => Promise.resolve({ error: null })),
    })),
  })),
}));

vi.mock('@/lib/job-schema', () => ({
  JobExtractionSchema: {
    type: 'object',
    properties: {
      title: { type: 'string' },
      company: { type: 'string' },
      description: { type: 'string' },
    },
  },
}));

vi.mock('@/lib/utils', () => ({
  logger: {
    info: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
  },
}));

// Mock data
const mockJobs = [
  {
    id: 'job-1',
    processing_status: 'pending',
    external_id: 'ext-1',
    raw_sourced_job_data: {
      title: 'Software Engineer',
      company: 'Test Corp',
      description: 'Great job opportunity',
    },
    created_at: '2025-01-01T00:00:00Z',
  },
  {
    id: 'job-2',
    processing_status: 'pending',
    external_id: 'ext-2',
    raw_sourced_job_data: {
      title: 'Product Manager',
      company: 'Another Corp',
      description: 'Lead product development',
    },
    created_at: '2025-01-01T00:01:00Z',
  },
];

describe('Workflow Processing Unit Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Job Locking Logic', () => {
    it('should lock jobs atomically with correct parameters', async () => {
      // This test verifies the atomic locking logic without hitting real database
      const { createClient } = await import('@/lib/supabase');
      const mockSupabase = createClient();

      // Simulate the locking query
      const result = await mockSupabase
        .from('jobs')
        .update({
          processing_status: 'processing',
          workflow_run_id: 'test-workflow-123',
          locked_at: expect.any(String),
          locked_by: 'workflow',
        })
        .eq('processing_status', 'pending')
        .is('workflow_run_id', null)
        .order('created_at', { ascending: true })
        .limit(5)
        .select('*');

      expect(result.data).toEqual(mockJobs);
      expect(result.error).toBeNull();
    });

    it('should handle empty job queue gracefully', async () => {
      // Mock empty result
      const { createClient } = await import('@/lib/supabase');
      const mockSupabase = createClient();

      // Override mock to return empty array
      mockSupabase
        .from()
        .update()
        .eq()
        .is()
        .order()
        .limit()
        .select.mockResolvedValueOnce({
          data: [],
          error: null,
        });

      const result = await mockSupabase
        .from('jobs')
        .update({})
        .eq('processing_status', 'pending')
        .is('workflow_run_id', null)
        .order('created_at', { ascending: true })
        .limit(5)
        .select('*');

      expect(result.data).toEqual([]);
      expect(result.error).toBeNull();
    });

    it('should handle database errors during locking', async () => {
      const { createClient } = await import('@/lib/supabase');
      const mockSupabase = createClient();

      // Mock database error
      const mockError = new Error('Database connection failed');
      mockSupabase
        .from()
        .update()
        .eq()
        .is()
        .order()
        .limit()
        .select.mockResolvedValueOnce({
          data: null,
          error: mockError,
        });

      const result = await mockSupabase
        .from('jobs')
        .update({})
        .eq('processing_status', 'pending')
        .is('workflow_run_id', null)
        .order('created_at', { ascending: true })
        .limit(5)
        .select('*');

      expect(result.data).toBeNull();
      expect(result.error).toEqual(mockError);
    });
  });

  describe('AI Processing Logic', () => {
    it('should process jobs with AI successfully', async () => {
      const { generateObject } = await import('ai');

      // Mock successful AI processing
      generateObject.mockResolvedValue({
        object: {
          title: 'Software Engineer',
          company: 'Test Corp',
          description: 'Great job opportunity',
          tags: ['engineering', 'software'],
        },
        usage: {
          promptTokens: 100,
          completionTokens: 50,
          totalTokens: 150,
        },
      });

      // Simulate processing a single job
      const job = mockJobs[0];
      const result = await generateObject({
        model: 'gpt-4o-mini',
        prompt: `Extract structured data from this job posting:\n\n${JSON.stringify(job.raw_sourced_job_data)}`,
        schema: expect.any(Object),
      });

      expect(result.object).toHaveProperty('title', 'Software Engineer');
      expect(result.object).toHaveProperty('company', 'Test Corp');
      expect(result.usage).toHaveProperty('totalTokens', 150);
    });

    it('should handle AI processing failures gracefully', async () => {
      const { generateObject } = await import('ai');

      // Mock AI processing failure
      const aiError = new Error('AI service unavailable');
      generateObject.mockRejectedValue(aiError);

      try {
        await generateObject({
          model: 'gpt-4o-mini',
          prompt: 'test prompt',
          schema: expect.any(Object),
        });
      } catch (error) {
        expect(error).toEqual(aiError);
      }
    });

    it('should process multiple jobs in parallel', async () => {
      const { generateObject } = await import('ai');

      // Mock successful processing for multiple jobs
      generateObject
        .mockResolvedValueOnce({
          object: { title: 'Software Engineer', company: 'Test Corp' },
          usage: { totalTokens: 150 },
        })
        .mockResolvedValueOnce({
          object: { title: 'Product Manager', company: 'Another Corp' },
          usage: { totalTokens: 120 },
        });

      // Simulate parallel processing
      const results = await Promise.all(
        mockJobs.map(async (job) => {
          try {
            const result = await generateObject({
              model: 'gpt-4o-mini',
              prompt: `Extract data from: ${JSON.stringify(job.raw_sourced_job_data)}`,
              schema: expect.any(Object),
            });

            return {
              id: job.id,
              success: true,
              ai_metadata: result.object,
              usage: result.usage,
            };
          } catch (error) {
            return {
              id: job.id,
              success: false,
              error: error.message,
            };
          }
        })
      );

      expect(results).toHaveLength(2);
      expect(results[0]).toMatchObject({
        id: 'job-1',
        success: true,
        ai_metadata: expect.any(Object),
      });
      expect(results[1]).toMatchObject({
        id: 'job-2',
        success: true,
        ai_metadata: expect.any(Object),
      });
    });
  });

  describe('Result Saving Logic', () => {
    it('should save successful processing results', async () => {
      const { createClient } = await import('@/lib/supabase');
      const mockSupabase = createClient();

      const processedResults = [
        {
          id: 'job-1',
          success: true,
          ai_metadata: { title: 'Software Engineer', company: 'Test Corp' },
        },
      ];

      // Simulate saving results
      for (const result of processedResults) {
        await mockSupabase
          .from('jobs')
          .update({
            processing_status: result.success ? 'completed' : 'failed',
            ai_metadata: result.success ? result.ai_metadata : null,
            processed_at: expect.any(String),
            workflow_run_id: null,
            locked_at: null,
            locked_by: null,
          })
          .eq('id', result.id);
      }

      // Verify the update was called with correct parameters
      expect(mockSupabase.from).toHaveBeenCalledWith('jobs');
    });

    it('should handle failed processing results', async () => {
      const { createClient } = await import('@/lib/supabase');
      const mockSupabase = createClient();

      const processedResults = [
        {
          id: 'job-1',
          success: false,
          error: 'AI processing failed',
        },
      ];

      // Simulate saving failed results
      for (const result of processedResults) {
        await mockSupabase
          .from('jobs')
          .update({
            processing_status: 'failed',
            ai_metadata: null,
            processed_at: expect.any(String),
            workflow_run_id: null,
            locked_at: null,
            locked_by: null,
          })
          .eq('id', result.id);
      }

      expect(mockSupabase.from).toHaveBeenCalledWith('jobs');
    });
  });

  describe('Workflow Step Execution', () => {
    it('should execute all workflow steps in correct order', async () => {
      // This test verifies the workflow step execution order
      const mockContext = {
        workflowRunId: 'test-workflow-123',
        requestPayload: { batchSize: 2 },
        run: vi.fn(),
      };

      // Mock step execution
      mockContext.run
        .mockResolvedValueOnce(mockJobs) // lock-jobs step
        .mockResolvedValueOnce([
          // ai-processing step
          { id: 'job-1', success: true, ai_metadata: {} },
          { id: 'job-2', success: true, ai_metadata: {} },
        ])
        .mockResolvedValueOnce(undefined); // save-results step

      // Simulate workflow execution
      const _lockedJobs = await mockContext.run(
        'lock-jobs',
        async () => mockJobs
      );
      const _processedJobs = await mockContext.run(
        'ai-processing',
        async () => []
      );
      await mockContext.run('save-results', async () => {});

      expect(mockContext.run).toHaveBeenCalledTimes(3);
      expect(mockContext.run).toHaveBeenNthCalledWith(
        1,
        'lock-jobs',
        expect.any(Function)
      );
      expect(mockContext.run).toHaveBeenNthCalledWith(
        2,
        'ai-processing',
        expect.any(Function)
      );
      expect(mockContext.run).toHaveBeenNthCalledWith(
        3,
        'save-results',
        expect.any(Function)
      );
    });
  });
});
