import { SLACK_FLAGS } from './config';
import { createServiceRoleClient } from './supabase';

export type HealthCadence = 'off' | '15m' | '30m' | '1h' | '6h' | '24h';

export interface NotificationSettings {
  enableCritical: boolean;
  enableAlert: boolean;
  enableInfo: boolean;
  enablePipeline: boolean;
  pipelineOnlyFailures: boolean;
  pipelineSteps: string[];

  // Frequency and summary settings
  healthCadence: HealthCadence;
  jobsScrapedOnComplete: boolean;
  jobsProcessedOnComplete: boolean;
  jobsProcessedDaily: boolean;
  jobsProcessedDailyTime: string; // HH:MM (24h)
}

const DEFAULT_SETTINGS: NotificationSettings = {
  enableCritical: SLACK_FLAGS.enableCritical,
  enableAlert: SLACK_FLAGS.enableAlert,
  enableInfo: SLACK_FLAGS.enableInfo,
  enablePipeline: SLACK_FLAGS.enablePipeline,
  pipelineOnlyFailures: SLACK_FLAGS.pipelineOnlyFailures,
  pipelineSteps: Array.from(SLACK_FLAGS.pipelineSteps),
  healthCadence: '1h',
  jobsScrapedOnComplete: true,
  jobsProcessedOnComplete: true,
  jobsProcessedDaily: true,
  jobsProcessedDailyTime: '09:00',
};

let cachedSettings: NotificationSettings | null = null;
let lastFetchedAt = 0;
const CACHE_TTL_MS = 60_000; // 1 minute

/**
 * Fetch settings from the database if available, otherwise fall back to defaults.
 * Uses a short-lived in-memory cache to avoid excessive DB calls.
 */
export async function getNotificationSettings(): Promise<NotificationSettings> {
  const now = Date.now();
  if (cachedSettings && now - lastFetchedAt < CACHE_TTL_MS) {
    return cachedSettings;
  }

  try {
    const supabase = createServiceRoleClient();
    const { data, error } = await supabase
      .from('notification_settings')
      .select('settings')
      .eq('id', 'global')
      .maybeSingle();

    if (error || !data?.settings) {
      cachedSettings = DEFAULT_SETTINGS;
    } else {
      cachedSettings = {
        ...DEFAULT_SETTINGS,
        ...(data.settings as NotificationSettings),
      };
    }
  } catch {
    // If table doesn't exist or Supabase not configured, fall back
    cachedSettings = DEFAULT_SETTINGS;
  }

  lastFetchedAt = now;
  return cachedSettings;
}

export function invalidateNotificationSettingsCache() {
  cachedSettings = null;
  lastFetchedAt = 0;
}
