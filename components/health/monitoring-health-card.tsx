'use client';

import { Lock, Monitor } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

type MonitoringData = {
  success: boolean;
  data?: Record<string, unknown>;
};

type MonitoringHealthCardProps = {
  data?: MonitoringData;
};

export function MonitoringHealthCard({
  data: _data,
}: MonitoringHealthCardProps) {
  // Since monitoring endpoint requires auth, monitoring data is currently unavailable
  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center gap-2 font-medium text-sm">
          <Monitor className="h-4 w-4" />
          Monitoring
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="font-bold text-2xl">--</span>
            <Badge variant="secondary">Auth Required</Badge>
          </div>

          <div className="flex items-center gap-2 text-sm">
            <Lock className="h-3 w-3 text-yellow-500" />
            <span className="text-muted-foreground">
              Monitoring data requires authentication
            </span>
          </div>

          <div className="border-t pt-2">
            <div className="text-muted-foreground text-xs">
              Configure monitoring access to view real-time system metrics
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
