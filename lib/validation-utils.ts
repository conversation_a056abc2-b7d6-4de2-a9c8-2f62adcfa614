import { ZodError, ZodIssue } from "zod";
import { logger } from "./logger";

// Enhanced validation result types for development
export interface ValidationSuccess<T> {
  success: true;
  data: T;
  warnings: string[];
  metadata: {
    validationTime: number;
    fieldsValidated: number;
    optionalFieldsPresent: number;
  };
}

export interface ValidationFailure {
  success: false;
  errors: EnhancedValidationError[];
  rawData: unknown;
  metadata: {
    validationTime: number;
    totalErrors: number;
    criticalErrors: number;
  };
}

export interface EnhancedValidationError {
  field: string;
  message: string;
  code: string;
  severity: "critical" | "warning" | "info";
  suggestion?: string;
  receivedValue: unknown;
  expectedType: string;
  emoji: string;
}

export type ValidationResult<T> = ValidationSuccess<T> | ValidationFailure;

// Enhanced validation function with rich error reporting
export function validateWithRichErrors<T>(
  schema: any,
  data: unknown,
  context: string = "Unknown"
): ValidationResult<T> {
  const startTime = Date.now();

  try {
    logger.info(`🔍 [${context}] Starting validation...`, {
      dataKeys:
        typeof data === "object" && data ? Object.keys(data) : "non-object",
      timestamp: new Date().toISOString(),
    });

    const result = schema.parse(data);
    const validationTime = Date.now() - startTime;

    // Count fields for metadata
    const fieldsValidated =
      typeof data === "object" && data ? Object.keys(data).length : 0;
    const optionalFieldsPresent = countOptionalFields(result);

    const warnings = generateWarnings(result);

    logger.info(`✅ [${context}] Validation successful!`, {
      validationTime: `${validationTime}ms`,
      fieldsValidated,
      optionalFieldsPresent,
      warnings: warnings.length,
      timestamp: new Date().toISOString(),
    });

    return {
      success: true,
      data: result,
      warnings,
      metadata: {
        validationTime,
        fieldsValidated,
        optionalFieldsPresent,
      },
    };
  } catch (error) {
    const validationTime = Date.now() - startTime;

    if (error instanceof ZodError) {
      const enhancedErrors = error.issues.map(enhanceZodIssue);
      const criticalErrors = enhancedErrors.filter(
        (e) => e.severity === "critical"
      ).length;

      logger.error(`❌ [${context}] Validation failed!`, {
        validationTime: `${validationTime}ms`,
        totalErrors: enhancedErrors.length,
        criticalErrors,
        errors: enhancedErrors.map(
          (e) => `${e.emoji} ${e.field}: ${e.message}`
        ),
        timestamp: new Date().toISOString(),
      });

      return {
        success: false,
        errors: enhancedErrors,
        rawData: data,
        metadata: {
          validationTime,
          totalErrors: enhancedErrors.length,
          criticalErrors,
        },
      };
    }

    // Handle non-Zod errors
    logger.error(`💥 [${context}] Unexpected validation error!`, {
      error: error instanceof Error ? error.message : String(error),
      validationTime: `${validationTime}ms`,
      timestamp: new Date().toISOString(),
    });

    return {
      success: false,
      errors: [
        {
          field: "unknown",
          message:
            error instanceof Error ? error.message : "Unknown validation error",
          code: "UNKNOWN_ERROR",
          severity: "critical",
          receivedValue: data,
          expectedType: "valid data",
          emoji: "💥",
        },
      ],
      rawData: data,
      metadata: {
        validationTime,
        totalErrors: 1,
        criticalErrors: 1,
      },
    };
  }
}

// Enhanced Zod issue processing
function enhanceZodIssue(issue: ZodIssue): EnhancedValidationError {
  const field = issue.path.join(".");

  // Determine severity based on error type
  const severity = getSeverity(issue);

  // Generate helpful suggestions
  const suggestion = generateSuggestion(issue);

  // Get appropriate emoji
  const emoji = getErrorEmoji(issue);

  return {
    field: field || "root",
    message: issue.message,
    code: issue.code,
    severity,
    suggestion,
    receivedValue: (issue as any).received || "undefined",
    expectedType: getExpectedType(issue),
    emoji,
  };
}

// Helper functions
function getSeverity(issue: ZodIssue): "critical" | "warning" | "info" {
  // Critical errors for required fields
  if (
    issue.code === "too_small" &&
    issue.path.some((p) => ["title", "description"].includes(String(p)))
  ) {
    return "critical";
  }

  // Warnings for optional fields
  if (issue.code === "invalid_value" || issue.code === "invalid_type") {
    return "warning";
  }

  return "info";
}

function generateSuggestion(issue: ZodIssue): string | undefined {
  switch (issue.code) {
    case "invalid_type":
      return `Expected ${(issue as any).expected}, but received ${(issue as any).received}. Check data type.`;
    case "too_small":
      return `Value is too short. Minimum length is ${(issue as any).minimum}.`;
    case "too_big":
      return `Value is too long. Maximum length is ${(issue as any).maximum}.`;
    case "invalid_value":
      return `Value must be one of: ${(issue as any).options?.join(", ") || "valid options"}`;
    default:
      return undefined;
  }
}

function getErrorEmoji(issue: ZodIssue): string {
  switch (issue.code) {
    case "invalid_type":
      return "🔧";
    case "too_small":
      return "📏";
    case "too_big":
      return "📐";
    case "invalid_value":
      return "🎯";
    default:
      return "⚠️";
  }
}

function getExpectedType(issue: ZodIssue): string {
  if (issue.code === "invalid_type") {
    return issue.expected;
  }
  if (issue.code === "invalid_value") {
    return `enum: ${(issue as any).options?.join(" | ") || "valid option"}`;
  }
  return "valid value";
}

function countOptionalFields(data: any): number {
  if (typeof data !== "object" || !data) return 0;

  // Count non-null optional fields
  return Object.values(data).filter(
    (value) => value !== null && value !== undefined && value !== ""
  ).length;
}

function generateWarnings(data: any): string[] {
  const warnings: string[] = [];

  if (typeof data !== "object" || !data) return warnings;

  // Check for potentially missing important fields
  if (!data.company)
    warnings.push(
      "💼 Company name is missing - consider extracting if available"
    );
  if (!data.salary_min && !data.salary_max)
    warnings.push("💰 Salary information is missing");
  if (!data.workplace_type) warnings.push("🏢 Workplace type not specified");
  if (!data.apply_url) warnings.push("🔗 Apply URL is missing");

  return warnings;
}

// Utility for logging validation results in development
export function logValidationResult<T>(
  result: ValidationResult<T>,
  context: string
): void {
  if (result.success) {
    console.log(`\n🎉 ${context} - Validation Success!`);
    console.log(`⏱️  Validation time: ${result.metadata.validationTime}ms`);
    console.log(`📊 Fields validated: ${result.metadata.fieldsValidated}`);
    console.log(
      `📋 Optional fields present: ${result.metadata.optionalFieldsPresent}`
    );

    if (result.warnings.length > 0) {
      console.log(`\n⚠️  Warnings (${result.warnings.length}):`);
      result.warnings.forEach((warning) => console.log(`   ${warning}`));
    }
  } else {
    console.log(`\n❌ ${context} - Validation Failed!`);
    console.log(`⏱️  Validation time: ${result.metadata.validationTime}ms`);
    console.log(`🚨 Total errors: ${result.metadata.totalErrors}`);
    console.log(`💥 Critical errors: ${result.metadata.criticalErrors}`);

    console.log(`\n📋 Error Details:`);
    result.errors.forEach((error, index) => {
      console.log(
        `   ${index + 1}. ${error.emoji} ${error.field}: ${error.message}`
      );
      if (error.suggestion) {
        console.log(`      💡 Suggestion: ${error.suggestion}`);
      }
      console.log(`      📥 Received: ${JSON.stringify(error.receivedValue)}`);
      console.log(`      📤 Expected: ${error.expectedType}`);
    });
  }
}
