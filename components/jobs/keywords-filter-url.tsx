'use client';

import { Plus, X } from 'lucide-react';
import { parseAsString, useQueryStates } from 'nuqs';
import { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

type KeywordsFilterUrlProps = {
  disabled?: boolean;
};

/**
 * Keywords filter component with URL state
 * Follows Pattern 1 from nuqs-todo.md: Single Source of Truth
 *
 * Key features:
 * - URL state stores comma-separated strings for include/exclude keywords
 * - Local input state for adding new keywords
 * - Add/remove functionality with visual feedback
 * - No infinite loops
 */
export function KeywordsFilterUrl({
  disabled = false,
}: KeywordsFilterUrlProps) {
  // URL state for keywords (comma-separated strings)
  const [keywordsState, setKeywordsState] = useQueryStates({
    include: parseAsString.withDefault(''),
    exclude: parseAsString.withDefault(''),
  });

  // Local input states for adding new keywords
  const [includeInput, setIncludeInput] = useState('');
  const [excludeInput, setExcludeInput] = useState('');

  // Convert strings to arrays for UI
  const includeKeywords = keywordsState.include
    ? keywordsState.include
        .split(',')
        .map((s) => s.trim())
        .filter(Boolean)
    : [];

  const excludeKeywords = keywordsState.exclude
    ? keywordsState.exclude
        .split(',')
        .map((s) => s.trim())
        .filter(Boolean)
    : [];

  // Add include keyword
  const addIncludeKeyword = () => {
    if (includeInput.trim() && !includeKeywords.includes(includeInput.trim())) {
      const newKeywords = [...includeKeywords, includeInput.trim()];
      setKeywordsState({
        include: newKeywords.length > 0 ? newKeywords.join(',') : null,
      });
      setIncludeInput('');
    }
  };

  // Add exclude keyword
  const addExcludeKeyword = () => {
    if (excludeInput.trim() && !excludeKeywords.includes(excludeInput.trim())) {
      const newKeywords = [...excludeKeywords, excludeInput.trim()];
      setKeywordsState({
        exclude: newKeywords.length > 0 ? newKeywords.join(',') : null,
      });
      setExcludeInput('');
    }
  };

  // Remove include keyword
  const removeIncludeKeyword = (keyword: string) => {
    const newKeywords = includeKeywords.filter((k) => k !== keyword);
    setKeywordsState({
      include: newKeywords.length > 0 ? newKeywords.join(',') : null,
    });
  };

  // Remove exclude keyword
  const removeExcludeKeyword = (keyword: string) => {
    const newKeywords = excludeKeywords.filter((k) => k !== keyword);
    setKeywordsState({
      exclude: newKeywords.length > 0 ? newKeywords.join(',') : null,
    });
  };

  // Clear all keywords
  const clearAllKeywords = () => {
    setKeywordsState({ include: null, exclude: null });
    setIncludeInput('');
    setExcludeInput('');
  };

  // Handle Enter key press
  const handleIncludeKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addIncludeKeyword();
    }
  };

  const handleExcludeKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addExcludeKeyword();
    }
  };

  // Check if any keywords are active
  const hasKeywords = includeKeywords.length > 0 || excludeKeywords.length > 0;

  return (
    <div>
      <div className="flex items-center justify-between">
        <Label className="font-medium text-sm">Keywords</Label>
        {hasKeywords && (
          <Button
            className="h-auto p-1 text-xs"
            disabled={disabled}
            onClick={clearAllKeywords}
            size="sm"
            variant="ghost"
          >
            Clear All
          </Button>
        )}
      </div>

      <div className="mt-2 space-y-4">
        {/* Include Keywords */}
        <div>
          <Label className="text-muted-foreground text-xs">Must Include</Label>
          <div className="mt-1 flex gap-2">
            <Input
              className="text-sm"
              disabled={disabled}
              onChange={(e) => setIncludeInput(e.target.value)}
              onKeyPress={handleIncludeKeyPress}
              placeholder="e.g., React, TypeScript"
              value={includeInput}
            />
            <Button
              className="px-3"
              disabled={disabled || !includeInput.trim()}
              onClick={addIncludeKeyword}
              size="sm"
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>

          {/* Include Keywords List */}
          {includeKeywords.length > 0 && (
            <div className="mt-2 flex flex-wrap gap-1">
              {includeKeywords.map((keyword) => (
                <Badge
                  className="bg-green-100 text-green-800 text-xs hover:bg-green-200"
                  key={keyword}
                  variant="secondary"
                >
                  {keyword}
                  <button
                    className="ml-1 hover:text-green-600"
                    disabled={disabled}
                    onClick={() => removeIncludeKeyword(keyword)}
                    type="button"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ))}
            </div>
          )}
        </div>

        {/* Exclude Keywords */}
        <div>
          <Label className="text-muted-foreground text-xs">Must Exclude</Label>
          <div className="mt-1 flex gap-2">
            <Input
              className="text-sm"
              disabled={disabled}
              onChange={(e) => setExcludeInput(e.target.value)}
              onKeyPress={handleExcludeKeyPress}
              placeholder="e.g., PHP, WordPress"
              value={excludeInput}
            />
            <Button
              className="px-3"
              disabled={disabled || !excludeInput.trim()}
              onClick={addExcludeKeyword}
              size="sm"
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>

          {/* Exclude Keywords List */}
          {excludeKeywords.length > 0 && (
            <div className="mt-2 flex flex-wrap gap-1">
              {excludeKeywords.map((keyword) => (
                <Badge
                  className="bg-red-100 text-red-800 text-xs hover:bg-red-200"
                  key={keyword}
                  variant="secondary"
                >
                  {keyword}
                  <button
                    className="ml-1 hover:text-red-600"
                    disabled={disabled}
                    onClick={() => removeExcludeKeyword(keyword)}
                    type="button"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ))}
            </div>
          )}
        </div>

        {/* Summary */}
        {hasKeywords && (
          <div className="text-muted-foreground text-xs">
            {includeKeywords.length > 0 && (
              <div>
                Include: {includeKeywords.length} keyword
                {includeKeywords.length !== 1 ? 's' : ''}
              </div>
            )}
            {excludeKeywords.length > 0 && (
              <div>
                Exclude: {excludeKeywords.length} keyword
                {excludeKeywords.length !== 1 ? 's' : ''}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
