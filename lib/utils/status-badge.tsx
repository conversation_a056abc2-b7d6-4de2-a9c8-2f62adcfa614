import { AlertCircle, CheckCircle2, XCircle } from 'lucide-react';
import type { ReactElement } from 'react';
import { Badge } from '@/components/ui/badge';

export type StatusVariant =
  | 'success'
  | 'warning'
  | 'error'
  | 'inactive'
  | 'default';

export type StatusConfig = {
  label: string;
  variant: 'default' | 'secondary' | 'outline' | 'destructive';
  icon: ReactElement;
};

export const STATUS_CONFIGS: Record<string, StatusConfig> = {
  // Success states
  SUCCEEDED: {
    label: 'SUCCEEDED',
    variant: 'default',
    icon: <CheckCircle2 className="mr-1 size-3" />,
  },
  ACTIVE: {
    label: 'ACTIVE',
    variant: 'default',
    icon: <CheckCircle2 className="mr-1 size-3" />,
  },
  // Error states
  FAILED: {
    label: 'FAILED',
    variant: 'destructive',
    icon: <XCircle className="mr-1 size-3" />,
  },
  ABORTED: {
    label: 'ABORTED',
    variant: 'destructive',
    icon: <XCircle className="mr-1 size-3" />,
  },
  ERROR: {
    label: 'ERROR',
    variant: 'destructive',
    icon: <XCircle className="mr-1 size-3" />,
  },
  // Warning/inactive states
  RUNNING: {
    label: 'RUNNING',
    variant: 'outline',
    icon: <AlertCircle className="mr-1 size-3" />,
  },
  READY: {
    label: 'READY',
    variant: 'outline',
    icon: <AlertCircle className="mr-1 size-3" />,
  },
  INACTIVE: {
    label: 'INACTIVE',
    variant: 'outline',
    icon: <AlertCircle className="mr-1 size-3" />,
  },
  // Default/unknown states
  'NOT CONFIGURED': {
    label: 'NOT CONFIGURED',
    variant: 'outline',
    icon: <AlertCircle className="mr-1 size-3" />,
  },
};

export type StatusBadgeProps = {
  status: string | null | undefined;
  fallbackLabel?: string;
  className?: string;
};

export function StatusBadge({
  status,
  fallbackLabel = 'UNKNOWN',
  className = '',
}: StatusBadgeProps) {
  const normalizedStatus = status?.toUpperCase() || fallbackLabel;
  const config =
    STATUS_CONFIGS[normalizedStatus] || STATUS_CONFIGS['NOT CONFIGURED'];

  return (
    <Badge
      className={`w-fit font-mono text-xs ${className}`}
      variant={config.variant}
    >
      {config.icon}
      {config.label}
    </Badge>
  );
}

export function createStatusBadge(
  status: string | null | undefined,
  options: Partial<StatusBadgeProps> = {}
) {
  return <StatusBadge status={status} {...options} />;
}
