/**
 * OpenAPI Schema Validation Utilities
 *
 * Validates API responses against OpenAPI specification
 * Completely decoupled from application code
 */

import { readFileSync } from 'node:fs';
import { join } from 'node:path';

// Schema type definitions
type SchemaProperty = {
  type?: string;
  properties?: Record<string, SchemaProperty>;
  required?: string[];
  $ref?: string;
  items?: SchemaProperty;
  format?: string;
  enum?: unknown[];
  description?: string;
};

type ResponseDefinition = {
  description?: string;
  content?: {
    'application/json'?: {
      schema?: SchemaProperty;
    };
  };
};

type PathOperation = {
  responses?: Record<string, ResponseDefinition>;
  parameters?: unknown[];
  requestBody?: unknown;
  summary?: string;
  description?: string;
};

type OpenAPISpec = {
  paths: Record<string, Record<string, PathOperation>>;
  components: {
    schemas: Record<string, SchemaProperty>;
  };
};

let spec: OpenAPISpec | null = null;

function loadOpenAPISpec(): OpenAPISpec {
  if (spec) {
    return spec;
  }

  try {
    const specPath = join(process.cwd(), 'public/api/openapi.json');
    const content = readFileSync(specPath, 'utf-8');
    spec = JSON.parse(content) as OpenAPISpec;
    return spec;
  } catch (error) {
    throw new Error(`Failed to load OpenAPI spec: ${error}`);
  }
}

/**
 * Validates a response against OpenAPI schema
 */
export function validateResponse(
  path: string,
  method: string,
  statusCode: number,
  responseData: unknown
): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  try {
    const openapi = loadOpenAPISpec();

    // Find the endpoint definition
    const pathDef = openapi.paths[path];
    if (!pathDef) {
      errors.push(`Path ${path} not found in OpenAPI spec`);
      return { valid: false, errors };
    }

    const methodDef = pathDef[method.toLowerCase()];
    if (!methodDef) {
      errors.push(`Method ${method} not found for path ${path}`);
      return { valid: false, errors };
    }

    const responseDef = methodDef.responses?.[statusCode.toString()];
    if (!responseDef) {
      errors.push(
        `Status code ${statusCode} not defined for ${method.toUpperCase()} ${path}`
      );
      return { valid: false, errors };
    }

    // Basic validation - you can extend this with a proper JSON schema validator
    const schema = responseDef.content?.['application/json']?.schema;
    if (schema) {
      const validationErrors = validateAgainstSchema(
        responseData,
        schema,
        openapi.components.schemas
      );
      errors.push(...validationErrors);
    }

    return { valid: errors.length === 0, errors };
  } catch (error) {
    errors.push(`Validation error: ${error}`);
    return { valid: false, errors };
  }
}

/**
 * Resolves schema references ($ref) to actual schema definitions
 */
function resolveSchemaReference(
  schema: SchemaProperty,
  components: Record<string, SchemaProperty>
): { resolved: SchemaProperty | null; errors: string[] } {
  if (!schema.$ref) {
    return { resolved: schema, errors: [] };
  }

  const refPath = schema.$ref.replace('#/components/schemas/', '');
  const resolvedSchema = components[refPath];

  if (!resolvedSchema) {
    return {
      resolved: null,
      errors: [`Schema reference ${schema.$ref} not found`],
    };
  }

  return { resolved: resolvedSchema, errors: [] };
}

/**
 * Validates the basic type of data against schema type definition
 */
function validateDataType(data: unknown, schema: SchemaProperty): string[] {
  const errors: string[] = [];

  if (!schema.type) {
    return errors;
  }

  const actualType = Array.isArray(data) ? 'array' : typeof data;
  if (schema.type !== actualType) {
    errors.push(`Expected type ${schema.type}, got ${actualType}`);
  }

  return errors;
}

/**
 * Validates required properties for object schemas
 */
function validateRequiredProperties(
  data: unknown,
  schema: SchemaProperty
): string[] {
  const errors: string[] = [];

  if (!schema.required || schema.type !== 'object') {
    return errors;
  }

  for (const required of schema.required) {
    if (!(required in (data as Record<string, unknown>))) {
      errors.push(`Missing required property: ${required}`);
    }
  }

  return errors;
}

/**
 * Validates object properties recursively
 */
function validateObjectProperties(
  data: unknown,
  schema: SchemaProperty,
  components: Record<string, SchemaProperty>
): string[] {
  const errors: string[] = [];

  if (!schema.properties || typeof data !== 'object' || data === null) {
    return errors;
  }

  for (const [key, value] of Object.entries(data as Record<string, unknown>)) {
    const propSchema = schema.properties[key];
    if (propSchema) {
      const propErrors = validateAgainstSchema(value, propSchema, components);
      errors.push(...propErrors.map((err) => `${key}: ${err}`));
    }
  }

  return errors;
}

/**
 * Basic schema validation (extend with ajv or similar for production)
 */
function validateAgainstSchema(
  data: unknown,
  schema: SchemaProperty,
  components: Record<string, SchemaProperty>
): string[] {
  // Handle $ref resolution
  const { resolved, errors: refErrors } = resolveSchemaReference(
    schema,
    components
  );
  if (refErrors.length > 0) {
    return refErrors;
  }

  // If schema was resolved, validate against the resolved schema
  if (resolved !== schema && resolved !== null) {
    return validateAgainstSchema(data, resolved, components);
  }

  const errors: string[] = [];

  // Validate basic type
  errors.push(...validateDataType(data, schema));

  // Validate required properties
  errors.push(...validateRequiredProperties(data, schema));

  // Validate object properties
  errors.push(...validateObjectProperties(data, schema, components));

  return errors;
}

/**
 * Test helper for API endpoint validation
 */
export async function testEndpoint(
  url: string,
  options: RequestInit = {}
): Promise<{
  response: Response;
  data: unknown;
  validation: { valid: boolean; errors: string[] };
}> {
  const response = await fetch(url, options);
  const data = await response.json();

  // Extract path and method from URL and options
  const urlObj = new URL(url, 'http://localhost:3000');
  const path = urlObj.pathname;
  const method = options.method || 'GET';

  const validation = validateResponse(path, method, response.status, data);

  return { response, data, validation };
}
