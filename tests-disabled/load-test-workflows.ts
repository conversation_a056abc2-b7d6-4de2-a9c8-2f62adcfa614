/**
 * Load Testing Script for Workflow Processing
 *
 * Tests workflow performance under concurrent load
 * Completely detached from production - safe to delete
 */

import { performance } from 'node:perf_hooks';

type LoadTestConfig = {
  baseUrl: string;
  concurrentRequests: number;
  batchSize: number;
  iterations: number;
  delayBetweenRequests: number;
};

type LoadTestResult = {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  minResponseTime: number;
  maxResponseTime: number;
  requestsPerSecond: number;
  errors: string[];
};

class WorkflowLoadTester {
  private readonly config: LoadTestConfig;
  private readonly results: LoadTestResult;

  constructor(config: LoadTestConfig) {
    this.config = config;
    this.results = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      minResponseTime: Number.POSITIVE_INFINITY,
      maxResponseTime: 0,
      requestsPerSecond: 0,
      errors: [],
    };
  }

  async runLoadTest(): Promise<LoadTestResult> {
    const startTime = performance.now();
    const responseTimes: number[] = [];

    // Run concurrent requests
    for (let iteration = 0; iteration < this.config.iterations; iteration++) {
      const promises = Array.from(
        { length: this.config.concurrentRequests },
        (_, index) => this.makeWorkflowRequest(iteration, index)
      );

      const iterationResults = await Promise.allSettled(promises);

      // Process results
      iterationResults.forEach((result, _index) => {
        this.results.totalRequests++;

        if (result.status === 'fulfilled') {
          this.results.successfulRequests++;
          responseTimes.push(result.value.responseTime);

          this.results.minResponseTime = Math.min(
            this.results.minResponseTime,
            result.value.responseTime
          );
          this.results.maxResponseTime = Math.max(
            this.results.maxResponseTime,
            result.value.responseTime
          );
        } else {
          this.results.failedRequests++;
          this.results.errors.push(result.reason.message);
        }
      });

      // Delay between iterations
      if (
        iteration < this.config.iterations - 1 &&
        this.config.delayBetweenRequests > 0
      ) {
        await this.delay(this.config.delayBetweenRequests);
      }
    }

    const endTime = performance.now();
    const totalDuration = (endTime - startTime) / 1000; // Convert to seconds

    // Calculate final metrics
    this.results.averageResponseTime =
      responseTimes.length > 0
        ? responseTimes.reduce((sum, time) => sum + time, 0) /
          responseTimes.length
        : 0;

    this.results.requestsPerSecond = this.results.totalRequests / totalDuration;

    this.printResults(totalDuration);
    return this.results;
  }

  private async makeWorkflowRequest(
    iteration: number,
    requestIndex: number
  ): Promise<{ responseTime: number; message: string }> {
    const startTime = performance.now();

    try {
      // Test workflow trigger endpoint
      const response = await fetch(
        `${this.config.baseUrl}/api/workflows/status`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            action: 'trigger_processing',
            batchSize: this.config.batchSize,
            source: `load_test_${iteration}_${requestIndex}`,
          }),
        }
      );

      const endTime = performance.now();
      const responseTime = endTime - startTime;

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error(
          `Workflow trigger failed: ${data.message || 'Unknown error'}`
        );
      }

      return {
        responseTime,
        message: `Triggered workflow ${data.messageId}`,
      };
    } catch (error) {
      const endTime = performance.now();
      const responseTime = endTime - startTime;

      const errorMessage =
        error instanceof Error ? error.message : String(error);
      throw new Error(
        `Request failed after ${responseTime.toFixed(2)}ms: ${errorMessage}`
      );
    }
  }

  private async delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  private printResults(_totalDuration: number): void {
    if (this.results.errors.length > 0) {
      const errorCounts = this.results.errors.reduce(
        (acc, error) => {
          acc[error] = (acc[error] || 0) + 1;
          return acc;
        },
        {} as Record<string, number>
      );

      Object.entries(errorCounts).forEach(([_error, _count]) => {});
    }
  }
}

// Test configurations
const testConfigs: Record<string, LoadTestConfig> = {
  light: {
    baseUrl: 'http://localhost:3000',
    concurrentRequests: 2,
    batchSize: 3,
    iterations: 3,
    delayBetweenRequests: 1000,
  },
  moderate: {
    baseUrl: 'http://localhost:3000',
    concurrentRequests: 5,
    batchSize: 5,
    iterations: 5,
    delayBetweenRequests: 2000,
  },
  heavy: {
    baseUrl: 'http://localhost:3000',
    concurrentRequests: 10,
    batchSize: 10,
    iterations: 3,
    delayBetweenRequests: 3000,
  },
  production: {
    baseUrl: 'https://bordfeed.com',
    concurrentRequests: 3,
    batchSize: 5,
    iterations: 2,
    delayBetweenRequests: 5000,
  },
};

// CLI interface
async function main() {
  const testType = process.argv[2] || 'light';
  const config = testConfigs[testType];

  if (!config) {
    process.exit(1);
  }

  // Override base URL if provided
  if (process.argv[3]) {
    config.baseUrl = process.argv[3];
  }

  const tester = new WorkflowLoadTester(config);

  try {
    const results = await tester.runLoadTest();

    // Exit with error code if too many failures
    const failureRate = results.failedRequests / results.totalRequests;
    if (failureRate > 0.1) {
      process.exit(1);
    }
    process.exit(0);
  } catch (error) {
    const _errorMessage =
      error instanceof Error ? error.message : String(error);
    process.exit(1);
  }
}

// Export for programmatic use
export { WorkflowLoadTester };
export type { LoadTestConfig, LoadTestResult };

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}
