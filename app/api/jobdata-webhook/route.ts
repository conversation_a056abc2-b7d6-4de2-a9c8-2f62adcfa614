import { type NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { insertJobsToDatabase } from '@/lib/database-utils';
import { mapJobDataApiFields, validateJob } from '@/lib/job-validation';
import { logger } from '@/lib/utils';
import { triggerJobProcessing } from '@/lib/workflow-client';

// JobDataApiData interface for type safety
type JobDataApiData = {
  title?: string;
  content?: string;
  raw_data?: {
    original_job?: Record<string, unknown>;
  };
  [key: string]: unknown;
};

/**
 * JobDataAPI Webhook Handler - Simplified Architecture
 *
 * Fast & simple flow:
 * 1. Receive webhook from Apify
 * 2. Fetch jobs from dataset
 * 3. Dedupe with Redis
 * 4. Insert unique jobs to Supabase
 * 5. Return immediately (< 200ms)
 *
 * Processing happens async via Upstash Workflow
 */

// Redis deduplication removed - using database external_id uniqueness instead

// Apify webhook schema
const ApifyWebhookSchema = z
  .object({
    eventType: z.string(),
    eventData: z.object({
      actorRunId: z.string(),
    }),
    resource: z
      .object({
        id: z.string(),
        status: z.string(),
        defaultDatasetId: z.string().optional(),
        datasetId: z.string().optional(),
      })
      .passthrough(),
  })
  .passthrough();

/**
 * Fetches jobs from Apify dataset using actor run ID
 */
async function fetchJobsFromActorRun(actorRunId: string): Promise<unknown[]> {
  const apifyToken = process.env.APIFY_TOKEN;
  if (!apifyToken) {
    throw new Error('APIFY_TOKEN not configured');
  }

  const datasetUrl = `https://api.apify.com/v2/actor-runs/${actorRunId}/dataset/items?format=json&clean=true&token=${apifyToken}`;
  const response = await fetch(datasetUrl);

  if (!response.ok) {
    throw new Error(`Failed to fetch dataset: ${response.status}`);
  }

  const jobs = await response.json();

  if (!Array.isArray(jobs)) {
    throw new Error('Invalid dataset response format');
  }

  return jobs;
}

/**
 * Processes and validates jobs for database insertion
 */
function processJobsForInsertion(jobs: unknown[]): {
  jobsToInsert: Record<string, unknown>[];
  skippedCount: number;
} {
  const jobsToInsert: Record<string, unknown>[] = [];
  let skippedCount = 0;

  for (const job of jobs) {
    // Map JobDataAPI fields to our database structure
    const mappedJob = mapJobDataApiFields(job as JobDataApiData);

    // Validate job and generate external ID
    const validation = validateJob(mappedJob, 'jobdataapi');

    if (!validation.isValid) {
      logger.warn(
        `Skipping invalid job: ${validation.missingFields.join(', ')}`,
        {
          jobId: (job as JobDataApiData).id,
          title: (job as JobDataApiData).title,
        }
      );
      skippedCount++;
      continue;
    }

    // Add external ID and prepare for database
    const dbJob = {
      ...mappedJob,
      external_id: validation.externalId,

      // Phase B: Complete raw data storage for AI processing
      raw_sourced_job_data: job, // Store complete original job structure

      // System fields
      source_type: 'jobdata_api',
      source_name: 'JobDataAPI',
      processing_status: 'pending',
      sourced_at: new Date().toISOString(),
    };

    jobsToInsert.push(dbJob);
  }

  return { jobsToInsert, skippedCount };
}

// Database deduplication using external_id uniqueness constraints
// insertJobsToDatabase function now imported from @/lib/database-utils

export async function POST(request: NextRequest) {
  const startTime = Date.now();

  try {
    logger.info('🔔 JobDataAPI webhook received');

    // Direct webhook from Apify - no signature verification needed
    logger.info('JobDataAPI webhook processing');

    // Parse webhook payload
    const body = await request.json();
    const webhook = ApifyWebhookSchema.parse(body);

    // Only process successful runs
    if (webhook.eventType !== 'ACTOR.RUN.SUCCEEDED') {
      return NextResponse.json({
        message: 'Ignoring non-success event',
        eventType: webhook.eventType,
      });
    }

    const runId = webhook.resource.id;
    const actorRunId = webhook.eventData.actorRunId;

    if (!actorRunId) {
      return NextResponse.json(
        { error: 'No actor run ID found' },
        { status: 400 }
      );
    }

    // Fetch jobs from Apify dataset
    const jobs = await fetchJobsFromActorRun(actorRunId);

    if (jobs.length === 0) {
      logger.info('No jobs found in dataset', { actorRunId });
      return NextResponse.json({
        success: true,
        message: 'No jobs to process',
        jobsReceived: 0,
        jobsSaved: 0,
      });
    }

    logger.info(`📥 Received ${jobs.length} jobs from JobDataAPI`);

    // Process and validate jobs
    const { jobsToInsert, skippedCount } = processJobsForInsertion(jobs);

    // Insert jobs to database
    const savedCount = await insertJobsToDatabase(jobsToInsert);

    const processingTime = Date.now() - startTime;

    logger.info('✅ JobDataAPI webhook completed', {
      runId,
      jobsReceived: jobs.length,
      jobsSaved: savedCount,
      jobsSkipped: skippedCount,
      processingTime: `${processingTime}ms`,
    });

    // Track pipeline metrics
    logger.pipeline({
      step: 'STORED',
      source: 'jobdata_api',
      jobCount: savedCount,
      success: true,
      batchId: runId,
      duration: processingTime,
      stats: {
        received: jobs.length,
        saved: savedCount,
        skipped: skippedCount,
      },
    });

    // Trigger workflow processing for saved jobs
    let workflowMessageId: string | undefined;
    if (savedCount > 0) {
      try {
        workflowMessageId = await triggerJobProcessing(
          Math.min(savedCount, 10),
          'jobdata_api'
        );
        logger.info('✅ Triggered workflow processing', {
          workflowMessageId,
          jobCount: savedCount,
        });
      } catch (workflowError) {
        logger.error('❌ Failed to trigger workflow processing', {
          workflowError,
        });
        // Don't fail the webhook - jobs are saved, processing can be triggered manually
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Jobs saved successfully',
      runId,
      jobsReceived: jobs.length,
      jobsSaved: savedCount,
      jobsSkipped: skippedCount,
      processingTime: `${processingTime}ms`,
      workflowTriggered: !!workflowMessageId,
      workflowMessageId,
    });
  } catch (error) {
    logger.error('JobDataAPI webhook failed', { error });

    // Always return 200 to Apify to prevent retries
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        processingTime: `${Date.now() - startTime}ms`,
      },
      { status: 200 }
    );
  }
}

// Health check endpoint
export function GET() {
  return NextResponse.json({
    service: 'JobDataAPI Webhook',
    status: 'healthy',
    architecture: 'Simplified Direct Insert',
    features: [
      'Direct Supabase insertion',
      'Database deduplication',
      'Fast response (< 200ms)',
      'Async AI processing',
    ],
  });
}
