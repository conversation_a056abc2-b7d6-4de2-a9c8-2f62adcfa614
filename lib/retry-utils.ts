/**
 * Retry utility for handling intermittent failures
 */

export type RetryOptions = {
  /** Maximum number of retry attempts */
  maxAttempts: number;
  /** Base delay between retries in milliseconds */
  baseDelay: number;
  /** Maximum delay between retries in milliseconds */
  maxDelay: number;
  /** Whether to use exponential backoff */
  exponentialBackoff: boolean;
  /** Function to determine if an error should trigger a retry */
  shouldRetry?: (error: Error, attempt: number) => boolean;
};

export type RetryResult<T> = {
  success: boolean;
  result?: T;
  error?: Error;
  attempts: number;
  totalDuration: number;
};

/**
 * Default retry configuration for AI operations
 */
export const DEFAULT_AI_RETRY_OPTIONS: RetryOptions = {
  maxAttempts: 2, // Reduced from 3 to 2
  baseDelay: 500, // Reduced from 1000ms to 500ms
  maxDelay: 5000, // Reduced from 10000ms to 5000ms
  exponentialBackoff: true,
  shouldRetry: (error: Error, attempt: number) => {
    // Don't retry on schema validation errors (usually permanent prompt/schema issues)
    if (error.message.includes('response did not match schema')) {
      return false;
    }

    // Retry on network/timeout errors (reduced from 3 to 2 attempts)
    if (
      error.message.includes('timeout') ||
      error.message.includes('network') ||
      error.message.includes('ECONNRESET') ||
      error.message.includes('ETIMEDOUT')
    ) {
      return attempt < 2;
    }

    // Retry on rate limiting (only once)
    if (error.message.includes('rate limit') || error.message.includes('429')) {
      return attempt < 1;
    }

    // Don't retry on validation errors (permanent)
    if (
      error.message.includes('Invalid request body') ||
      error.message.includes('validation')
    ) {
      return false;
    }

    // Default: retry once for unknown errors
    return attempt < 2;
  },
};

/**
 * Calculate delay for next retry attempt
 */
function calculateDelay(attempt: number, options: RetryOptions): number {
  if (!options.exponentialBackoff) {
    return Math.min(options.baseDelay, options.maxDelay);
  }

  const exponentialDelay = options.baseDelay * 2 ** (attempt - 1);
  const jitteredDelay = exponentialDelay * (0.5 + Math.random() * 0.5); // Add jitter
  return Math.min(jitteredDelay, options.maxDelay);
}

/**
 * Sleep for specified milliseconds
 */
function sleep(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

/**
 * Retry an async operation with configurable options
 */
export async function retryOperation<T>(
  operation: () => Promise<T>,
  options: Partial<RetryOptions> = {}
): Promise<RetryResult<T>> {
  const config = { ...DEFAULT_AI_RETRY_OPTIONS, ...options };
  const startTime = Date.now();

  let lastError: Error | undefined;

  for (let attempt = 1; attempt <= config.maxAttempts; attempt++) {
    try {
      const result = await operation();
      return {
        success: true,
        result,
        attempts: attempt,
        totalDuration: Date.now() - startTime,
      };
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));

      // Check if we should retry
      const shouldRetry = config.shouldRetry?.(lastError, attempt) ?? true;

      if (attempt >= config.maxAttempts || !shouldRetry) {
        break;
      }

      // Calculate delay and wait
      const delay = calculateDelay(attempt, config);
      await sleep(delay);
    }
  }

  return {
    success: false,
    error: lastError,
    attempts: config.maxAttempts,
    totalDuration: Date.now() - startTime,
  };
}

/**
 * Wrapper for AI extraction operations with retry logic
 */
export async function retryAIExtraction<T>(
  operation: () => Promise<T>,
  customOptions?: Partial<RetryOptions>
): Promise<T> {
  const result = await retryOperation(operation, {
    ...DEFAULT_AI_RETRY_OPTIONS,
    ...customOptions,
  });

  if (!result.success) {
    // Enhance error message with retry information
    const enhancedError = new Error(
      `AI extraction failed after ${result.attempts} attempts (${result.totalDuration}ms total). Last error: ${result.error?.message}`
    );
    enhancedError.cause = result.error;
    throw enhancedError;
  }

  return result.result!;
}
