'use client';

import { MultiSelectUrlFilter } from '@/components/common/MultiSelectUrlFilter';
import { LANGUAGES } from '@/lib/data/languages';

type LanguagesFilterUrlProps = {
  disabled?: boolean;
};

/**
 * Languages filter component with URL state
 * Now uses the generic MultiSelectUrlFilter component
 */
export function LanguagesFilterUrl({
  disabled = false,
}: LanguagesFilterUrlProps) {
  return (
    <MultiSelectUrlFilter
      clearable={true}
      disabled={disabled}
      label="Languages"
      maxHeight="max-h-48"
      options={LANGUAGES}
      paramName="languages"
      searchable={true}
      showCount={true}
    />
  );
}
