/**
 * Smart Job Filtering Engine
 *
 * Uses database constants + text search instead of hardcoded patterns.
 * Much more scalable for non-tech jobs and diverse industries.
 */

import type { JobData } from './types';
import { logger } from './utils';

export type JobFilters = {
  // Database constant filters (normalized data)
  careerLevels?: readonly string[]; // Use career_level field
  workplaceTypes?: readonly string[]; // Use workplace_type field
  remoteRegions?: readonly string[]; // Use remote_region field
  jobTypes?: readonly string[]; // Use type field
  departments?: readonly string[]; // Use department field
  industries?: readonly string[]; // Use industry field

  // Salary filters
  salaryMin?: number;
  salaryMax?: number;
  salaryCurrency?: string;

  // Text search (flexible & scalable)
  includeKeywords?: readonly string[]; // Search in title + description
  excludeKeywords?: readonly string[]; // Negative search

  // Location filters
  countries?: readonly string[];
  cities?: readonly string[];

  // Date filters
  postedAfter?: Date;
  validUntil?: Date;
};

/**
 * Add career and workplace filter conditions
 */
function addJobCategoryFilters(
  conditions: string[],
  params: unknown[],
  filters: JobFilters,
  paramIndex: number
): number {
  let currentIndex = paramIndex;

  // Filter by career levels (normalized field)
  if (filters.careerLevels?.length) {
    conditions.push(`career_level && $${currentIndex}`);
    params.push(filters.careerLevels);
    currentIndex++;
  }

  // Filter by workplace types (normalized field)
  if (filters.workplaceTypes?.length) {
    conditions.push(`workplace_type = ANY($${currentIndex})`);
    params.push(filters.workplaceTypes);
    currentIndex++;
  }

  // Filter by remote regions (normalized field)
  if (filters.remoteRegions?.length) {
    conditions.push(`remote_region = ANY($${currentIndex})`);
    params.push(filters.remoteRegions);
    currentIndex++;
  }

  // Filter by job types (normalized field)
  if (filters.jobTypes?.length) {
    conditions.push(`type = ANY($${currentIndex})`);
    params.push(filters.jobTypes);
    currentIndex++;
  }

  // Filter by departments (normalized field)
  if (filters.departments?.length) {
    conditions.push(`department = ANY($${currentIndex})`);
    params.push(filters.departments);
    currentIndex++;
  }

  // Filter by industries (normalized field)
  if (filters.industries?.length) {
    conditions.push(`industry = ANY($${currentIndex})`);
    params.push(filters.industries);
    currentIndex++;
  }

  return currentIndex;
}

/**
 * Add salary range filter conditions
 */
function addSalaryFilters(
  conditions: string[],
  params: unknown[],
  filters: JobFilters,
  paramIndex: number
): number {
  let currentIndex = paramIndex;

  if (filters.salaryMin !== undefined) {
    conditions.push(`salary_min >= $${currentIndex}`);
    params.push(filters.salaryMin);
    currentIndex++;
  }

  if (filters.salaryMax !== undefined) {
    conditions.push(`salary_max <= $${currentIndex}`);
    params.push(filters.salaryMax);
    currentIndex++;
  }

  if (filters.salaryCurrency) {
    conditions.push(`salary_currency = $${currentIndex}`);
    params.push(filters.salaryCurrency);
    currentIndex++;
  }

  return currentIndex;
}

/**
 * Add text search filter conditions
 */
function addTextSearchFilters(
  conditions: string[],
  params: unknown[],
  filters: JobFilters,
  paramIndex: number
): number {
  let currentIndex = paramIndex;

  // Text search - include keywords (flexible tech stack detection)
  if (filters.includeKeywords?.length) {
    const keywordConditions = filters.includeKeywords.map((keyword) => {
      const condition = `(title ILIKE $${currentIndex} OR description ILIKE $${currentIndex} OR skills ILIKE $${currentIndex})`;
      params.push(`%${keyword}%`);
      currentIndex++;
      return condition;
    });

    if (keywordConditions.length > 0) {
      conditions.push(`(${keywordConditions.join(' OR ')})`);
    }
  }

  // Text search - exclude keywords
  if (filters.excludeKeywords?.length) {
    const excludeConditions = filters.excludeKeywords.map((keyword) => {
      const condition = `(title NOT ILIKE $${currentIndex} AND description NOT ILIKE $${currentIndex} AND skills NOT ILIKE $${currentIndex})`;
      params.push(`%${keyword}%`);
      currentIndex++;
      return condition;
    });

    if (excludeConditions.length > 0) {
      conditions.push(`(${excludeConditions.join(' AND ')})`);
    }
  }

  return currentIndex;
}

/**
 * Add location and date filter conditions
 */
function addLocationDateFilters(
  conditions: string[],
  params: unknown[],
  filters: JobFilters,
  paramIndex: number
): number {
  let currentIndex = paramIndex;

  // Location filters
  if (filters.countries?.length) {
    conditions.push(`workplace_country = ANY($${currentIndex})`);
    params.push(filters.countries);
    currentIndex++;
  }

  if (filters.cities?.length) {
    conditions.push(`workplace_city = ANY($${currentIndex})`);
    params.push(filters.cities);
    currentIndex++;
  }

  // Date filters
  if (filters.postedAfter) {
    conditions.push(`posted_date >= $${currentIndex}`);
    params.push(filters.postedAfter);
    currentIndex++;
  }

  if (filters.validUntil) {
    conditions.push(`valid_through <= $${currentIndex}`);
    params.push(filters.validUntil);
    currentIndex++;
  }

  return currentIndex;
}

/**
 * Build SQL WHERE clause from job filters
 */
export function buildJobFilterQuery(filters: JobFilters): {
  whereClause: string;
  params: unknown[];
} {
  const conditions: string[] = [];
  const params: unknown[] = [];
  let paramIndex = 1;

  // Apply all filter categories
  paramIndex = addJobCategoryFilters(conditions, params, filters, paramIndex);
  paramIndex = addSalaryFilters(conditions, params, filters, paramIndex);
  paramIndex = addTextSearchFilters(conditions, params, filters, paramIndex);
  paramIndex = addLocationDateFilters(conditions, params, filters, paramIndex);

  const whereClause = conditions.length > 0 ? conditions.join(' AND ') : '1=1';

  return { whereClause, params };
}

/**
 * Calculate salary-based score component
 */
function calculateSalaryScore(job: JobData): number {
  if (!job.salary_min || job.salary_currency !== 'USD') {
    return 0;
  }

  if (job.salary_min >= 200_000) {
    return 4;
  }
  if (job.salary_min >= 150_000) {
    return 3;
  }
  if (job.salary_min >= 100_000) {
    return 2;
  }
  if (job.salary_min >= 80_000) {
    return 1;
  }
  return 0;
}

/**
 * Calculate benefits-based score component
 */
function calculateBenefitsScore(job: JobData): number {
  let score = 0;
  const benefitsText = (job.benefits || '').toLowerCase();

  if (benefitsText.includes('equity') || benefitsText.includes('stock')) {
    score += 2;
  }
  if (benefitsText.includes('unlimited') && benefitsText.includes('pto')) {
    score += 1;
  }
  if (benefitsText.includes('learning') && benefitsText.includes('budget')) {
    score += 1;
  }

  return score;
}

/**
 * Calculate remote work score component
 */
function calculateRemoteScore(job: JobData): number {
  if (job.workplace_type === 'Remote') {
    return 2;
  }
  if (job.workplace_type === 'Hybrid') {
    return 1;
  }
  return 0;
}

/**
 * Calculate attractiveness score based on salary and benefits
 */
function calculateAttractivenessScore(job: JobData): number {
  const salaryScore = calculateSalaryScore(job);
  const benefitsScore = calculateBenefitsScore(job);
  const remoteScore = calculateRemoteScore(job);

  return salaryScore + benefitsScore + remoteScore;
}

/**
 * Calculate urgency score based on keywords and posting date
 */
function calculateUrgencyScore(job: JobData): number {
  let score = 0;

  // Urgency keywords
  const jobText = `${job.title} ${job.description}`.toLowerCase();
  if (
    jobText.includes('urgent') ||
    jobText.includes('asap') ||
    jobText.includes('immediate')
  ) {
    score += 3;
  }
  if (jobText.includes('soon') || jobText.includes('quickly')) {
    score += 2;
  }

  // Posted date urgency
  if (job.posted_date) {
    const daysSincePosted =
      (Date.now() - new Date(job.posted_date).getTime()) /
      (1000 * 60 * 60 * 24);
    if (daysSincePosted <= 1) {
      score += 2;
    } else if (daysSincePosted <= 7) {
      score += 1;
    }
  }

  return score;
}

/**
 * Determine complexity level from career level and salary
 */
function determineComplexityLevel(
  job: JobData
): 'entry' | 'mid' | 'senior' | 'executive' {
  // Check career level first
  if (
    job.career_level?.some((level) =>
      ['Director', 'VP', 'C-level'].includes(level)
    )
  ) {
    return 'executive';
  }

  if (
    job.career_level?.some((level) =>
      ['Staff', 'Principal', 'Lead', 'Senior'].includes(level)
    )
  ) {
    return 'senior';
  }

  if (
    job.career_level?.some((level) =>
      ['Junior', 'Entry level', 'Graduate'].includes(level)
    )
  ) {
    return 'entry';
  }

  // Fallback to salary if career level is not clear
  if (job.salary_min && job.salary_currency === 'USD') {
    if (job.salary_min >= 200_000) {
      return 'executive';
    }
    if (job.salary_min >= 150_000) {
      return 'senior';
    }
    if (job.salary_min >= 80_000) {
      return 'mid';
    }
    return 'entry';
  }

  // Default to mid if no clear indicators
  return 'mid';
}

/**
 * Get attractiveness level from score
 */
function getAttractivenessLevel(score: number): 'high' | 'medium' | 'low' {
  if (score >= 6) {
    return 'high';
  }
  if (score >= 3) {
    return 'medium';
  }
  return 'low';
}

/**
 * Get urgency level from score
 */
function getUrgencyLevel(score: number): 'high' | 'medium' | 'low' {
  if (score >= 4) {
    return 'high';
  }
  if (score >= 2) {
    return 'medium';
  }
  return 'low';
}

/**
 * Extract simple categorization tags (non-redundant)
 *
 * Only extracts data NOT already in normalized fields
 */
export function extractJobCategories(job: JobData): {
  attractiveness: 'high' | 'medium' | 'low';
  urgency: 'high' | 'medium' | 'low';
  complexity: 'entry' | 'mid' | 'senior' | 'executive';
} {
  const attractivenessScore = calculateAttractivenessScore(job);
  const urgencyScore = calculateUrgencyScore(job);
  const complexity = determineComplexityLevel(job);

  return {
    attractiveness: getAttractivenessLevel(attractivenessScore),
    urgency: getUrgencyLevel(urgencyScore),
    complexity,
  };
}

/**
 * Main tagging function - stores only derived categories
 */
export function tagJob(job: JobData): { job: JobData; tags: string[] } {
  try {
    // Extract non-redundant categories
    const categories = extractJobCategories(job);

    // Create simple tag array with only derived data
    const tags = [
      `attractiveness:${categories.attractiveness}`,
      `urgency:${categories.urgency}`,
      `complexity:${categories.complexity}`,
    ];

    logger.info(
      `Tagged job "${job.title}" with ${tags.length} derived categories`
    );

    return {
      job,
      tags,
    };
  } catch (error) {
    logger.error('Error tagging job:', error);
    return {
      job,
      tags: [],
    };
  }
}

/**
 * Example filter configurations for different job boards
 */
export const EXAMPLE_FILTERS = {
  reactJobs: {
    includeKeywords: ['React', 'React.js', 'ReactJS'],
    excludeKeywords: ['PHP', 'WordPress', 'jQuery'],
    careerLevels: ['Senior', 'Lead'],
    workplaceTypes: ['Remote', 'Hybrid'],
    salaryMin: 100_000,
    salaryCurrency: 'USD',
  },

  aiJobs: {
    includeKeywords: [
      'AI',
      'Machine Learning',
      'ML',
      'Deep Learning',
      'TensorFlow',
      'PyTorch',
    ],
    excludeKeywords: ['WordPress', 'PHP'],
    careerLevels: ['Senior', 'Lead', 'Staff'],
    salaryMin: 120_000,
    salaryCurrency: 'USD',
  },

  remoteMarketing: {
    departments: ['Marketing', 'Growth', 'Product Marketing'],
    workplaceTypes: ['Remote'],
    excludeKeywords: ['Developer', 'Engineer', 'Programming'],
    careerLevels: ['Senior', 'Lead', 'Manager'],
  },

  fintechSenior: {
    industries: ['FinTech', 'Financial Services', 'Banking'],
    includeKeywords: ['Senior', 'Lead', 'Principal'],
    excludeKeywords: ['Junior', 'Entry', 'Intern'],
    salaryMin: 130_000,
    salaryCurrency: 'USD',
  },
} as const;
