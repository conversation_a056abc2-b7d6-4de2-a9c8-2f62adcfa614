'use client';

import { MultiSelectUrlFilter } from '@/components/common/MultiSelectUrlFilter';
import { LANGUAGES } from '@/lib/data/languages';

interface LanguagesFilterUrlProps {
  disabled?: boolean;
}

/**
 * Languages filter component with URL state
 * Now uses the generic MultiSelectUrlFilter component
 */
export function LanguagesFilterUrl({
  disabled = false,
}: LanguagesFilterUrlProps) {
  return (
    <MultiSelectUrlFilter
      label="Languages"
      paramName="languages"
      options={LANGUAGES}
      disabled={disabled}
      searchable={true}
      maxHeight="max-h-48"
      clearable={true}
      showCount={true}
    />
  );
}
