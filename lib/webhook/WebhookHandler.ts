import { type NextRequest, NextResponse } from 'next/server';
import { type ZodSchema, z } from 'zod';
import { createHealthCheckResponse } from '@/lib/api-utils';
import { insertJobsToDatabase } from '@/lib/database-utils';
import { logger } from '@/lib/logger';
import { getApifyService } from '@/lib/services/ApifyService';
import { triggerJobProcessing } from '@/lib/workflow-client';

/**
 * Base Webhook Handler Class
 *
 * Provides a standardized pattern for webhook handlers that process
 * job data from external sources like Apify actors. Handles common
 * patterns like validation, data fetching, processing, and response.
 */

// Standard Apify webhook schema
export const BaseApifyWebhookSchema = z
  .object({
    eventType: z.string(),
    eventData: z.object({
      actorRunId: z.string(),
    }),
    resource: z
      .object({
        id: z.string(),
        status: z.string(),
        defaultDatasetId: z.string().optional(),
        datasetId: z.string().optional(),
        actorId: z.string().optional(),
        actId: z.string().optional(),
      })
      .passthrough(),
  })
  .passthrough();

export type ApifyWebhook = z.infer<typeof BaseApifyWebhookSchema>;

// Job processing result interface
export type JobProcessingResult = {
  jobsToInsert: Record<string, unknown>[];
  skippedCount: number;
  validationErrors?: string[];
};

// Webhook response interface
export type WebhookResponse = {
  success: boolean;
  message: string;
  runId?: string;
  jobsReceived: number;
  jobsSaved: number;
  jobsSkipped: number;
  processingTime: string;
  workflowTriggered?: boolean;
  workflowMessageId?: string;
  error?: string;
};

// Health check configuration
export type HealthCheckConfig = {
  service: string;
  architecture?: string;
  features?: string[];
  environment?: Record<string, unknown>;
};

// Abstract webhook handler configuration
export type WebhookHandlerConfig<TJob = unknown, TMapped = unknown> = {
  serviceName: string;
  sourceType: string;
  sourceName: string;
  webhookSchema?: ZodSchema<any>;
  requireApifyHeader?: boolean;
  maxJobsToProcess?: number;
  healthCheck: HealthCheckConfig;

  // Abstract methods that must be implemented
  fetchJobs: (datasetId: string) => Promise<TJob[]>;
  mapJobFields: (job: TJob) => TMapped;
  validateJob: (
    mappedJob: TMapped,
    source: string
  ) => {
    isValid: boolean;
    externalId?: string;
    missingFields: string[];
  };
};

/**
 * Abstract base class for webhook handlers
 */
export abstract class WebhookHandler<TJob = unknown, TMapped = unknown> {
  protected config: WebhookHandlerConfig<TJob, TMapped>;
  protected apifyService = getApifyService();

  constructor(config: WebhookHandlerConfig<TJob, TMapped>) {
    this.config = config;
  }

  /**
   * Main POST handler for webhook requests
   */
  async handlePost(request: NextRequest): Promise<NextResponse> {
    const startTime = Date.now();

    try {
      logger.info(`🔔 ${this.config.serviceName} webhook received`);

      // Check for Apify webhook header if required
      if (this.config.requireApifyHeader) {
        const apifyHeader = request.headers.get('X-Apify-Webhook');
        if (!apifyHeader) {
          logger.warn('Missing X-Apify-Webhook header');
        }
      }

      // Parse and validate webhook payload
      const body = await request.json();
      const webhookSchema = this.config.webhookSchema || BaseApifyWebhookSchema;
      const webhook = webhookSchema.parse(body);

      logger.info(`Processing ${this.config.serviceName} actor run`, {
        runId: webhook.eventData.actorRunId,
        datasetId:
          webhook.resource.defaultDatasetId || webhook.resource.datasetId,
        actorId: webhook.resource.id,
      });

      // Only process successful runs
      if (webhook.eventType !== 'ACTOR.RUN.SUCCEEDED') {
        return NextResponse.json({
          message: 'Ignoring non-success event',
          eventType: webhook.eventType,
        });
      }

      const runId = webhook.resource.id;
      const datasetId =
        webhook.resource.defaultDatasetId || webhook.resource.datasetId;

      if (!datasetId) {
        throw new Error('No dataset ID found in webhook payload');
      }

      // Fetch jobs from dataset
      const jobs = await this.config.fetchJobs(datasetId);

      if (!Array.isArray(jobs) || jobs.length === 0) {
        logger.info('No jobs found in dataset', { datasetId });
        return this.createResponse({
          success: true,
          message: 'No jobs to process',
          jobsReceived: 0,
          jobsSaved: 0,
          jobsSkipped: 0,
          processingTime: `${Date.now() - startTime}ms`,
        });
      }

      logger.info(
        `📥 Received ${jobs.length} jobs from ${this.config.serviceName}`
      );

      // Process and validate jobs
      const { jobsToInsert, skippedCount } = await this.processJobs(
        jobs,
        runId
      );

      // Insert jobs to database
      const savedCount = await insertJobsToDatabase(jobsToInsert);

      const processingTime = Date.now() - startTime;

      logger.info(`✅ ${this.config.serviceName} webhook completed`, {
        runId,
        jobsReceived: jobs.length,
        jobsSaved: savedCount,
        jobsSkipped: skippedCount,
        processingTime: `${processingTime}ms`,
      });

      // Track pipeline metrics
      logger.pipeline({
        step: 'STORED',
        source: this.config.sourceType,
        jobCount: savedCount,
        success: true,
        batchId: runId,
        duration: processingTime,
        metadata: {
          apifyRunId: runId,
          apifyDatasetId: datasetId,
          jobsSkipped: skippedCount,
        },
      });

      // Trigger workflow processing for saved jobs
      let workflowMessageId: string | undefined;
      if (savedCount > 0) {
        try {
          const maxJobs = this.config.maxJobsToProcess || 10;
          workflowMessageId = await triggerJobProcessing(
            Math.min(savedCount, maxJobs),
            this.config.sourceType
          );
          logger.info('✅ Triggered workflow processing', {
            workflowMessageId,
            jobCount: savedCount,
          });
        } catch (workflowError) {
          logger.error('❌ Failed to trigger workflow processing', {
            workflowError,
          });
          // Don't fail the webhook - jobs are saved, processing can be triggered manually
        }
      }

      return this.createResponse({
        success: true,
        message: 'Jobs saved successfully',
        runId,
        jobsReceived: jobs.length,
        jobsSaved: savedCount,
        jobsSkipped: skippedCount,
        processingTime: `${processingTime}ms`,
        workflowTriggered: !!workflowMessageId,
        workflowMessageId,
      });
    } catch (error) {
      logger.error(`${this.config.serviceName} webhook failed`, { error });

      // Always return 200 to Apify to prevent retries
      return this.createResponse(
        {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          message: 'Webhook acknowledged but processing failed',
          jobsReceived: 0,
          jobsSaved: 0,
          jobsSkipped: 0,
          processingTime: `${Date.now() - startTime}ms`,
        },
        200
      );
    }
  }

  /**
   * GET handler for health check endpoints
   */
  handleGet(): NextResponse {
    return createHealthCheckResponse(this.config.healthCheck);
  }

  /**
   * Process jobs: map, validate, and prepare for database insertion
   */
  protected async processJobs(
    jobs: TJob[],
    _runId: string
  ): Promise<JobProcessingResult> {
    const jobsToInsert: Record<string, unknown>[] = [];
    let skippedCount = 0;
    const validationErrors: string[] = [];

    for (const job of jobs) {
      try {
        const mappedJob = this.config.mapJobFields(job);
        const validation = this.config.validateJob(
          mappedJob,
          this.config.sourceType
        );

        if (!validation.isValid) {
          const errorMsg = `Missing fields: ${validation.missingFields.join(', ')}`;
          logger.warn(`Skipping invalid job: ${errorMsg}`, {
            jobId: (job as any).id,
            title: (job as any).title,
          });
          validationErrors.push(errorMsg);
          skippedCount++;
          continue;
        }

        const dbJob = {
          ...mappedJob,
          external_id: validation.externalId,
          raw_sourced_job_data: job, // Store complete original job structure
          source_type: this.config.sourceType,
          source_name: this.config.sourceName,
          processing_status: 'pending', // Key for database-as-queue
          sourced_at: new Date().toISOString(),
        };

        jobsToInsert.push(dbJob);
      } catch (processingError) {
        logger.error('Error processing individual job', {
          processingError,
          job: (job as any).id,
        });
        skippedCount++;
      }
    }

    return { jobsToInsert, skippedCount, validationErrors };
  }

  /**
   * Create standardized webhook response
   */
  protected createResponse(
    response: WebhookResponse,
    status = 200
  ): NextResponse {
    return NextResponse.json(response, { status });
  }
}

/**
 * Concrete implementation for Apify-based webhooks
 */
export class ApifyWebhookHandler<
  TJob = unknown,
  TMapped = unknown,
> extends WebhookHandler<TJob, TMapped> {
  constructor(config: WebhookHandlerConfig<TJob, TMapped>) {
    super({
      ...config,
      requireApifyHeader: config.requireApifyHeader ?? true,
      fetchJobs:
        config.fetchJobs ||
        ((datasetId: string) =>
          this.apifyService.fetchDataset<TJob>(datasetId, {
            format: 'json',
            clean: true,
          })),
    });
  }
}

/**
 * Create a standardized webhook route handler
 */
export function createWebhookRouteHandler<TJob = unknown, TMapped = unknown>(
  config: WebhookHandlerConfig<TJob, TMapped>
) {
  const handler = new ApifyWebhookHandler(config);

  return {
    async POST(request: NextRequest) {
      return handler.handlePost(request);
    },

    GET() {
      return handler.handleGet();
    },
  };
}
