/**
 * Webhook Schema Definitions
 *
 * Common and specific webhook schemas for different sources
 */

import { z } from 'zod';

// Base webhook schemas
export const BaseWebhookSchema = z.object({
  eventType: z.string(),
  timestamp: z.string().datetime().optional(),
  source: z.string().optional(),
});

// Enhanced Apify webhook schema with more event types
export const ApifyWebhookSchema = z
  .object({
    eventType: z.enum([
      'ACTOR.RUN.SUCCEEDED',
      'ACTOR.RUN.FAILED',
      'ACTOR.RUN.ABORTED',
      'ACTOR.RUN.TIMED_OUT',
      'ACTOR.RUN.CREATED',
      'ACTOR.RUN.STARTED',
    ]),
    eventData: z.object({
      actorId: z.string(),
      actorRunId: z.string(),
      actorTaskId: z.string().optional(),
      userId: z.string(),
    }),
    resource: z
      .object({
        id: z.string(),
        actId: z.string().optional(),
        actorId: z.string().optional(),
        status: z.enum([
          'READY',
          'RUNNING',
          'SUCCEEDED',
          'FAILED',
          'TIMED_OUT',
          'ABORTED',
        ]),
        statusMessage: z.string().optional(),
        defaultDatasetId: z.string().optional(),
        datasetId: z.string().optional(),
        defaultKeyValueStoreId: z.string().optional(),
        startedAt: z.string().datetime().optional(),
        finishedAt: z.string().datetime().optional(),
        exitCode: z.number().optional(),
        stats: z
          .object({
            inputBodyLen: z.number().optional(),
            restartCount: z.number().optional(),
            outputBodyLen: z.number().optional(),
            durationMillis: z.number().optional(),
            runTimeMillis: z.number().optional(),
            cpuMillis: z.number().optional(),
            memAvgBytes: z.number().optional(),
            memMaxBytes: z.number().optional(),
            memCurrentBytes: z.number().optional(),
            computeUnits: z.number().optional(),
          })
          .optional(),
      })
      .passthrough(),
    createdAt: z.string().datetime(),
  })
  .passthrough();

export type ApifyWebhook = z.infer<typeof ApifyWebhookSchema>;

// WeWorkRemotely specific job schema
export const WWRJobSchema = z
  .object({
    id: z.string(),
    title: z.string(),
    content: z.string(),
    source_type: z.string(),
    source_name: z.string(),
    raw_data: z.object({
      rss_item: z.object({
        title: z.string(),
        description: z.string(),
        link: z.string().url(),
        pubDate: z.string(),
        category: z.string(),
      }),
      source_url: z.string().url(),
      fetched_at: z.string(),
    }),
  })
  .passthrough();

export type WWRJob = z.infer<typeof WWRJobSchema>;

// JobDataAPI specific job schema
export const JobDataApiJobSchema = z
  .object({
    title: z.string().optional(),
    content: z.string().optional(),
    raw_data: z
      .object({
        original_job: z.record(z.string(), z.unknown()).optional(),
      })
      .optional(),
  })
  .passthrough();

export type JobDataApiJob = z.infer<typeof JobDataApiJobSchema>;

// Workable specific job schema
export const WorkableJobSchema = z
  .object({
    title: z.string().optional(),
    company: z.string().optional(),
    description: z.string().optional(),
    url: z.string().url().optional(),
    reference_number: z.string().optional(),
  })
  .passthrough();

export type WorkableJob = z.infer<typeof WorkableJobSchema>;

// Generic webhook validation utility
export function validateWebhookPayload<T>(
  schema: z.ZodSchema<T>,
  payload: unknown
): { success: true; data: T } | { success: false; error: z.ZodError } {
  const result = schema.safeParse(payload);

  if (result.success) {
    return { success: true, data: result.data };
  }

  return { success: false, error: result.error };
}
