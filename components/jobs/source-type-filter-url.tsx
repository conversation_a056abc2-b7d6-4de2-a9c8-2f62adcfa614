'use client';

import { parseAsString, useQueryState } from 'nuqs';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

type SourceTypeFilterUrlProps = {
  disabled?: boolean;
};

// Source type options (matching the legacy implementation)
const sourceTypeOptions = [
  { value: 'all', label: 'All Sources' },
  { value: 'linkedin', label: 'LinkedIn' },
  { value: 'indeed', label: 'Indeed' },
  { value: 'glassdoor', label: 'Glassdoor' },
  { value: 'monster', label: 'Monster' },
  { value: 'ziprecruiter', label: 'ZipRecruiter' },
  { value: 'careerbuilder', label: 'CareerBuilder' },
  { value: 'dice', label: 'Dice' },
  { value: 'stackoverflow', label: 'Stack Overflow' },
  { value: 'github', label: 'GitHub Jobs' },
  { value: 'angellist', label: 'AngelList' },
  { value: 'remote', label: 'Remote.co' },
  { value: 'weworkremotely', label: 'We Work Remotely' },
  { value: 'flexjobs', label: 'FlexJobs' },
  { value: 'upwork', label: 'Upwork' },
  { value: 'freelancer', label: 'Freelancer' },
  { value: 'other', label: 'Other' },
];

/**
 * Source Type filter component with URL state
 * Follows Pattern 1 from nuqs-todo.md: Single Source of Truth
 *
 * Key features:
 * - URL state is the only state (no local state)
 * - Direct updates to URL
 * - No infinite loops
 */
export function SourceTypeFilterUrl({
  disabled = false,
}: SourceTypeFilterUrlProps) {
  // URL state for source type filter
  const [sourceType, setSourceType] = useQueryState(
    'source',
    parseAsString.withDefault('')
  );

  const handleValueChange = (value: string) => {
    // Set to null to remove from URL when "all" is selected
    setSourceType(value === 'all' ? null : value);
  };

  return (
    <div>
      <Label>Source Type</Label>
      <Select
        disabled={disabled}
        onValueChange={handleValueChange}
        value={sourceType || 'all'}
      >
        <SelectTrigger>
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          {sourceTypeOptions.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
