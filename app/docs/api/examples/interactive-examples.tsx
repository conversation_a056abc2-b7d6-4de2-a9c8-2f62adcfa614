'use client';

import { <PERSON><PERSON>, ExternalLink, Play } from 'lucide-react';
import { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Tabs, Tabs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import type { JobData } from '@/lib/types';
import {
  REAL_API_RESPONSES,
  REAL_JOB_EXAMPLES,
  REAL_WEBHOOK_EXAMPLES,
} from '../../../../tests/fixtures/real-data-examples';

export function InteractiveExamples() {
  const [copiedId, setCopiedId] = useState<string | null>(null);

  const copyToClipboard = async (text: string, id: string) => {
    await navigator.clipboard.writeText(text);
    setCopiedId(id);
    setTimeout(() => setCopiedId(null), 2000);
  };

  return (
    <div className="space-y-8">
      {/* Job Data Examples */}
      <section>
        <h2 className="mb-4 font-semibold text-2xl">Real Job Data Examples</h2>
        <p className="mb-6 text-muted-foreground">
          These are examples of actual job postings processed by Bordfeed, with
          sensitive data sanitized.
        </p>

        <Tabs className="w-full" defaultValue="senior-engineer">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="senior-engineer">Senior Engineer</TabsTrigger>
            <TabsTrigger value="frontend-dev">Frontend Developer</TabsTrigger>
            <TabsTrigger value="data-engineer">Data Engineer</TabsTrigger>
          </TabsList>

          <TabsContent value="senior-engineer">
            <JobExampleCard
              copiedId={copiedId}
              description="Full-time hybrid position with competitive salary and benefits"
              id="senior-engineer"
              job={REAL_JOB_EXAMPLES.seniorSoftwareEngineer}
              onCopy={copyToClipboard}
              title="Senior Software Engineer at TechCorp"
            />
          </TabsContent>

          <TabsContent value="frontend-dev">
            <JobExampleCard
              copiedId={copiedId}
              description="Remote React specialist position with global timezone flexibility"
              id="frontend-dev"
              job={REAL_JOB_EXAMPLES.remoteFrontendDeveloper}
              onCopy={copyToClipboard}
              title="Remote Frontend Developer at StartupCorp"
            />
          </TabsContent>

          <TabsContent value="data-engineer">
            <JobExampleCard
              copiedId={copiedId}
              description="Cloud infrastructure role with hybrid work arrangement"
              id="data-engineer"
              job={REAL_JOB_EXAMPLES.dataEngineer}
              onCopy={copyToClipboard}
              title="Data Engineer at DataCorp"
            />
          </TabsContent>
        </Tabs>
      </section>

      {/* API Response Examples */}
      <section>
        <h2 className="mb-4 font-semibold text-2xl">API Response Examples</h2>
        <p className="mb-6 text-muted-foreground">
          Real API responses from Bordfeed endpoints showing successful
          processing and error scenarios.
        </p>

        <Tabs className="w-full" defaultValue="pipeline-success">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="pipeline-success">Pipeline Success</TabsTrigger>
            <TabsTrigger value="health-checks">Health Checks</TabsTrigger>
            <TabsTrigger value="error-responses">Error Responses</TabsTrigger>
          </TabsList>

          <TabsContent value="pipeline-success">
            <APIResponseCard
              copiedId={copiedId}
              description="Successful batch processing of 3 job postings with metrics"
              endpoint="POST /api/pipeline-ingest"
              id="pipeline-success"
              onCopy={copyToClipboard}
              response={REAL_API_RESPONSES.pipelineSuccess}
              status={200}
              title="Pipeline Processing Success"
            />
          </TabsContent>

          <TabsContent value="health-checks">
            <div className="space-y-4">
              <APIResponseCard
                copiedId={copiedId}
                description="Overall system status with integration health"
                endpoint="GET /api/health"
                id="health-system"
                onCopy={copyToClipboard}
                response={REAL_API_RESPONSES.healthResponses.system}
                status={200}
                title="System Health Check"
              />
              <APIResponseCard
                copiedId={copiedId}
                description="Pipeline service status with usage metrics"
                endpoint="GET /api/pipeline-ingest"
                id="health-pipeline"
                onCopy={copyToClipboard}
                response={REAL_API_RESPONSES.healthResponses.pipeline}
                status={200}
                title="Pipeline Health Check"
              />
            </div>
          </TabsContent>

          <TabsContent value="error-responses">
            <div className="space-y-4">
              <APIResponseCard
                copiedId={copiedId}
                description="Missing required field in request payload"
                endpoint="POST /api/pipeline-ingest"
                id="error-validation"
                onCopy={copyToClipboard}
                response={REAL_API_RESPONSES.errorResponses.validationError}
                status={400}
                title="Validation Error"
              />
              <APIResponseCard
                copiedId={copiedId}
                description="Invalid QStash signature"
                endpoint="POST /api/webhook-callbacks"
                id="error-auth"
                onCopy={copyToClipboard}
                response={REAL_API_RESPONSES.errorResponses.authenticationError}
                status={401}
                title="Authentication Error"
              />
            </div>
          </TabsContent>
        </Tabs>
      </section>

      {/* Webhook Examples */}
      <section>
        <h2 className="mb-4 font-semibold text-2xl">
          Webhook Payload Examples
        </h2>
        <p className="mb-6 text-muted-foreground">
          Real webhook payloads from various job sources and QStash callbacks.
        </p>

        <Tabs className="w-full" defaultValue="qstash">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="qstash">QStash Callback</TabsTrigger>
            <TabsTrigger value="workable">Workable</TabsTrigger>
            <TabsTrigger value="wwr">WeWorkRemotely</TabsTrigger>
            <TabsTrigger value="jobdata">JobData API</TabsTrigger>
          </TabsList>

          <TabsContent value="qstash">
            <WebhookExampleCard
              copiedId={copiedId}
              description="Successful pipeline processing callback from QStash"
              id="webhook-qstash"
              onCopy={copyToClipboard}
              source="QStash"
              title="QStash Callback Webhook"
              webhook={REAL_WEBHOOK_EXAMPLES.qstashCallback}
            />
          </TabsContent>

          <TabsContent value="workable">
            <WebhookExampleCard
              copiedId={copiedId}
              description="New job posting notification from Workable ATS"
              id="webhook-workable"
              onCopy={copyToClipboard}
              source="Workable"
              title="Workable Job Published"
              webhook={REAL_WEBHOOK_EXAMPLES.workableWebhook}
            />
          </TabsContent>

          <TabsContent value="wwr">
            <WebhookExampleCard
              copiedId={copiedId}
              description="Remote job posting from WeWorkRemotely RSS feed"
              id="webhook-wwr"
              onCopy={copyToClipboard}
              source="WeWorkRemotely"
              title="WeWorkRemotely Job Feed"
              webhook={REAL_WEBHOOK_EXAMPLES.wwrWebhook}
            />
          </TabsContent>

          <TabsContent value="jobdata">
            <WebhookExampleCard
              copiedId={copiedId}
              description="Job posting data from JobData API service"
              id="webhook-jobdata"
              onCopy={copyToClipboard}
              source="JobData API"
              title="JobData API Webhook"
              webhook={REAL_WEBHOOK_EXAMPLES.jobdataWebhook}
            />
          </TabsContent>
        </Tabs>
      </section>

      {/* Try It Out Section */}
      <section>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Play className="h-5 w-5" />
              Try It Out
            </CardTitle>
            <CardDescription>
              Test these examples with the interactive API playground
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4">
              <Button asChild>
                <a className="flex items-center gap-2" href="/docs/api">
                  <ExternalLink className="h-4 w-4" />
                  Open API Playground
                </a>
              </Button>
              <Button asChild variant="outline">
                <a
                  className="flex items-center gap-2"
                  href="/docs/api/openapi.json"
                >
                  <ExternalLink className="h-4 w-4" />
                  View OpenAPI Spec
                </a>
              </Button>
            </div>
          </CardContent>
        </Card>
      </section>
    </div>
  );
}

type JobExampleCardProps = {
  job: JobData;
  title: string;
  description: string;
  onCopy: (text: string, id: string) => void;
  copiedId: string | null;
  id: string;
};

function JobExampleCard({
  job,
  title,
  description,
  onCopy,
  copiedId,
  id,
}: JobExampleCardProps) {
  return (
    <Card>
      <CardHeader>
        <div className="flex items-start justify-between">
          <div>
            <CardTitle>{title}</CardTitle>
            <CardDescription>{description}</CardDescription>
          </div>
          <Button
            className="flex items-center gap-2"
            onClick={() => onCopy(JSON.stringify(job, null, 2), id)}
            size="sm"
            variant="outline"
          >
            <Copy className="h-4 w-4" />
            {copiedId === id ? 'Copied!' : 'Copy JSON'}
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex flex-wrap gap-2">
            <Badge variant="secondary">{job.type}</Badge>
            <Badge variant="secondary">{job.workplace_type}</Badge>
            <Badge variant="secondary">
              {job.salary_currency} {job.salary_min?.toLocaleString()} -{' '}
              {job.salary_max?.toLocaleString()}
            </Badge>
            {job.featured && <Badge variant="default">Featured</Badge>}
          </div>

          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <strong>Company:</strong> {job.company}
            </div>
            <div>
              <strong>Location:</strong>{' '}
              {job.workplace_city || job.remote_region}
            </div>
            <div>
              <strong>Department:</strong> {job.department}
            </div>
            <div>
              <strong>Career Level:</strong>{' '}
              {Array.isArray(job.career_level)
                ? job.career_level.join(', ')
                : job.career_level || 'Not specified'}
            </div>
          </div>

          <details className="mt-4">
            <summary className="cursor-pointer font-medium">
              View Full JSON
            </summary>
            <pre className="mt-2 max-h-96 overflow-auto rounded-lg bg-muted p-4 text-xs">
              {JSON.stringify(job, null, 2)}
            </pre>
          </details>
        </div>
      </CardContent>
    </Card>
  );
}

type APIResponseCardProps = {
  response: Record<string, unknown>;
  title: string;
  description: string;
  endpoint: string;
  status: number;
  onCopy: (text: string, id: string) => void;
  copiedId: string | null;
  id: string;
};

function APIResponseCard({
  response,
  title,
  description,
  endpoint,
  status,
  onCopy,
  copiedId,
  id,
}: APIResponseCardProps) {
  let statusColor: 'destructive' | 'secondary' | 'default' | 'outline' =
    'default';
  if (status >= 400) {
    statusColor = 'destructive';
  } else if (status >= 300) {
    statusColor = 'secondary';
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-start justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              {title}
              <Badge variant={statusColor}>{status}</Badge>
            </CardTitle>
            <CardDescription>{description}</CardDescription>
            <code className="rounded bg-muted px-2 py-1 text-sm">
              {endpoint}
            </code>
          </div>
          <Button
            className="flex items-center gap-2"
            onClick={() => onCopy(JSON.stringify(response, null, 2), id)}
            size="sm"
            variant="outline"
          >
            <Copy className="h-4 w-4" />
            {copiedId === id ? 'Copied!' : 'Copy JSON'}
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <pre className="max-h-96 overflow-auto rounded-lg bg-muted p-4 text-xs">
          {JSON.stringify(response, null, 2)}
        </pre>
      </CardContent>
    </Card>
  );
}

type WebhookExampleCardProps = {
  webhook: Record<string, unknown>;
  title: string;
  description: string;
  source: string;
  onCopy: (text: string, id: string) => void;
  copiedId: string | null;
  id: string;
};

function WebhookExampleCard({
  webhook,
  title,
  description,
  source,
  onCopy,
  copiedId,
  id,
}: WebhookExampleCardProps) {
  return (
    <Card>
      <CardHeader>
        <div className="flex items-start justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              {title}
              <Badge variant="outline">{source}</Badge>
            </CardTitle>
            <CardDescription>{description}</CardDescription>
          </div>
          <Button
            className="flex items-center gap-2"
            onClick={() => onCopy(JSON.stringify(webhook, null, 2), id)}
            size="sm"
            variant="outline"
          >
            <Copy className="h-4 w-4" />
            {copiedId === id ? 'Copied!' : 'Copy JSON'}
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <pre className="max-h-96 overflow-auto rounded-lg bg-muted p-4 text-xs">
          {JSON.stringify(webhook, null, 2)}
        </pre>
      </CardContent>
    </Card>
  );
}
