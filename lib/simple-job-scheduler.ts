// Simplified Job Scheduler for MVP
// No complex scoring, no views, just the essentials

import { recordJobBoardPosting } from "./database-utils";
import {
  getActiveJobBoards,
  getJobBoardById,
  type JobBoardConfig,
  updateJobBoardStats,
} from "./job-board-service";
import { getAirtablePat } from "./secrets-manager";
import { createServerClient } from "./supabase";
import type { JobData } from "./types";
import { logger } from "./utils";

interface PostingResult {
  jobId: string;
  boardId: string;
  success: boolean;
  error?: string;
}

interface SchedulerResult {
  boardId: string;
  boardName: string;
  jobsPosted: number;
  errors: string[];
}

/**
 * Check if job matches career level filter
 */
function matchesCareerLevel(job: JobData, board: JobBoardConfig): boolean {
  if (!(board.filters.careerLevels?.length && job.career_level)) {
    return true;
  }

  const hasMatchingLevel = job.career_level.some((level: string) =>
    board.filters.careerLevels?.includes(level)
  );

  if (!hasMatchingLevel) {
    return false;
  }

  return true;
}

/**
 * Check if job matches workplace type filter
 */
function matchesWorkplaceType(job: JobData, board: JobBoardConfig): boolean {
  return !(
    board.filters.workplaceTypes?.length &&
    job.workplace_type &&
    !board.filters.workplaceTypes.includes(job.workplace_type)
  );
}

/**
 * Check if job matches salary filters
 */
function matchesSalaryFilters(job: JobData, board: JobBoardConfig): boolean {
  // Salary minimum filter
  if (
    board.filters.salaryMin &&
    job.salary_min &&
    job.salary_min < board.filters.salaryMin
  ) {
    return false;
  }

  // Salary maximum filter
  if (
    board.filters.salaryMax &&
    job.salary_max &&
    job.salary_max > board.filters.salaryMax
  ) {
    return false;
  }

  return true;
}

/**
 * Check if job matches keyword filters
 */
function matchesKeywordFilters(job: JobData, board: JobBoardConfig): boolean {
  // Include keywords check
  if (board.filters.includeKeywords?.length) {
    const jobText = `${job.title} ${job.description} ${
      job.skills || ""
    }`.toLowerCase();

    const hasKeyword = board.filters.includeKeywords.some((keyword: string) =>
      jobText.includes(keyword.toLowerCase())
    );

    if (!hasKeyword) {
      return false;
    }
  }

  // Exclude keywords check
  if (board.filters.excludeKeywords?.length) {
    const jobText = `${job.title} ${job.description} ${
      job.skills || ""
    }`.toLowerCase();

    const hasExcluded = board.filters.excludeKeywords.some((keyword: string) =>
      jobText.includes(keyword.toLowerCase())
    );

    if (hasExcluded) {
      return false;
    }
  }

  return true;
}

/**
 * Check if job matches all board filters
 */
function matchesBoardFilters(job: JobData, board: JobBoardConfig): boolean {
  return (
    matchesCareerLevel(job, board) &&
    matchesWorkplaceType(job, board) &&
    matchesSalaryFilters(job, board) &&
    matchesKeywordFilters(job, board)
  );
}

/**
 * Get eligible jobs for a board using SQL-based filtering
 */
async function getEligibleJobsWithSQL(board: JobBoardConfig, limit = 50) {
  const supabase = await createServerClient();

  // Get active jobs from database
  const { data: jobs, error } = await supabase
    .from("jobs")
    .select("*")
    .eq("status", "active")
    .limit(limit * 2); // Get more to account for filtering

  if (error) {
    throw new Error(`Database error: ${error.message}`);
  }

  if (!jobs) {
    return [];
  }

  // Apply board-specific filters
  const eligibleJobs = jobs.filter((job: JobData) =>
    matchesBoardFilters(job, board)
  );

  return eligibleJobs.slice(0, limit);
}

/**
 * Check daily limit - simple count query
 */
async function getTodaysPostCount(boardId: string): Promise<number> {
  const supabase = await createServerClient();
  const today = new Date().toISOString().split("T")[0];

  const { count } = await supabase
    .from("job_board_postings")
    .select("*", { count: "exact", head: true })
    .eq("board_id", boardId)
    .eq("status", "posted")
    .gte("posted_at", `${today}T00:00:00.000Z`)
    .lt("posted_at", `${today}T23:59:59.999Z`);

  return count || 0;
}

/**
 * Post job to Airtable
 */
async function postToAirtable(
  // biome-ignore lint/suspicious/noExplicitAny: Job data structure varies by source and needs dynamic handling
  job: any,
  board: JobBoardConfig
): Promise<string> {
  // Use secure PAT retrieval system
  const pat = await getAirtablePat(board.id);
  const baseId = board.airtable.baseId;
  const tableName = board.airtable.tableName;

  if (!(pat && baseId && tableName)) {
    throw new Error(`Airtable not configured for board ${board.id}`);
  }

  console.log("🔗 Making direct Airtable API call", {
    baseId,
    tableName,
    boardId: board.id,
    jobId: job.id,
    timestamp: new Date().toISOString(),
  });

  // Map job data to Airtable fields (simplified version)
  const fields: Record<string, unknown> = {};

  // Basic fields
  if (job.title) fields.title = job.title;
  if (job.company) fields.company = job.company;
  if (job.type) fields.type = job.type;
  if (job.description) fields.description = job.description;
  if (job.apply_url) fields.apply_url = job.apply_url;
  if (job.workplace_type) fields.workplace_type = job.workplace_type;
  if (job.workplace_country) fields.workplace_country = job.workplace_country;
  if (job.workplace_city) fields.workplace_city = job.workplace_city;
  if (job.remote_region) fields.remote_region = job.remote_region;
  if (job.salary_min) fields.salary_min = job.salary_min;
  if (job.salary_max) fields.salary_max = job.salary_max;
  if (job.salary_currency) fields.salary_currency = job.salary_currency;
  if (job.career_level) fields.career_level = job.career_level;
  if (job.posted_date) fields.posted_date = job.posted_date;
  if (job.job_source_name) fields.job_source_name = job.job_source_name;

  // Direct Airtable API call
  const response = await fetch(
    `https://api.airtable.com/v0/${baseId}/${encodeURIComponent(tableName)}`,
    {
      method: "POST",
      headers: {
        Authorization: `Bearer ${pat}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        records: [{ fields }],
      }),
    }
  );

  if (!response.ok) {
    const errorText = await response.text();
    console.error("🚨 Direct Airtable API call failed", {
      status: response.status,
      statusText: response.statusText,
      errorText,
      boardId: board.id,
      jobId: job.id,
      timestamp: new Date().toISOString(),
    });
    throw new Error(`Airtable API failed (${response.status}): ${errorText}`);
  }

  const result = await response.json();
  const recordId = result.records?.[0]?.id || "unknown";

  console.log("✅ Direct Airtable API call successful", {
    recordId,
    boardId: board.id,
    jobId: job.id,
    timestamp: new Date().toISOString(),
  });

  return recordId;
}

/**
 * Record posting in database
 */
async function recordPosting(
  jobId: string,
  boardId: string,
  success: boolean,
  recordId?: string,
  error?: string
) {
  // Use centralized utility for recording the posting
  await recordJobBoardPosting(
    jobId,
    boardId,
    success ? "posted" : "failed",
    recordId,
    error
  );

  if (success) {
    // Update job's posted_to_boards array
    const supabase = await createServerClient();
    const { data: job } = await supabase
      .from("jobs")
      .select("posted_to_boards")
      .eq("id", jobId)
      .single();

    const boards = job?.posted_to_boards || [];
    if (!boards.includes(boardId)) {
      boards.push(boardId);
      await supabase
        .from("jobs")
        .update({
          posted_to_boards: boards,
          last_posted_at: new Date().toISOString(),
        })
        .eq("id", jobId);
    }

    // Update job board stats
    await updateJobBoardStats(boardId, 1);
  }
}

/**
 * Process one board - simplified
 */
async function processBoard(board: JobBoardConfig): Promise<PostingResult[]> {
  const results: PostingResult[] = [];

  // Check today's count
  const postedToday = await getTodaysPostCount(board.id);
  const remaining = board.posting.dailyLimit - postedToday;

  if (remaining <= 0) {
    logger.info(
      `Daily limit reached for ${board.id} (${postedToday}/${board.posting.dailyLimit})`
    );
    return results;
  }

  // Get eligible jobs
  const jobs = await getEligibleJobsWithSQL(board, remaining);
  const toPost = jobs.slice(0, remaining);

  logger.info(`Posting ${toPost.length} jobs to ${board.id}`);

  // Post jobs
  for (const job of toPost) {
    try {
      // biome-ignore lint/nursery/noAwaitInLoop: Sequential posting required to respect rate limits
      const recordId = await postToAirtable(job, board);
      await recordPosting(job.id, board.id, true, recordId);

      results.push({
        jobId: job.id,
        boardId: board.id,
        success: true,
      });

      logger.info(`✅ Posted ${job.title} to ${board.id}`);
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : "Unknown error";
      await recordPosting(job.id, board.id, false, undefined, errorMsg);

      results.push({
        jobId: job.id,
        boardId: board.id,
        success: false,
        error: errorMsg,
      });

      logger.error(`❌ Failed to post ${job.title} to ${board.id}:`, error);
    }
  }

  return results;
}

/**
 * Main scheduler - simplified
 */
export async function runSimpleJobScheduler(
  boardId?: string
): Promise<SchedulerResult[]> {
  const startTime = Date.now();
  const results: SchedulerResult[] = [];
  const _supabase = await createServerClient();

  // Use console.log for production visibility - v2
  console.log("🚀 SIMPLE JOB SCHEDULER STARTED", {
    timestamp: new Date().toISOString(),
    targetBoardId: boardId || "ALL_ACTIVE_BOARDS",
    version: "v2-fixed",
  });
  logger.info("🚀 SIMPLE JOB SCHEDULER STARTED", {
    timestamp: new Date().toISOString(),
    targetBoardId: boardId || "ALL_ACTIVE_BOARDS",
  });

  try {
    // Get boards to process
    console.log("📋 FETCHING JOB BOARDS");
    logger.info("📋 FETCHING JOB BOARDS");
    const boards = boardId
      ? [await getJobBoardById(boardId)].filter(Boolean)
      : await getActiveJobBoards();

    console.log("📊 JOB BOARDS RETRIEVED", {
      totalBoards: boards.length,
      boardIds: boards.map((b) => b?.id).filter(Boolean),
      boardNames: boards.map((b) => b?.name).filter(Boolean),
    });
    logger.info("📊 JOB BOARDS RETRIEVED", {
      totalBoards: boards.length,
      boardIds: boards.map((b) => b?.id).filter(Boolean),
      boardNames: boards.map((b) => b?.name).filter(Boolean),
    });

    if (boards.length === 0) {
      logger.warn("⚠️ NO ACTIVE JOB BOARDS TO PROCESS");
      return results;
    }

    // Process boards in parallel
    const boardResults = await Promise.all(
      boards
        .filter((board): board is JobBoardConfig => board !== null)
        .map(async (board) => {
          const boardResult: SchedulerResult = {
            boardId: board.id,
            boardName: board.name,
            jobsPosted: 0,
            errors: [],
          };

          try {
            console.log(`🎯 PROCESSING BOARD: ${board.name} (${board.id})`, {
              boardId: board.id,
              boardName: board.name,
              dailyLimit: board.posting.dailyLimit,
              airtableBase: board.airtable.baseId,
              airtableTable: board.airtable.tableName,
            });
            logger.info(`🎯 PROCESSING BOARD: ${board.name} (${board.id})`, {
              boardId: board.id,
              boardName: board.name,
              dailyLimit: board.posting.dailyLimit,
              airtableBase: board.airtable.baseId,
              airtableTable: board.airtable.tableName,
            });

            // Get eligible jobs using new SQL approach
            const eligibleJobs = await getEligibleJobsWithSQL(
              board,
              board.posting.dailyLimit
            );

            console.log(`📝 ELIGIBLE JOBS FOUND`, {
              boardId: board.id,
              eligibleJobsCount: eligibleJobs.length,
              dailyLimit: board.posting.dailyLimit,
            });
            logger.info(`📝 ELIGIBLE JOBS FOUND`, {
              boardId: board.id,
              eligibleJobsCount: eligibleJobs.length,
              dailyLimit: board.posting.dailyLimit,
            });

            const postingResults = await processBoard(board);
            const successfulPosts = postingResults.filter((r) => r.success);
            const failedPosts = postingResults.filter((r) => !r.success);

            boardResult.jobsPosted = successfulPosts.length;
            boardResult.errors = failedPosts.map(
              (r) => r.error || "Unknown error"
            );

            logger.info(`✅ BOARD PROCESSING COMPLETE`, {
              boardId: board.id,
              boardName: board.name,
              jobsPosted: boardResult.jobsPosted,
              errorCount: boardResult.errors.length,
              errors: boardResult.errors,
              successfulJobIds: successfulPosts.map((p) => p.jobId),
              failedJobIds: failedPosts.map((p) => p.jobId),
            });
          } catch (error) {
            logger.error(`💥 BOARD PROCESSING ERROR`, {
              boardId: board.id,
              boardName: board.name,
              error: error instanceof Error ? error.message : "Unknown error",
              stack: error instanceof Error ? error.stack : undefined,
            });
            boardResult.jobsPosted = 0;
            boardResult.errors = ["Error processing board"];
          }

          return boardResult;
        })
    );

    results.push(...boardResults);

    const executionTime = Date.now() - startTime;
    const totalPosted = results.reduce((sum, r) => sum + r.jobsPosted, 0);
    const totalErrors = results.reduce((sum, r) => sum + r.errors.length, 0);

    console.log("🎉 SIMPLE JOB SCHEDULER COMPLETED", {
      executionTimeMs: executionTime,
      totalBoardsProcessed: results.length,
      totalJobsPosted: totalPosted,
      totalErrors,
      results,
      timestamp: new Date().toISOString(),
    });
    logger.info("🎉 SIMPLE JOB SCHEDULER COMPLETED", {
      executionTimeMs: executionTime,
      totalBoardsProcessed: results.length,
      totalJobsPosted: totalPosted,
      totalErrors,
      results,
      timestamp: new Date().toISOString(),
    });

    return results;
  } catch (error) {
    console.error("💥 Error running scheduler:", error);
    logger.error("💥 Error running scheduler:", error);
    return results;
  }
}
